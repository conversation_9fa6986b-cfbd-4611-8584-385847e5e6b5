/* ===== THEME-SPECIFIC STYLES ===== */

/* ===== LIGHT THEME (DEFAULT) ===== */
:root,
[data-theme="light"],
.theme-light {
  /* Light theme colors */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f3f4;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f5f5f5;
  --bg-active: #e9ecef;

  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #868e96;
  --text-muted: #adb5bd;
  --text-inverse: #ffffff;

  --border-light: #dee2e6;
  --border-medium: #ced4da;
  --border-dark: #adb5bd;
}

/* ===== DARK THEME ===== */
[data-theme="dark"],
.theme-dark {
  /* Dark theme colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --bg-card: #2d2d2d;
  --bg-input: #3a3a3a;
  --bg-hover: #404040;
  --bg-active: #4a4a4a;

  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #b3b3b3;
  --text-muted: #999999;
  --text-inverse: #000000;

  --border-light: #404040;
  --border-medium: #555555;
  --border-dark: #666666;
}

/* ===== BLUE THEME ===== */
[data-theme="blue"],
.theme-blue {
  --color-primary: #2196F3;
  --color-primary-dark: #1976D2;
  --color-primary-light: #64B5F6;

  --bg-primary: #f3f8ff;
  --bg-secondary: #ffffff;
  --bg-tertiary: #e3f2fd;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f5f9ff;
  --bg-active: #e1f5fe;
}

/* ===== GREEN THEME ===== */
[data-theme="green"],
.theme-green {
  --color-primary: #4CAF50;
  --color-primary-dark: #388E3C;
  --color-primary-light: #81C784;

  --bg-primary: #f3f8f3;
  --bg-secondary: #ffffff;
  --bg-tertiary: #e8f5e8;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f5f9f5;
  --bg-active: #e8f5e8;
}

/* ===== PURPLE THEME ===== */
[data-theme="purple"],
.theme-purple {
  --color-primary: #9C27B0;
  --color-primary-dark: #7B1FA2;
  --color-primary-light: #BA68C8;

  --bg-primary: #f8f3f8;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f3e5f5;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f9f5f9;
  --bg-active: #f3e5f5;
}

/* ===== ORANGE THEME ===== */
[data-theme="orange"],
.theme-orange {
  --color-primary: #FF9800;
  --color-primary-dark: #F57C00;
  --color-primary-light: #FFB74D;

  --bg-primary: #fff8f3;
  --bg-secondary: #ffffff;
  --bg-tertiary: #fff3e0;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #fff9f5;
  --bg-active: #fff3e0;
}

/* ===== TEAL THEME ===== */
[data-theme="teal"],
.theme-teal {
  --color-primary: #009688;
  --color-primary-dark: #00695C;
  --color-primary-light: #4DB6AC;

  --bg-primary: #f3f8f8;
  --bg-secondary: #ffffff;
  --bg-tertiary: #e0f2f1;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f5f9f9;
  --bg-active: #e0f2f1;
}

/* ===== THEME-SPECIFIC CONTENT FIXES ===== */

/* Fix main content background for all themes */
.main-content,
.page-content {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Fix dark theme content specifically */
[data-theme="dark"] .main-content,
[data-theme="dark"] .page-content {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card,
[data-theme="dark"] .stat-card,
[data-theme="dark"] .chart-card {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Dark theme specific component adjustments */
.theme-dark .neumorphic {
  background-color: var(--bg-secondary);
}

.theme-dark .neumorphic-inset {
  background-color: var(--bg-secondary);
}

.theme-dark .loading-spinner {
  border-color: var(--border-light);
  border-top-color: var(--color-primary);
}

.theme-dark .theme-toggle .light-icon {
  display: inline;
}

.theme-dark .theme-toggle .dark-icon {
  display: none;
}

/* Dark theme scrollbar adjustments */
.theme-dark ::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.theme-dark ::-webkit-scrollbar-thumb {
  background: var(--border-medium);
}

.theme-dark ::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Dark theme selection */
.theme-dark ::selection {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

.theme-dark ::-moz-selection {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

/* ===== NEUMORPHIC DESIGN SYSTEM ===== */

/* Base neumorphic styles */
.neumorphic {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-neumorphic-outset);
  border: 1px solid transparent;
  transition: var(--transition-all);
}

.neumorphic:hover {
  box-shadow: var(--shadow-neumorphic-hover);
}

.neumorphic-inset {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-neumorphic-inset);
  border: 1px solid transparent;
}

.neumorphic-pressed {
  box-shadow: var(--shadow-neumorphic-pressed) !important;
  transform: scale(0.98);
}

/* Neumorphic variants */
.neumorphic-flat {
  box-shadow: none;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
}

.neumorphic-raised {
  box-shadow: var(--shadow-neumorphic-card);
}

.neumorphic-floating {
  box-shadow: var(--shadow-neumorphic-modal);
}

/* ===== COMPONENT THEME ADJUSTMENTS ===== */

/* Header theme adjustments */
.app-header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* Sidebar theme adjustments */
.sidebar {
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* Card theme adjustments */
.card {
  background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
}

/* Button theme adjustments */
.btn-primary {
  background: linear-gradient(145deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  box-shadow: var(--shadow-neumorphic-outset);
}

.btn-primary:hover {
  background: linear-gradient(145deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  box-shadow: var(--shadow-neumorphic-hover);
}

.btn-primary:active {
  box-shadow: var(--shadow-neumorphic-pressed);
}

/* Form control theme adjustments */
.form-control {
  background: linear-gradient(145deg, var(--bg-input) 0%, var(--bg-secondary) 100%);
}

.form-control:focus {
  background: var(--bg-input);
  box-shadow: var(--shadow-neumorphic-inset), 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* Modal theme adjustments */
.modal-content {
  background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
}

.modal-backdrop {
  backdrop-filter: blur(8px);
}

/* Toast theme adjustments */
.toast {
  background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
  backdrop-filter: blur(10px);
}

/* Table theme adjustments */
.table-container {
  background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
}

.table th {
  background: linear-gradient(145deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

/* ===== THEME TRANSITION EFFECTS ===== */

/* Smooth theme transitions */
.theme-transitioning {
  transition: all var(--transition-slow) ease-in-out;
}

.theme-transitioning * {
  transition: background-color var(--transition-slow) ease-in-out,
              color var(--transition-slow) ease-in-out,
              border-color var(--transition-slow) ease-in-out,
              box-shadow var(--transition-slow) ease-in-out;
}

/* ===== ACCESSIBILITY THEME ADJUSTMENTS ===== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .neumorphic,
  .neumorphic-inset {
    border: 2px solid var(--text-primary);
    box-shadow: none;
  }
  
  .btn {
    border: 2px solid var(--text-primary);
    box-shadow: none;
  }
  
  .form-control {
    border: 2px solid var(--text-primary);
    box-shadow: none;
  }
  
  .card {
    border: 2px solid var(--text-primary);
    box-shadow: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .neumorphic,
  .neumorphic-inset,
  .btn,
  .form-control,
  .card,
  .modal,
  .toast {
    transition: none;
  }
  
  .theme-transitioning,
  .theme-transitioning * {
    transition: none;
  }
}

/* ===== CUSTOM THEME PROPERTIES ===== */

/* Theme-aware gradients */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.gradient-surface {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.gradient-card {
  background: linear-gradient(145deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
}

/* Theme-aware borders */
.border-theme {
  border: 1px solid var(--border-light);
}

.border-theme-medium {
  border: 1px solid var(--border-medium);
}

.border-theme-dark {
  border: 1px solid var(--border-dark);
}

/* Theme-aware text colors */
.text-theme-primary {
  color: var(--text-primary);
}

.text-theme-secondary {
  color: var(--text-secondary);
}

.text-theme-tertiary {
  color: var(--text-tertiary);
}

.text-theme-muted {
  color: var(--text-muted);
}

/* Theme-aware background colors */
.bg-theme-primary {
  background-color: var(--bg-primary);
}

.bg-theme-secondary {
  background-color: var(--bg-secondary);
}

.bg-theme-tertiary {
  background-color: var(--bg-tertiary);
}

.bg-theme-card {
  background-color: var(--bg-card);
}

/* ===== THEME-SPECIFIC ANIMATIONS ===== */

/* Neumorphic button press animation */
@keyframes neumorphic-press {
  0% {
    box-shadow: var(--shadow-neumorphic-outset);
    transform: scale(1);
  }
  50% {
    box-shadow: var(--shadow-neumorphic-pressed);
    transform: scale(0.98);
  }
  100% {
    box-shadow: var(--shadow-neumorphic-outset);
    transform: scale(1);
  }
}

.neumorphic-press-animation {
  animation: neumorphic-press 0.3s ease-in-out;
}

/* Theme switch animation */
@keyframes theme-switch {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

.theme-switch-animation {
  animation: theme-switch 0.5s ease-in-out;
}

/* ===== THEME UTILITIES ===== */

/* Hide elements in specific themes */
.light-only {
  display: block;
}

.dark-only {
  display: none;
}

.theme-dark .light-only {
  display: none;
}

.theme-dark .dark-only {
  display: block;
}

/* Theme-specific spacing */
.theme-padding {
  padding: var(--space-md);
}

.theme-margin {
  margin: var(--space-md);
}

.theme-gap {
  gap: var(--space-md);
}

/* Theme-specific typography */
.theme-heading {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.theme-body {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.theme-caption {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-normal);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* ===== THEME DEBUGGING ===== */

/* Debug mode - shows theme boundaries */
.theme-debug * {
  outline: 1px solid rgba(255, 0, 0, 0.3);
}

.theme-debug .neumorphic {
  outline: 2px solid rgba(0, 255, 0, 0.5);
}

.theme-debug .neumorphic-inset {
  outline: 2px solid rgba(0, 0, 255, 0.5);
}
