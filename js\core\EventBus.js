/**
 * EventBus - Centralized Event Management System
 * Provides pub/sub pattern for loose coupling between components
 */

class EventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
        this.maxListeners = 100;
        this.debug = false;
    }

    /**
     * Subscribe to an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @param {Object} options - Options object
     * @returns {Function} Unsubscribe function
     */
    on(event, callback, options = {}) {
        if (typeof event !== 'string') {
            throw new Error('Event name must be a string');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        // Initialize event array if it doesn't exist
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listeners = this.events.get(event);

        // Check max listeners limit
        if (listeners.length >= this.maxListeners) {
            console.warn(`EventBus: Maximum listeners (${this.maxListeners}) exceeded for event "${event}"`);
        }

        // Create listener object
        const listener = {
            callback,
            context: options.context || null,
            priority: options.priority || 0,
            once: false,
            id: this.generateId()
        };

        // Insert listener based on priority (higher priority first)
        const insertIndex = listeners.findIndex(l => l.priority < listener.priority);
        if (insertIndex === -1) {
            listeners.push(listener);
        } else {
            listeners.splice(insertIndex, 0, listener);
        }

        if (this.debug) {
            console.log(`EventBus: Subscribed to "${event}" (ID: ${listener.id})`);
        }

        // Return unsubscribe function
        return () => this.off(event, callback);
    }

    /**
     * Subscribe to an event once (auto-unsubscribe after first emission)
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @param {Object} options - Options object
     * @returns {Function} Unsubscribe function
     */
    once(event, callback, options = {}) {
        if (typeof event !== 'string') {
            throw new Error('Event name must be a string');
        }

        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        // Initialize once events array if it doesn't exist
        if (!this.onceEvents.has(event)) {
            this.onceEvents.set(event, []);
        }

        const listeners = this.onceEvents.get(event);

        // Create listener object
        const listener = {
            callback,
            context: options.context || null,
            priority: options.priority || 0,
            id: this.generateId()
        };

        // Insert listener based on priority
        const insertIndex = listeners.findIndex(l => l.priority < listener.priority);
        if (insertIndex === -1) {
            listeners.push(listener);
        } else {
            listeners.splice(insertIndex, 0, listener);
        }

        if (this.debug) {
            console.log(`EventBus: Subscribed once to "${event}" (ID: ${listener.id})`);
        }

        // Return unsubscribe function
        return () => this.offOnce(event, callback);
    }

    /**
     * Unsubscribe from an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function to remove
     * @returns {boolean} True if listener was removed
     */
    off(event, callback) {
        if (!this.events.has(event)) {
            return false;
        }

        const listeners = this.events.get(event);
        const index = listeners.findIndex(listener => listener.callback === callback);

        if (index !== -1) {
            const removed = listeners.splice(index, 1)[0];
            
            if (this.debug) {
                console.log(`EventBus: Unsubscribed from "${event}" (ID: ${removed.id})`);
            }

            // Clean up empty event arrays
            if (listeners.length === 0) {
                this.events.delete(event);
            }

            return true;
        }

        return false;
    }

    /**
     * Unsubscribe from a once event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function to remove
     * @returns {boolean} True if listener was removed
     */
    offOnce(event, callback) {
        if (!this.onceEvents.has(event)) {
            return false;
        }

        const listeners = this.onceEvents.get(event);
        const index = listeners.findIndex(listener => listener.callback === callback);

        if (index !== -1) {
            const removed = listeners.splice(index, 1)[0];
            
            if (this.debug) {
                console.log(`EventBus: Unsubscribed once from "${event}" (ID: ${removed.id})`);
            }

            // Clean up empty event arrays
            if (listeners.length === 0) {
                this.onceEvents.delete(event);
            }

            return true;
        }

        return false;
    }

    /**
     * Remove all listeners for an event
     * @param {string} event - Event name
     * @returns {number} Number of listeners removed
     */
    removeAllListeners(event) {
        let removed = 0;

        if (this.events.has(event)) {
            removed += this.events.get(event).length;
            this.events.delete(event);
        }

        if (this.onceEvents.has(event)) {
            removed += this.onceEvents.get(event).length;
            this.onceEvents.delete(event);
        }

        if (this.debug && removed > 0) {
            console.log(`EventBus: Removed ${removed} listeners for "${event}"`);
        }

        return removed;
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {*} data - Data to pass to listeners
     * @param {Object} options - Emission options
     * @returns {boolean} True if event had listeners
     */
    emit(event, data = null, options = {}) {
        if (typeof event !== 'string') {
            throw new Error('Event name must be a string');
        }

        const { async = false, stopOnError = false } = options;
        let hasListeners = false;

        if (this.debug) {
            console.log(`EventBus: Emitting "${event}"`, data);
        }

        // Handle regular listeners
        if (this.events.has(event)) {
            const listeners = [...this.events.get(event)]; // Copy to avoid modification during iteration
            hasListeners = true;

            if (async) {
                this.emitAsync(listeners, event, data, stopOnError);
            } else {
                this.emitSync(listeners, event, data, stopOnError);
            }
        }

        // Handle once listeners
        if (this.onceEvents.has(event)) {
            const listeners = [...this.onceEvents.get(event)]; // Copy array
            hasListeners = true;

            // Clear once listeners before calling them
            this.onceEvents.delete(event);

            if (async) {
                this.emitAsync(listeners, event, data, stopOnError);
            } else {
                this.emitSync(listeners, event, data, stopOnError);
            }
        }

        return hasListeners;
    }

    /**
     * Emit event synchronously
     * @private
     */
    emitSync(listeners, event, data, stopOnError) {
        for (const listener of listeners) {
            try {
                if (listener.context) {
                    listener.callback.call(listener.context, data, event);
                } else {
                    listener.callback(data, event);
                }
            } catch (error) {
                console.error(`EventBus: Error in listener for "${event}":`, error);
                
                if (stopOnError) {
                    throw error;
                }
            }
        }
    }

    /**
     * Emit event asynchronously
     * @private
     */
    async emitAsync(listeners, event, data, stopOnError) {
        for (const listener of listeners) {
            try {
                const result = listener.context 
                    ? listener.callback.call(listener.context, data, event)
                    : listener.callback(data, event);

                // Handle promises
                if (result && typeof result.then === 'function') {
                    await result;
                }
            } catch (error) {
                console.error(`EventBus: Error in async listener for "${event}":`, error);
                
                if (stopOnError) {
                    throw error;
                }
            }
        }
    }

    /**
     * Get list of events with listeners
     * @returns {Array} Array of event names
     */
    getEvents() {
        const regularEvents = Array.from(this.events.keys());
        const onceEvents = Array.from(this.onceEvents.keys());
        return [...new Set([...regularEvents, ...onceEvents])];
    }

    /**
     * Get number of listeners for an event
     * @param {string} event - Event name
     * @returns {number} Number of listeners
     */
    getListenerCount(event) {
        let count = 0;
        
        if (this.events.has(event)) {
            count += this.events.get(event).length;
        }
        
        if (this.onceEvents.has(event)) {
            count += this.onceEvents.get(event).length;
        }
        
        return count;
    }

    /**
     * Set maximum number of listeners per event
     * @param {number} max - Maximum number of listeners
     */
    setMaxListeners(max) {
        if (typeof max !== 'number' || max < 0) {
            throw new Error('Max listeners must be a non-negative number');
        }
        this.maxListeners = max;
    }

    /**
     * Enable or disable debug logging
     * @param {boolean} enabled - Whether to enable debug logging
     */
    setDebug(enabled) {
        this.debug = Boolean(enabled);
    }

    /**
     * Clear all listeners
     */
    clear() {
        const eventCount = this.events.size + this.onceEvents.size;
        this.events.clear();
        this.onceEvents.clear();
        
        if (this.debug) {
            console.log(`EventBus: Cleared all listeners (${eventCount} events)`);
        }
    }

    /**
     * Generate unique ID for listeners
     * @private
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Create a namespaced event bus
     * @param {string} namespace - Namespace prefix
     * @returns {Object} Namespaced event bus methods
     */
    namespace(namespace) {
        if (typeof namespace !== 'string') {
            throw new Error('Namespace must be a string');
        }

        const prefixEvent = (event) => `${namespace}:${event}`;

        return {
            on: (event, callback, options) => this.on(prefixEvent(event), callback, options),
            once: (event, callback, options) => this.once(prefixEvent(event), callback, options),
            off: (event, callback) => this.off(prefixEvent(event), callback),
            emit: (event, data, options) => this.emit(prefixEvent(event), data, options),
            removeAllListeners: (event) => this.removeAllListeners(prefixEvent(event)),
            getListenerCount: (event) => this.getListenerCount(prefixEvent(event))
        };
    }

    /**
     * Create a promise that resolves when an event is emitted
     * @param {string} event - Event name
     * @param {number} timeout - Optional timeout in milliseconds
     * @returns {Promise} Promise that resolves with event data
     */
    waitFor(event, timeout = null) {
        return new Promise((resolve, reject) => {
            let timeoutId = null;

            const cleanup = () => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
            };

            const unsubscribe = this.once(event, (data) => {
                cleanup();
                resolve(data);
            });

            if (timeout) {
                timeoutId = setTimeout(() => {
                    unsubscribe();
                    reject(new Error(`Timeout waiting for event "${event}"`));
                }, timeout);
            }
        });
    }
}

// Export the EventBus class
export { EventBus };
