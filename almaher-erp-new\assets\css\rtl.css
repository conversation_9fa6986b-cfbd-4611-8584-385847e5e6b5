/**
 * RTL Support - نظام الماهر للصيانة والخدمات
 * دعم اللغة العربية والكتابة من اليمين لليسار
 */

/* ========== الإعدادات الأساسية لـ RTL ========== */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] body {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
}

/* ========== تخطيط الشبكة ========== */
[dir="rtl"] .app {
  grid-template-areas: 
    "header header"
    "main sidebar";
  grid-template-columns: 1fr var(--sidebar-width);
}

/* ========== الرأس ========== */
[dir="rtl"] .header-content {
  flex-direction: row-reverse;
}

[dir="rtl"] .company-info {
  margin-left: var(--spacing-4);
  margin-right: 0;
  text-align: right;
}

[dir="rtl"] .header-actions {
  flex-direction: row-reverse;
}

/* ========== الشريط الجانبي ========== */
[dir="rtl"] .sidebar {
  border-right: 1px solid var(--border-color);
  border-left: none;
}

/* ========== التنقل ========== */
[dir="rtl"] .nav-link {
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .nav-icon {
  margin-right: var(--spacing-3);
  margin-left: 0;
}

[dir="rtl"] .nav-text {
  text-align: right;
}

[dir="rtl"] .nav-arrow {
  margin-right: auto;
  margin-left: 0;
}

[dir="rtl"] .nav-submenu {
  margin-right: var(--spacing-6);
  margin-left: 0;
}

/* ========== الأزرار ========== */
[dir="rtl"] .btn-icon {
  margin-right: var(--spacing-2);
  margin-left: 0;
}

[dir="rtl"] .btn {
  flex-direction: row-reverse;
}

/* ========== النماذج ========== */
[dir="rtl"] .form-label {
  text-align: right;
}

[dir="rtl"] .form-input {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .form-input::placeholder {
  text-align: right;
  direction: rtl;
}

/* ========== الجداول ========== */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

[dir="rtl"] .table th:first-child,
[dir="rtl"] .table td:first-child {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

[dir="rtl"] .table th:last-child,
[dir="rtl"] .table td:last-child {
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

/* ========== البطاقات ========== */
[dir="rtl"] .card-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .card-actions {
  flex-direction: row-reverse;
}

/* ========== الإحصائيات ========== */
[dir="rtl"] .stat-card {
  text-align: center;
}

/* ========== القوائم المنسدلة ========== */
[dir="rtl"] .theme-dropdown {
  right: 0;
  left: auto;
}

[dir="rtl"] .notification-dropdown {
  right: 0;
  left: auto;
}

[dir="rtl"] .theme-option {
  flex-direction: row-reverse;
}

[dir="rtl"] .theme-preview {
  margin-right: var(--spacing-2);
  margin-left: 0;
}

/* ========== التوست ========== */
[dir="rtl"] .toast-container {
  right: var(--spacing-6);
  left: auto;
}

[dir="rtl"] .toast {
  border-left: 4px solid var(--primary-color);
  border-right: none;
  transform: translateX(100%);
}

[dir="rtl"] .toast.show {
  transform: translateX(0);
}

[dir="rtl"] .toast-success {
  border-left-color: var(--success-color);
}

[dir="rtl"] .toast-warning {
  border-left-color: var(--warning-color);
}

[dir="rtl"] .toast-error {
  border-left-color: var(--error-color);
}

[dir="rtl"] .toast-info {
  border-left-color: var(--info-color);
}

/* ========== المودال ========== */
[dir="rtl"] .modal-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .modal-footer {
  justify-content: flex-end;
  flex-direction: row-reverse;
}

/* ========== الشارات ========== */
[dir="rtl"] .notification-badge {
  left: 0;
  right: auto;
  transform: translate(-50%, -50%);
}

/* ========== التحميل ========== */
[dir="rtl"] .loading-spinner {
  margin-right: var(--spacing-3);
  margin-left: 0;
}

/* ========== الأيقونات ========== */
[dir="rtl"] .icon-left {
  margin-right: var(--spacing-2);
  margin-left: 0;
}

[dir="rtl"] .icon-right {
  margin-left: var(--spacing-2);
  margin-right: 0;
}

/* ========== التمرير ========== */
[dir="rtl"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* ========== الاستجابة للشاشات الصغيرة ========== */
@media (max-width: 768px) {
  [dir="rtl"] .app {
    grid-template-areas: 
      "header"
      "main";
    grid-template-columns: 1fr;
  }
  
  [dir="rtl"] .sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: auto;
    transform: translateX(-100%);
  }
  
  [dir="rtl"] .sidebar.open {
    transform: translateX(0);
  }
  
  [dir="rtl"] .header-content {
    flex-direction: row;
  }
  
  [dir="rtl"] .company-info {
    margin-left: 0;
    margin-right: var(--spacing-4);
    text-align: center;
  }
}

/* ========== تخصيصات للنصوص العربية ========== */
[dir="rtl"] h1, 
[dir="rtl"] h2, 
[dir="rtl"] h3, 
[dir="rtl"] h4, 
[dir="rtl"] h5, 
[dir="rtl"] h6 {
  text-align: right;
  font-weight: var(--font-weight-bold);
}

[dir="rtl"] p {
  text-align: right;
  line-height: 1.8;
}

[dir="rtl"] .text-center {
  text-align: center !important;
}

[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

/* ========== تخصيصات للأرقام ========== */
[dir="rtl"] .number {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

[dir="rtl"] .currency {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

[dir="rtl"] .phone {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

[dir="rtl"] .email {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* ========== تخصيصات للتواريخ ========== */
[dir="rtl"] .date {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

[dir="rtl"] .time {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* ========== تحسينات للخطوط العربية ========== */
[dir="rtl"] {
  font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========== تخصيصات للطباعة ========== */
@media print {
  [dir="rtl"] .sidebar {
    display: none;
  }
  
  [dir="rtl"] .app {
    grid-template-areas: 
      "header"
      "main";
    grid-template-columns: 1fr;
  }
  
  [dir="rtl"] .no-print {
    display: none !important;
  }
}
