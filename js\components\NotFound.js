/**
 * NotFound Component - 404 Error Page
 * Displays when a route is not found
 */

class NotFound {
    constructor(params = {}) {
        this.params = params;
        this.container = null;
    }

    /**
     * Render the 404 page
     */
    async render() {
        this.container = document.createElement('div');
        this.container.className = 'not-found-container';
        
        this.container.innerHTML = `
            <div class="not-found-content">
                <div class="not-found-illustration">
                    <div class="error-code">404</div>
                    <div class="error-icon">🔍</div>
                </div>
                
                <div class="not-found-text">
                    <h1 class="not-found-title">الصفحة غير موجودة</h1>
                    <p class="not-found-description">
                        عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
                    </p>
                </div>
                
                <div class="not-found-actions">
                    <button class="btn btn-primary" id="go-home-btn">
                        <span>🏠</span>
                        العودة للرئيسية
                    </button>
                    <button class="btn btn-secondary" id="go-back-btn">
                        <span>↩️</span>
                        العودة للخلف
                    </button>
                </div>
                
                <div class="helpful-links">
                    <h3>روابط مفيدة:</h3>
                    <ul class="links-list">
                        <li><a href="#/customers" class="helpful-link">إدارة العملاء</a></li>
                        <li><a href="#/products" class="helpful-link">إدارة المنتجات</a></li>
                        <li><a href="#/invoices" class="helpful-link">إدارة الفواتير</a></li>
                        <li><a href="#/work-orders" class="helpful-link">أوامر العمل</a></li>
                        <li><a href="#/reports" class="helpful-link">التقارير</a></li>
                    </ul>
                </div>
            </div>
        `;

        // Set up event listeners
        this.setupEventListeners();

        return this.container;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Go home button
        const goHomeBtn = this.container.querySelector('#go-home-btn');
        if (goHomeBtn) {
            goHomeBtn.addEventListener('click', () => {
                window.router?.navigate('/');
            });
        }

        // Go back button
        const goBackBtn = this.container.querySelector('#go-back-btn');
        if (goBackBtn) {
            goBackBtn.addEventListener('click', () => {
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.router?.navigate('/');
                }
            });
        }

        // Helpful links
        const helpfulLinks = this.container.querySelectorAll('.helpful-link');
        helpfulLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                if (href && href.startsWith('#/')) {
                    const path = href.substring(1); // Remove the #
                    window.router?.navigate(path);
                }
            });
        });
    }

    /**
     * Initialize component
     */
    async init() {
        console.log('NotFound component initialized');
    }

    /**
     * Cleanup component
     */
    destroy() {
        if (this.container) {
            this.container.remove();
        }
    }
}

// Export the NotFound component
export default NotFound;
