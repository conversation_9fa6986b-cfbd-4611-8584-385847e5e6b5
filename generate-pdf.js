/**
 * PDF Generator Script for User Guide
 * يحول دليل المستخدم من HTML إلى PDF
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function generatePDF() {
    console.log('🚀 بدء إنشاء ملف PDF...');
    
    try {
        // تشغيل المتصفح
        const browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // تحديد مسار الملف
        const htmlPath = path.join(__dirname, 'user-guide.html');
        const fileUrl = `file://${htmlPath}`;
        
        console.log('📄 تحميل ملف HTML...');
        await page.goto(fileUrl, { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });
        
        // انتظار تحميل الخطوط والأنماط
        await page.waitForTimeout(2000);
        
        console.log('🎨 تطبيق إعدادات PDF...');
        
        // إنشاء PDF
        const pdf = await page.pdf({
            path: 'دليل-المستخدم-نظام-الماهر.pdf',
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20mm',
                right: '15mm',
                bottom: '20mm',
                left: '15mm'
            },
            displayHeaderFooter: true,
            headerTemplate: `
                <div style="font-size: 10px; width: 100%; text-align: center; color: #666; font-family: Arial;">
                    <span>دليل المستخدم - نظام الماهر للصيانة والخدمات ERP</span>
                </div>
            `,
            footerTemplate: `
                <div style="font-size: 10px; width: 100%; text-align: center; color: #666; font-family: Arial;">
                    <span>صفحة <span class="pageNumber"></span> من <span class="totalPages"></span></span>
                    <span style="margin-left: 20px;">© 2025 الماهر للصيانة والخدمات</span>
                </div>
            `,
            preferCSSPageSize: true
        });
        
        await browser.close();
        
        console.log('✅ تم إنشاء ملف PDF بنجاح!');
        console.log('📁 اسم الملف: دليل-المستخدم-نظام-الماهر.pdf');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء PDF:', error);
        process.exit(1);
    }
}

// تشغيل الدالة
generatePDF();
