/* ===== CONTACT & SUPPLIER MANAGEMENT STYLES ===== */

.supplier-management,
.contact-management {
  padding: var(--space-lg);
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== SUPPLIER/CONTACT CARDS ===== */
.suppliers-grid,
.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-lg);
}

.supplier-card,
.contact-card {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  cursor: pointer;
  overflow: hidden;
}

.supplier-card:hover,
.contact-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.supplier-name,
.contact-name {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.supplier-type,
.contact-type {
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-sm);
}

.contact-person {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-xs);
}

.contact-position {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  margin-bottom: var(--space-md);
}

/* ===== SUPPLIER/CONTACT TABLES ===== */
.suppliers-table-container,
.contacts-table-container {
  overflow-x: auto;
}

.suppliers-table,
.contacts-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.suppliers-table th,
.contacts-table th {
  background: var(--bg-tertiary);
  padding: var(--space-md);
  text-align: right;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  font-size: var(--font-size-sm);
}

.suppliers-table td,
.contacts-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.supplier-row,
.contact-row {
  transition: var(--transition-colors);
  cursor: pointer;
}

.supplier-row:hover,
.contact-row:hover {
  background: var(--bg-hover);
}

.supplier-id,
.contact-id {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.supplier-info .supplier-name,
.contact-info .contact-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.supplier-info .supplier-phone,
.contact-info .contact-phone {
  font-size: 11px;
  color: var(--text-muted);
}

.person-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.person-position {
  font-size: 11px;
  color: var(--text-muted);
}

.balance {
  font-weight: var(--font-weight-medium);
  color: var(--color-success);
}

/* ===== STATUS BADGES FOR SUPPLIERS/CONTACTS ===== */
.status-badge.active {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-badge.inactive {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.status-badge.blocked {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-badge.potential {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

/* ===== STATISTICS CARDS VARIATIONS ===== */
.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.stat-card.inactive .stat-icon {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.stat-card.potential .stat-icon {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: white;
}

.stat-card.balance .stat-icon {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.stat-card.companies .stat-icon {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
}

/* ===== SECTIONS SPECIFIC STYLES ===== */
.suppliers-section,
.contacts-section {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-neumorphic-outset);
  border: 1px solid var(--border-light);
}

.suppliers-container,
.contacts-container {
  margin-top: var(--space-lg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .suppliers-grid,
  .contacts-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .supplier-management,
  .contact-management {
    padding: var(--space-md);
  }
  
  .suppliers-grid,
  .contacts-grid {
    grid-template-columns: 1fr;
  }
  
  .suppliers-table-container,
  .contacts-table-container {
    font-size: var(--font-size-xs);
  }
  
  .suppliers-table th,
  .contacts-table th,
  .suppliers-table td,
  .contacts-table td {
    padding: var(--space-sm);
  }
}

/* ===== ADDITIONAL CONTACT SPECIFIC STYLES ===== */
.contact-card .detail-item {
  margin-bottom: var(--space-xs);
}

.contact-card .detail-item:last-child {
  margin-bottom: 0;
}

.contact-card .detail-text {
  word-break: break-word;
}

/* ===== SUPPLIER SPECIFIC STYLES ===== */
.supplier-card .balance {
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
}

.supplier-card .credit-limit {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* ===== FILTERS SECTION ENHANCEMENTS ===== */
.filters-section .filters-row {
  display: grid;
  grid-template-columns: 2fr repeat(4, 1fr);
  gap: var(--space-lg);
  align-items: end;
}

@media (max-width: 1024px) {
  .filters-section .filters-row {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-md);
  }
}

@media (max-width: 768px) {
  .filters-section .filters-row {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }
}
