/**
 * Chart Manager
 * إدارة الرسوم البيانية والتقارير المرئية
 */
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultColors = [
            '#007bff', '#28a745', '#ffc107', '#dc3545', 
            '#6f42c1', '#20c997', '#fd7e14', '#e83e8c'
        ];
        this.chartTypes = {
            line: 'خط',
            bar: 'أعمدة',
            pie: 'دائري',
            doughnut: 'حلقي',
            area: 'منطقة'
        };
    }

    /**
     * Initialize chart manager
     */
    async init() {
        console.log('📊 Initializing Chart Manager...');
        try {
            await this.loadChartLibrary();
            console.log('✅ Chart Manager initialized');
        } catch (error) {
            console.error('❌ Chart Manager initialization failed:', error);
        }
    }

    /**
     * Load chart library (using Chart.js CDN)
     */
    async loadChartLibrary() {
        if (window.Chart) {
            this.setupChartDefaults();
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
            script.onload = () => {
                console.log('✅ Chart.js loaded successfully');
                this.setupChartDefaults();
                resolve();
            };
            script.onerror = () => {
                console.error('❌ Failed to load Chart.js');
                reject(new Error('Failed to load Chart.js'));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Setup chart defaults
     */
    setupChartDefaults() {
        if (!window.Chart) return;

        // RTL support
        Chart.defaults.font.family = 'Cairo, Arial, sans-serif';
        Chart.defaults.plugins.legend.rtl = true;
        Chart.defaults.plugins.tooltip.rtl = true;
        Chart.defaults.plugins.tooltip.titleAlign = 'right';
        Chart.defaults.plugins.tooltip.bodyAlign = 'right';
        Chart.defaults.plugins.legend.align = 'end';
    }

    /**
     * Create a line chart
     */
    createLineChart(containerId, data, options = {}) {
        const canvas = this.createCanvas(containerId);
        if (!canvas) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    rtl: true,
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo, Arial, sans-serif',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    rtl: true,
                    titleAlign: 'right',
                    bodyAlign: 'right',
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#007bff',
                    borderWidth: 1
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, Arial, sans-serif'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo, Arial, sans-serif'
                        }
                    }
                }
            }
        };

        const config = {
            type: 'line',
            data: this.processChartData(data),
            options: { ...defaultOptions, ...options }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(containerId, chart);
        return chart;
    }

    /**
     * Create a bar chart
     */
    createBarChart(containerId, data, options = {}) {
        const canvas = this.createCanvas(containerId);
        if (!canvas) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    rtl: true,
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo, Arial, sans-serif',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    rtl: true,
                    titleAlign: 'right',
                    bodyAlign: 'right'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo, Arial, sans-serif'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo, Arial, sans-serif'
                        }
                    }
                }
            }
        };

        const config = {
            type: 'bar',
            data: this.processChartData(data),
            options: { ...defaultOptions, ...options }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(containerId, chart);
        return chart;
    }

    /**
     * Create a pie chart
     */
    createPieChart(containerId, data, options = {}) {
        const canvas = this.createCanvas(containerId);
        if (!canvas) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    rtl: true,
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo, Arial, sans-serif',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    rtl: true,
                    titleAlign: 'right',
                    bodyAlign: 'right',
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        };

        const config = {
            type: 'pie',
            data: this.processChartData(data),
            options: { ...defaultOptions, ...options }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(containerId, chart);
        return chart;
    }

    /**
     * Create a doughnut chart
     */
    createDoughnutChart(containerId, data, options = {}) {
        const canvas = this.createCanvas(containerId);
        if (!canvas) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'right',
                    rtl: true,
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo, Arial, sans-serif',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    rtl: true,
                    titleAlign: 'right',
                    bodyAlign: 'right'
                }
            }
        };

        const config = {
            type: 'doughnut',
            data: this.processChartData(data),
            options: { ...defaultOptions, ...options }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(containerId, chart);
        return chart;
    }

    /**
     * Create canvas element
     */
    createCanvas(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`Chart container ${containerId} not found`);
            return null;
        }

        // Clear existing content
        container.innerHTML = '';

        const canvas = document.createElement('canvas');
        canvas.style.maxHeight = '400px';
        container.appendChild(canvas);

        return canvas;
    }

    /**
     * Process chart data
     */
    processChartData(data) {
        if (!data.datasets) {
            data.datasets = [];
        }

        // Add colors if not provided
        data.datasets.forEach((dataset, index) => {
            if (!dataset.backgroundColor) {
                if (data.labels && data.labels.length > 1) {
                    // Multiple colors for pie/doughnut charts
                    dataset.backgroundColor = this.defaultColors.slice(0, data.labels.length);
                    dataset.borderColor = this.defaultColors.slice(0, data.labels.length);
                } else {
                    // Single color for line/bar charts
                    dataset.backgroundColor = this.defaultColors[index % this.defaultColors.length];
                    dataset.borderColor = this.defaultColors[index % this.defaultColors.length];
                }
            }

            if (!dataset.borderWidth) {
                dataset.borderWidth = 2;
            }
        });

        return data;
    }

    /**
     * Update chart data
     */
    updateChart(containerId, newData) {
        const chart = this.charts.get(containerId);
        if (!chart) return;

        chart.data = this.processChartData(newData);
        chart.update();
    }

    /**
     * Destroy chart
     */
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.destroy();
            this.charts.delete(containerId);
        }
    }

    /**
     * Get chart instance
     */
    getChart(containerId) {
        return this.charts.get(containerId);
    }

    /**
     * Create sales trend chart
     */
    createSalesTrendChart(containerId, salesData) {
        const data = {
            labels: salesData.map(item => item.month),
            datasets: [{
                label: 'المبيعات',
                data: salesData.map(item => item.sales),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };

        return this.createLineChart(containerId, data);
    }

    /**
     * Create revenue comparison chart
     */
    createRevenueComparisonChart(containerId, revenueData) {
        const data = {
            labels: revenueData.map(item => item.category),
            datasets: [{
                label: 'الإيرادات',
                data: revenueData.map(item => item.revenue),
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545',
                    '#6f42c1', '#20c997', '#fd7e14', '#e83e8c'
                ]
            }]
        };

        return this.createBarChart(containerId, data);
    }

    /**
     * Create service distribution chart
     */
    createServiceDistributionChart(containerId, serviceData) {
        const data = {
            labels: serviceData.map(item => item.service),
            datasets: [{
                data: serviceData.map(item => item.count),
                backgroundColor: this.defaultColors
            }]
        };

        return this.createDoughnutChart(containerId, data);
    }

    /**
     * Create customer growth chart
     */
    createCustomerGrowthChart(containerId, growthData) {
        const data = {
            labels: growthData.map(item => item.month),
            datasets: [{
                label: 'عملاء جدد',
                data: growthData.map(item => item.newCustomers),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: true
            }, {
                label: 'إجمالي العملاء',
                data: growthData.map(item => item.totalCustomers),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true
            }]
        };

        return this.createLineChart(containerId, data);
    }

    /**
     * Destroy all charts
     */
    destroyAllCharts() {
        this.charts.forEach((chart, containerId) => {
            chart.destroy();
        });
        this.charts.clear();
    }

    /**
     * Get available chart types
     */
    getChartTypes() {
        return this.chartTypes;
    }

    /**
     * Export chart as image
     */
    exportChart(containerId, filename = 'chart.png') {
        const chart = this.charts.get(containerId);
        if (!chart) return;

        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = filename;
        link.href = url;
        link.click();
    }
}

// Export for use in other modules
window.ChartManager = ChartManager;
