import DIContainer from './core/DIContainer.js';
import { EventBus } from './core/EventBus.js';
import { Router } from './core/Router.js';

// Import all components
import DashboardManagement from './components/DashboardManagement.js';
import WorkOrderManagement from './components/WorkOrderManagement.js';
import CustomerManagement from './components/CustomerManagement.js';
import SupplierManagement from './components/SupplierManagement.js';
import ContactManagement from './components/ContactManagement.js';
import ProductManagement from './components/ProductManagement.js';
import InvoiceManagement from './components/InvoiceManagement.js';
import QuotationManagement from './components/QuotationManagement.js';
import PurchaseManagement from './components/PurchaseManagement.js';
import PaymentManagement from './components/PaymentManagement.js';
import ReportManagement from './components/ReportManagement.js';
import SettingsManagement from './components/SettingsManagement.js';
import NotFound from './components/NotFound.js';

// Create DI container
const container = new DIContainer();

// Register core services
container.register('EventBus', new EventBus());

// Register components
container.register('DashboardManagement', DashboardManagement);
container.register('WorkOrderManagement', WorkOrderManagement);
container.register('CustomerManagement', CustomerManagement);
container.register('SupplierManagement', SupplierManagement);
container.register('ContactManagement', ContactManagement);
container.register('ProductManagement', ProductManagement);
container.register('InvoiceManagement', InvoiceManagement);
container.register('QuotationManagement', QuotationManagement);
container.register('PurchaseManagement', PurchaseManagement);
container.register('PaymentManagement', PaymentManagement);
container.register('ReportManagement', ReportManagement);
container.register('SettingsManagement', SettingsManagement);
container.register('NotFound', NotFound);

// Create router and pass the container
const router = new Router(container);

// Initialize the router
router.init();

// Make router globally available for debugging
window.router = router;