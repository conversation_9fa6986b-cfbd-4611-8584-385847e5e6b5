/**
 * UI Manager - Handles UI Components and Interactions
 * Manages modals, toasts, sidebar, and other UI elements
 */

import { EventBus } from './EventBus.js';

class UIManager {
    constructor() {
        this.eventBus = new EventBus();
        this.modals = new Map();
        this.toasts = [];
        this.sidebarOpen = true;
        this.pageLoader = null;
        
        this.init();
    }

    /**
     * Initialize UI Manager
     */
    init() {
        this.setupModalContainer();
        this.setupToastContainer();
        this.setupSidebar();
        this.setupPageLoader();
        this.setupGlobalUIEvents();
    }

    /**
     * Set up modal container
     */
    setupModalContainer() {
        let modalContainer = document.getElementById('modal-container');
        if (!modalContainer) {
            modalContainer = document.createElement('div');
            modalContainer.id = 'modal-container';
            modalContainer.className = 'modal-container';
            document.body.appendChild(modalContainer);
        }
    }

    /**
     * Set up toast container
     */
    setupToastContainer() {
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
    }

    /**
     * Set up sidebar
     */
    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        
        if (sidebar && mainContent) {
            // Set initial state based on screen size
            if (window.innerWidth <= 768) {
                this.closeSidebar();
            } else {
                this.openSidebar();
            }
        }
    }

    /**
     * Set up page loader
     */
    setupPageLoader() {
        this.pageLoader = document.createElement('div');
        this.pageLoader.className = 'page-loader';
        this.pageLoader.innerHTML = `
            <div class="page-loader-content">
                <div class="loading-spinner"></div>
                <p>جاري التحميل...</p>
            </div>
        `;
        this.pageLoader.style.display = 'none';
        document.body.appendChild(this.pageLoader);
    }

    /**
     * Set up global UI events
     */
    setupGlobalUIEvents() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeTopModal();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });
    }

    /**
     * Show modal
     */
    showModal(config) {
        const {
            id = this.generateId(),
            title = '',
            content = '',
            size = 'medium',
            closable = true,
            actions = [],
            onClose = null
        } = config;

        // Create modal element
        const modal = document.createElement('div');
        modal.className = `modal modal-${size}`;
        modal.setAttribute('data-modal-id', id);
        
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-dialog">
                <div class="modal-content neumorphic">
                    <div class="modal-header">
                        <h3 class="modal-title">${title}</h3>
                        ${closable ? '<button class="modal-close" aria-label="إغلاق">×</button>' : ''}
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    ${actions.length > 0 ? `
                        <div class="modal-footer">
                            ${actions.map(action => `
                                <button class="btn ${action.class || 'btn-secondary'}" 
                                        data-action="${action.action || ''}"
                                        ${action.disabled ? 'disabled' : ''}>
                                    ${action.text}
                                </button>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // Add event listeners
        if (closable) {
            const closeBtn = modal.querySelector('.modal-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.closeModal(id));
            }
        }

        // Add action listeners
        actions.forEach(action => {
            const btn = modal.querySelector(`[data-action="${action.action}"]`);
            if (btn && action.handler) {
                btn.addEventListener('click', (e) => {
                    action.handler(e, id);
                });
            }
        });

        // Add to container
        const container = document.getElementById('modal-container');
        container.appendChild(modal);

        // Store modal reference
        this.modals.set(id, {
            element: modal,
            config,
            onClose
        });

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // Emit event
        this.eventBus.emit('ui:modal-opened', { id, config });

        return id;
    }

    /**
     * Close modal
     */
    closeModal(id) {
        const modal = this.modals.get(id);
        if (!modal) return;

        const { element, onClose } = modal;

        // Hide modal with animation
        element.classList.remove('show');

        setTimeout(() => {
            element.remove();
            this.modals.delete(id);

            // Call onClose callback
            if (onClose) {
                onClose(id);
            }

            // Emit event
            this.eventBus.emit('ui:modal-closed', { id });
        }, 300);
    }

    /**
     * Close top modal
     */
    closeTopModal() {
        const modals = Array.from(this.modals.keys());
        if (modals.length > 0) {
            this.closeModal(modals[modals.length - 1]);
        }
    }

    /**
     * Close all modals
     */
    closeAllModals() {
        const modalIds = Array.from(this.modals.keys());
        modalIds.forEach(id => this.closeModal(id));
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 5000) {
        const id = this.generateId();
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type} neumorphic`;
        toast.setAttribute('data-toast-id', id);
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${icons[type] || icons.info}</span>
                <span class="toast-message">${message}</span>
                <button class="toast-close" aria-label="إغلاق">×</button>
            </div>
        `;

        // Add close listener
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.closeToast(id));

        // Add to container
        const container = document.getElementById('toast-container');
        container.appendChild(toast);

        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Store toast reference
        this.toasts.push({ id, element: toast });

        // Auto-close after duration
        if (duration > 0) {
            setTimeout(() => {
                this.closeToast(id);
            }, duration);
        }

        return id;
    }

    /**
     * Close toast
     */
    closeToast(id) {
        const toastIndex = this.toasts.findIndex(t => t.id === id);
        if (toastIndex === -1) return;

        const toast = this.toasts[toastIndex];
        
        // Hide toast with animation
        toast.element.classList.remove('show');

        setTimeout(() => {
            toast.element.remove();
            this.toasts.splice(toastIndex, 1);
        }, 300);
    }

    /**
     * Show confirmation dialog
     */
    showConfirmation(config) {
        const {
            title = 'تأكيد',
            message = 'هل أنت متأكد؟',
            confirmText = 'تأكيد',
            cancelText = 'إلغاء',
            confirmClass = 'btn-primary',
            onConfirm = null,
            onCancel = null
        } = config;

        return new Promise((resolve) => {
            const modalId = this.showModal({
                title,
                content: `<p>${message}</p>`,
                size: 'small',
                actions: [
                    {
                        text: cancelText,
                        class: 'btn-secondary',
                        action: 'cancel',
                        handler: () => {
                            this.closeModal(modalId);
                            if (onCancel) onCancel();
                            resolve(false);
                        }
                    },
                    {
                        text: confirmText,
                        class: confirmClass,
                        action: 'confirm',
                        handler: () => {
                            this.closeModal(modalId);
                            if (onConfirm) onConfirm();
                            resolve(true);
                        }
                    }
                ]
            });
        });
    }

    /**
     * Show page loader
     */
    showPageLoader() {
        if (this.pageLoader) {
            this.pageLoader.style.display = 'flex';
            setTimeout(() => {
                this.pageLoader.classList.add('show');
            }, 10);
        }
    }

    /**
     * Hide page loader
     */
    hidePageLoader() {
        if (this.pageLoader) {
            this.pageLoader.classList.remove('show');
            setTimeout(() => {
                this.pageLoader.style.display = 'none';
            }, 300);
        }
    }

    /**
     * Toggle sidebar
     */
    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    /**
     * Open sidebar
     */
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const body = document.body;

        if (sidebar && mainContent) {
            sidebar.classList.add('open');
            mainContent.classList.add('sidebar-open');
            body.classList.add('sidebar-open');
            this.sidebarOpen = true;

            this.eventBus.emit('ui:sidebar-opened');
        }
    }

    /**
     * Close sidebar
     */
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const body = document.body;

        if (sidebar && mainContent) {
            sidebar.classList.remove('open');
            mainContent.classList.remove('sidebar-open');
            body.classList.remove('sidebar-open');
            this.sidebarOpen = false;

            this.eventBus.emit('ui:sidebar-closed');
        }
    }

    /**
     * Show loading state for element
     */
    showElementLoader(element, text = 'جاري التحميل...') {
        if (!element) return;

        const loader = document.createElement('div');
        loader.className = 'element-loader';
        loader.innerHTML = `
            <div class="element-loader-content">
                <div class="loading-spinner"></div>
                <span>${text}</span>
            </div>
        `;

        element.style.position = 'relative';
        element.appendChild(loader);

        return loader;
    }

    /**
     * Hide loading state for element
     */
    hideElementLoader(element) {
        if (!element) return;

        const loader = element.querySelector('.element-loader');
        if (loader) {
            loader.remove();
        }
    }

    /**
     * Create dropdown menu
     */
    createDropdown(config) {
        const {
            trigger,
            items = [],
            position = 'bottom-right',
            onSelect = null
        } = config;

        if (!trigger) return;

        const dropdown = document.createElement('div');
        dropdown.className = `dropdown dropdown-${position} neumorphic`;
        
        dropdown.innerHTML = `
            <ul class="dropdown-menu">
                ${items.map(item => `
                    <li class="dropdown-item ${item.disabled ? 'disabled' : ''}" 
                        data-value="${item.value || ''}">
                        ${item.icon ? `<span class="dropdown-icon">${item.icon}</span>` : ''}
                        <span class="dropdown-text">${item.text}</span>
                    </li>
                `).join('')}
            </ul>
        `;

        // Position dropdown
        const rect = trigger.getBoundingClientRect();
        dropdown.style.position = 'fixed';
        dropdown.style.top = `${rect.bottom + 5}px`;
        dropdown.style.left = `${rect.left}px`;
        dropdown.style.zIndex = '1000';

        // Add click listeners
        dropdown.addEventListener('click', (e) => {
            const item = e.target.closest('.dropdown-item');
            if (item && !item.classList.contains('disabled')) {
                const value = item.getAttribute('data-value');
                if (onSelect) {
                    onSelect(value, item);
                }
                this.closeDropdown(dropdown);
            }
        });

        // Add to body
        document.body.appendChild(dropdown);

        // Show dropdown
        setTimeout(() => {
            dropdown.classList.add('show');
        }, 10);

        // Close on outside click
        const closeHandler = (e) => {
            if (!dropdown.contains(e.target) && !trigger.contains(e.target)) {
                this.closeDropdown(dropdown);
                document.removeEventListener('click', closeHandler);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', closeHandler);
        }, 100);

        return dropdown;
    }

    /**
     * Close dropdown
     */
    closeDropdown(dropdown) {
        if (!dropdown) return;

        dropdown.classList.remove('show');
        setTimeout(() => {
            dropdown.remove();
        }, 300);
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Event subscription methods
     */
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }

    off(event, callback) {
        return this.eventBus.off(event, callback);
    }
}

// Export the UIManager class
export { UIManager };
