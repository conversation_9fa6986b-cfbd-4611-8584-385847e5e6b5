/**
 * CSS Variables - نظام الماهر للصيانة والخدمات
 * متغيرات التصميم والألوان
 */

:root {
  /* ========== الألوان الأساسية ========== */
  --primary-color: #2196F3;
  --primary-light: #64B5F6;
  --primary-dark: #1976D2;
  --secondary-color: #FF9800;
  --secondary-light: #FFB74D;
  --secondary-dark: #F57C00;
  
  /* ========== ألوان الحالة ========== */
  --success-color: #4CAF50;
  --success-light: #81C784;
  --success-dark: #388E3C;
  --warning-color: #FF9800;
  --warning-light: #FFB74D;
  --warning-dark: #F57C00;
  --error-color: #F44336;
  --error-light: #E57373;
  --error-dark: #D32F2F;
  --info-color: #2196F3;
  --info-light: #64B5F6;
  --info-dark: #1976D2;
  
  /* ========== الألوان المحايدة ========== */
  --white: #FFFFFF;
  --black: #000000;
  --gray-50: #FAFAFA;
  --gray-100: #F5F5F5;
  --gray-200: #EEEEEE;
  --gray-300: #E0E0E0;
  --gray-400: #BDBDBD;
  --gray-500: #9E9E9E;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  
  /* ========== ألوان الخلفية ========== */
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);
  --bg-card: var(--white);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* ========== ألوان النص ========== */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --text-inverse: var(--white);
  --text-muted: var(--gray-400);
  
  /* ========== ألوان الحدود ========== */
  --border-color: var(--gray-300);
  --border-light: var(--gray-200);
  --border-dark: var(--gray-400);
  --border-focus: var(--primary-color);
  
  /* ========== الظلال ========== */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  
  /* ========== ظلال Neumorphic ========== */
  --neumorphic-shadow-light: 8px 8px 16px rgba(0, 0, 0, 0.1);
  --neumorphic-shadow-dark: -8px -8px 16px rgba(255, 255, 255, 0.8);
  --neumorphic-shadow-inset-light: inset 8px 8px 16px rgba(0, 0, 0, 0.1);
  --neumorphic-shadow-inset-dark: inset -8px -8px 16px rgba(255, 255, 255, 0.8);
  --neumorphic-shadow-pressed: inset 4px 4px 8px rgba(0, 0, 0, 0.15), inset -4px -4px 8px rgba(255, 255, 255, 0.9);
  
  /* ========== الخطوط ========== */
  --font-family-primary: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  --font-family-secondary: 'Arial', sans-serif;
  --font-family-mono: 'Courier New', monospace;
  
  /* ========== أحجام الخطوط ========== */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  /* ========== أوزان الخطوط ========== */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* ========== ارتفاعات الأسطر ========== */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* ========== المسافات ========== */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  
  /* ========== نصف الأقطار ========== */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* ========== الانتقالات ========== */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* ========== أبعاد التخطيط ========== */
  --header-height: 70px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  --container-max-width: 1200px;
  
  /* ========== نقاط الكسر ========== */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ========== Z-Index ========== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========== الثيم الداكن ========== */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --bg-card: var(--gray-800);
  --text-primary: var(--white);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-color: var(--gray-600);
  --border-light: var(--gray-700);
  --border-dark: var(--gray-500);
  
  /* ظلال Neumorphic للثيم الداكن */
  --neumorphic-shadow-light: 8px 8px 16px rgba(0, 0, 0, 0.3);
  --neumorphic-shadow-dark: -8px -8px 16px rgba(255, 255, 255, 0.05);
  --neumorphic-shadow-inset-light: inset 8px 8px 16px rgba(0, 0, 0, 0.3);
  --neumorphic-shadow-inset-dark: inset -8px -8px 16px rgba(255, 255, 255, 0.05);
}

/* ========== الثيم الأزرق ========== */
[data-theme="blue"] {
  --primary-color: #1E88E5;
  --primary-light: #42A5F5;
  --primary-dark: #1565C0;
  --bg-primary: #E3F2FD;
  --bg-secondary: #BBDEFB;
  --bg-card: var(--white);
}

/* ========== الثيم الأخضر ========== */
[data-theme="green"] {
  --primary-color: #43A047;
  --primary-light: #66BB6A;
  --primary-dark: #2E7D32;
  --bg-primary: #E8F5E8;
  --bg-secondary: #C8E6C9;
  --bg-card: var(--white);
}
