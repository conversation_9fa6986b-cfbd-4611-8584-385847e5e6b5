<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أوامر الشغل - نسخة مبسطة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/work-orders.css">
</head>
<body>
    <div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
        <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1>🔧 أوامر الشغل - نسخة مبسطة</h1>
            <div id="status" style="padding: 10px; margin: 10px 0; border-radius: 6px; background: #fff3cd; color: #856404;">
                ⏳ جاري التحميل...
            </div>
        </div>
        
        <div id="work-orders-container">
            <!-- سيتم عرض أوامر الشغل هنا -->
        </div>
    </div>

    <!-- تحميل الكلاس مباشرة بدون modules -->
    <script>
        // Work Order Management Class - نسخة مبسطة
        class SimpleWorkOrderManagement {
            constructor() {
                console.log('🔧 SimpleWorkOrderManagement constructor called');
                this.container = null;
                this.workOrders = [];
                this.filteredWorkOrders = [];
                this.currentFilter = 'all';
                this.currentSort = 'newest';
                this.searchTerm = '';
                
                // حالات أوامر الشغل
                this.statuses = {
                    'new': { name: 'جديد', color: '#007bff', icon: '🆕' },
                    'in_progress': { name: 'قيد التنفيذ', color: '#ffc107', icon: '⚙️' },
                    'completed': { name: 'مكتمل', color: '#28a745', icon: '✅' },
                    'cancelled': { name: 'ملغي', color: '#dc3545', icon: '❌' }
                };
                
                // أنواع الخدمات
                this.serviceTypes = [
                    'صيانة مكيفات',
                    'أعمال كهربائية', 
                    'أعمال سباكة',
                    'صيانة عامة',
                    'تركيب أجهزة',
                    'خدمات أخرى'
                ];
                
                // مستويات الأولوية
                this.priorities = {
                    'urgent': { name: 'عاجل', color: '#dc3545', icon: '🔥' },
                    'normal': { name: 'عادي', color: '#007bff', icon: '📋' },
                    'low': { name: 'منخفض', color: '#6c757d', icon: '📝' }
                };
                
                this.createSampleData();
                this.filteredWorkOrders = [...this.workOrders];
            }

            createSampleData() {
                const sampleOrders = [
                    {
                        id: 'WO-2024-001',
                        customerId: 'CUST-001',
                        customerName: 'أحمد محمد العلي',
                        customerPhone: '0501234567',
                        customerAddress: 'حي النزهة، الرياض',
                        serviceType: 'صيانة مكيفات',
                        description: 'صيانة دورية لمكيف سبليت 2 طن - تنظيف وفحص شامل',
                        priority: 'normal',
                        status: 'new',
                        assignedTo: 'فني أحمد سالم',
                        scheduledDate: '2024-01-15',
                        scheduledTime: '09:00',
                        createdDate: '2024-01-10',
                        estimatedCost: 200,
                        actualCost: 0,
                        estimatedDuration: '2 ساعة',
                        notes: 'العميل طلب الحضور في الصباح الباكر'
                    },
                    {
                        id: 'WO-2024-002',
                        customerId: 'CUST-002',
                        customerName: 'فاطمة سعد الغامدي',
                        customerPhone: '0509876543',
                        customerAddress: 'حي الملك فهد، جدة',
                        serviceType: 'أعمال كهربائية',
                        description: 'إصلاح عطل في لوحة الكهرباء الرئيسية',
                        priority: 'urgent',
                        status: 'in_progress',
                        assignedTo: 'فني محمد علي',
                        scheduledDate: '2024-01-12',
                        scheduledTime: '14:00',
                        createdDate: '2024-01-11',
                        estimatedCost: 350,
                        actualCost: 320,
                        estimatedDuration: '3 ساعات',
                        notes: 'عطل عاجل - انقطاع كهرباء جزئي'
                    },
                    {
                        id: 'WO-2024-003',
                        customerId: 'CUST-003',
                        customerName: 'شركة التقنية المتقدمة',
                        customerPhone: '0112345678',
                        customerAddress: 'طريق الملك عبدالعزيز، الرياض',
                        serviceType: 'صيانة عامة',
                        description: 'صيانة شاملة لمكاتب الشركة - تكييف وكهرباء وسباكة',
                        priority: 'normal',
                        status: 'completed',
                        assignedTo: 'فريق الصيانة الشاملة',
                        scheduledDate: '2024-01-08',
                        scheduledTime: '08:00',
                        createdDate: '2024-01-05',
                        estimatedCost: 1500,
                        actualCost: 1420,
                        estimatedDuration: '8 ساعات',
                        notes: 'عقد صيانة شهري - تم التنفيذ بنجاح'
                    }
                ];
                
                this.workOrders = sampleOrders;
            }

            render(container) {
                console.log('🔧 SimpleWorkOrderManagement render called', container);
                this.container = container;
                
                const html = `
                    <div class="work-order-management">
                        <!-- Header -->
                        <div class="page-header">
                            <div class="page-title">
                                <h1>
                                    <span class="page-icon">🔧</span>
                                    إدارة أوامر الشغل
                                </h1>
                                <p class="page-description">إدارة وتتبع جميع أوامر الصيانة والخدمات</p>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="stats-grid">
                            <div class="stat-card new">
                                <div class="stat-icon">🆕</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.workOrders.filter(wo => wo.status === 'new').length}</div>
                                    <div class="stat-label">أوامر جديدة</div>
                                </div>
                            </div>
                            <div class="stat-card in-progress">
                                <div class="stat-icon">⚙️</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.workOrders.filter(wo => wo.status === 'in_progress').length}</div>
                                    <div class="stat-label">قيد التنفيذ</div>
                                </div>
                            </div>
                            <div class="stat-card completed">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.workOrders.filter(wo => wo.status === 'completed').length}</div>
                                    <div class="stat-label">مكتملة</div>
                                </div>
                            </div>
                            <div class="stat-card total">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.workOrders.length}</div>
                                    <div class="stat-label">إجمالي الأوامر</div>
                                </div>
                            </div>
                        </div>

                        <!-- Work Orders List -->
                        <div class="work-orders-section">
                            <div class="section-header">
                                <h2>قائمة أوامر الشغل</h2>
                            </div>
                            
                            <div class="work-orders-grid">
                                ${this.workOrders.map(wo => this.renderWorkOrderCard(wo)).join('')}
                            </div>
                        </div>
                    </div>
                `;
                
                container.innerHTML = html;
                console.log('✅ SimpleWorkOrderManagement render completed');
            }

            renderWorkOrderCard(workOrder) {
                const status = this.statuses[workOrder.status];
                const priority = this.priorities[workOrder.priority];
                
                return `
                    <div class="work-order-card">
                        <div class="card-header">
                            <div class="card-id">${workOrder.id}</div>
                            <div class="card-badges">
                                <span class="priority-badge ${workOrder.priority}" title="${priority.name}">
                                    ${priority.icon}
                                </span>
                                <span class="status-badge ${workOrder.status}" title="${status.name}">
                                    ${status.icon} ${status.name}
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <h3 class="customer-name">${workOrder.customerName}</h3>
                            <div class="service-type">${workOrder.serviceType}</div>
                            <p class="description">${workOrder.description}</p>
                            
                            <div class="card-details">
                                <div class="detail-item">
                                    <span class="detail-icon">📅</span>
                                    <span class="detail-text">${workOrder.scheduledDate} ${workOrder.scheduledTime || ''}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">👨‍🔧</span>
                                    <span class="detail-text">${workOrder.assignedTo}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">💰</span>
                                    <span class="detail-text">${workOrder.estimatedCost} ر.س</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-actions">
                            <button class="btn btn-sm btn-primary">عرض</button>
                            <button class="btn btn-sm btn-secondary">تعديل</button>
                            <button class="btn btn-sm btn-outline">تغيير الحالة</button>
                        </div>
                    </div>
                `;
            }
        }

        // تشغيل التطبيق
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const colors = {
                info: { bg: '#d1ecf1', color: '#0c5460' },
                success: { bg: '#d4edda', color: '#155724' },
                error: { bg: '#f8d7da', color: '#721c24' }
            };
            statusDiv.style.background = colors[type].bg;
            statusDiv.style.color = colors[type].color;
            statusDiv.innerHTML = message;
        }

        function loadWorkOrders() {
            try {
                updateStatus('🔧 إنشاء مدير أوامر الشغل...', 'info');
                
                const workOrderManager = new SimpleWorkOrderManagement();
                updateStatus('✅ تم إنشاء المدير بنجاح', 'success');
                
                const container = document.getElementById('work-orders-container');
                workOrderManager.render(container);
                
                updateStatus(`✅ تم عرض ${workOrderManager.workOrders.length} أوامر شغل بنجاح`, 'success');
                
                // Make globally available
                window.workOrderManager = workOrderManager;
                
            } catch (error) {
                updateStatus(`❌ خطأ: ${error.message}`, 'error');
                console.error('Error:', error);
            }
        }

        // تشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadWorkOrders);
    </script>
</body>
</html>
