<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أوامر الشغل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/work-orders.css">
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار وحدة أوامر الشغل</h1>
        
        <button class="test-button" onclick="testWorkOrderClass()">اختبار تحميل الكلاس</button>
        <button class="test-button" onclick="testCreateInstance()">اختبار إنشاء Instance</button>
        <button class="test-button" onclick="testRenderWorkOrders()">اختبار عرض أوامر الشغل</button>
        
        <div id="test-results"></div>
        
        <div id="work-order-container" style="margin-top: 30px;">
            <!-- سيتم عرض أوامر الشغل هنا -->
        </div>
    </div>

    <script type="module">
        let workOrderInstance = null;
        
        // Test loading WorkOrderManagement class
        window.testWorkOrderClass = async function() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                // Try to import the module
                const module = await import('./js/components/WorkOrderManagement.js');
                const WorkOrderManagement = module.default;
                
                if (WorkOrderManagement) {
                    resultsDiv.innerHTML += `
                        <div class="test-result success">
                            ✅ تم تحميل كلاس WorkOrderManagement بنجاح
                            <br>Type: ${typeof WorkOrderManagement}
                            <br>Name: ${WorkOrderManagement.name}
                        </div>
                    `;
                    
                    // Make it globally available
                    window.WorkOrderManagement = WorkOrderManagement;
                } else {
                    throw new Error('WorkOrderManagement class not found in module');
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        ❌ فشل في تحميل WorkOrderManagement
                        <br>Error: ${error.message}
                    </div>
                `;
            }
        };
        
        // Test creating instance
        window.testCreateInstance = function() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                if (!window.WorkOrderManagement) {
                    throw new Error('WorkOrderManagement class not loaded');
                }
                
                workOrderInstance = new window.WorkOrderManagement();
                
                resultsDiv.innerHTML += `
                    <div class="test-result success">
                        ✅ تم إنشاء instance بنجاح
                        <br>Instance type: ${typeof workOrderInstance}
                        <br>Has render method: ${typeof workOrderInstance.render === 'function'}
                        <br>Work orders count: ${workOrderInstance.workOrders.length}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        ❌ فشل في إنشاء instance
                        <br>Error: ${error.message}
                    </div>
                `;
            }
        };
        
        // Test rendering work orders
        window.testRenderWorkOrders = function() {
            const resultsDiv = document.getElementById('test-results');
            const container = document.getElementById('work-order-container');
            
            try {
                if (!workOrderInstance) {
                    throw new Error('WorkOrderManagement instance not created');
                }
                
                // Clear container
                container.innerHTML = '';
                
                // Render work orders
                workOrderInstance.render(container);
                
                resultsDiv.innerHTML += `
                    <div class="test-result success">
                        ✅ تم عرض أوامر الشغل بنجاح
                        <br>Container has content: ${container.innerHTML.length > 0}
                        <br>Work orders rendered: ${workOrderInstance.filteredWorkOrders.length}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        ❌ فشل في عرض أوامر الشغل
                        <br>Error: ${error.message}
                        <br>Stack: ${error.stack}
                    </div>
                `;
            }
        };
        
        // Auto-test on load
        window.addEventListener('load', async () => {
            await new Promise(resolve => setTimeout(resolve, 500));
            await testWorkOrderClass();
            await new Promise(resolve => setTimeout(resolve, 500));
            testCreateInstance();
            await new Promise(resolve => setTimeout(resolve, 500));
            testRenderWorkOrders();
        });
    </script>
</body>
</html>
