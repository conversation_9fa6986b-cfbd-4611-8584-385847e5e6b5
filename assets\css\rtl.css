/* ===== RTL (Right-to-Left) SPECIFIC STYLES ===== */

/* ===== RTL LAYOUT ADJUSTMENTS ===== */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

/* Grid template areas for RTL */
[dir="rtl"] .app-container {
  grid-template-areas: 
    "header header"
    "main sidebar";
  grid-template-columns: 1fr var(--sidebar-width);
}

/* ===== SIDEBAR RTL ADJUSTMENTS ===== */
[dir="rtl"] .sidebar {
  border-left: none;
  border-right: 1px solid var(--border-light);
}

[dir="rtl"] .nav-item.active > .nav-link::before {
  right: auto;
  left: 0;
}

[dir="rtl"] .nav-submenu .nav-link {
  padding-right: var(--space-lg);
  padding-left: calc(var(--space-lg) + var(--space-xl));
}

/* Navigation arrows for RTL */
[dir="rtl"] .nav-arrow {
  transform: rotate(180deg);
}

[dir="rtl"] .nav-item.expanded > .nav-link .nav-arrow {
  transform: rotate(90deg);
}

/* ===== BREADCRUMB RTL ADJUSTMENTS ===== */
[dir="rtl"] .breadcrumb-list li:not(:last-child)::after {
  content: '▶';
}

/* ===== FORM ELEMENTS RTL ===== */
[dir="rtl"] .form-control {
  text-align: right;
}

[dir="rtl"] .form-control[type="number"] {
  text-align: left; /* Numbers should remain LTR */
}

[dir="rtl"] .form-control[type="email"],
[dir="rtl"] .form-control[type="url"],
[dir="rtl"] .form-control[type="tel"] {
  text-align: left; /* These inputs often contain LTR content */
}

/* ===== BUTTON GROUPS RTL ===== */
[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-group .btn:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
}

[dir="rtl"] .btn-group .btn:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
}

/* ===== MODAL RTL ADJUSTMENTS ===== */
[dir="rtl"] .modal-footer {
  justify-content: flex-start;
}

[dir="rtl"] .modal-footer .btn {
  order: -1;
}

[dir="rtl"] .modal-footer .btn:first-child {
  order: 0;
}

/* ===== TABLE RTL ADJUSTMENTS ===== */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

[dir="rtl"] .table th:first-child,
[dir="rtl"] .table td:first-child {
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

[dir="rtl"] .table th:last-child,
[dir="rtl"] .table td:last-child {
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

/* ===== TOAST RTL ADJUSTMENTS ===== */
[dir="rtl"] .toast-container {
  left: auto;
  right: var(--space-lg);
}

[dir="rtl"] .toast {
  transform: translateX(100%);
}

[dir="rtl"] .toast.show {
  transform: translateX(0);
}

[dir="rtl"] .toast-success,
[dir="rtl"] .toast-error,
[dir="rtl"] .toast-warning,
[dir="rtl"] .toast-info {
  border-right: none;
  border-left: 4px solid;
}

[dir="rtl"] .toast-success {
  border-left-color: var(--color-success);
}

[dir="rtl"] .toast-error {
  border-left-color: var(--color-error);
}

[dir="rtl"] .toast-warning {
  border-left-color: var(--color-warning);
}

[dir="rtl"] .toast-info {
  border-left-color: var(--color-info);
}

/* ===== DROPDOWN RTL ADJUSTMENTS ===== */
[dir="rtl"] .dropdown-bottom-right {
  transform-origin: top left;
}

[dir="rtl"] .dropdown-bottom-left {
  transform-origin: top right;
}

/* ===== PROGRESS BAR RTL ===== */
[dir="rtl"] .progress-bar::after {
  animation: progress-shine-rtl 2s infinite;
}

@keyframes progress-shine-rtl {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* ===== NOTIFICATION BADGE RTL ===== */
[dir="rtl"] .notification-badge {
  left: auto;
  right: -2px;
}

/* ===== RESPONSIVE RTL ADJUSTMENTS ===== */

/* Tablet RTL */
@media (max-width: 1024px) {
  [dir="rtl"] .app-container {
    grid-template-columns: 1fr var(--sidebar-collapsed-width);
  }
}

/* Mobile RTL */
@media (max-width: 768px) {
  [dir="rtl"] .app-container {
    grid-template-areas: 
      "header"
      "main";
    grid-template-columns: 1fr;
  }
  
  [dir="rtl"] .sidebar {
    right: auto;
    left: 0;
    transform: translateX(-100%);
  }
  
  [dir="rtl"] .sidebar.open {
    transform: translateX(0);
  }
  
  [dir="rtl"] .company-brand {
    text-align: left;
  }
}

/* ===== SPECIFIC RTL TYPOGRAPHY ===== */

/* Arabic text improvements */
[dir="rtl"] {
  font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* Improve Arabic number display */
[dir="rtl"] .arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
}

/* Mixed content (Arabic + English) */
[dir="rtl"] .mixed-content {
  unicode-bidi: plaintext;
}

/* Force LTR for specific content */
.ltr-content {
  direction: ltr !important;
  text-align: left !important;
}

/* Force RTL for specific content */
.rtl-content {
  direction: rtl !important;
  text-align: right !important;
}

/* ===== ICON ADJUSTMENTS FOR RTL ===== */

/* Flip icons that have directional meaning */
[dir="rtl"] .icon-arrow-right::before {
  content: '◀';
}

[dir="rtl"] .icon-arrow-left::before {
  content: '▶';
}

[dir="rtl"] .icon-chevron-right::before {
  content: '‹';
}

[dir="rtl"] .icon-chevron-left::before {
  content: '›';
}

/* Don't flip these icons */
[dir="rtl"] .icon-no-flip {
  transform: scaleX(-1);
}

/* ===== FORM LAYOUT RTL ===== */

/* Form groups with labels */
[dir="rtl"] .form-group-horizontal {
  flex-direction: row-reverse;
}

[dir="rtl"] .form-group-horizontal .form-label {
  text-align: right;
  margin-left: var(--space-md);
  margin-right: 0;
}

/* Input groups */
[dir="rtl"] .input-group {
  flex-direction: row-reverse;
}

[dir="rtl"] .input-group .form-control {
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

[dir="rtl"] .input-group .input-group-text {
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

/* ===== CARD LAYOUT RTL ===== */

/* Card with image */
[dir="rtl"] .card-horizontal {
  flex-direction: row-reverse;
}

[dir="rtl"] .card-horizontal .card-image {
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
}

/* ===== LIST ADJUSTMENTS RTL ===== */

/* Ordered lists */
[dir="rtl"] ol {
  padding-right: var(--space-lg);
  padding-left: 0;
}

/* Unordered lists */
[dir="rtl"] ul {
  padding-right: var(--space-lg);
  padding-left: 0;
}

/* Custom list markers for RTL */
[dir="rtl"] .list-custom {
  list-style: none;
  padding-right: 0;
}

[dir="rtl"] .list-custom li::before {
  content: '•';
  color: var(--color-primary);
  font-weight: bold;
  position: absolute;
  right: -1em;
}

/* ===== FLEXBOX RTL UTILITIES ===== */

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* ===== MARGIN AND PADDING RTL UTILITIES ===== */

[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .pr-0 { padding-right: 0; padding-left: 0; }
[dir="rtl"] .pl-0 { padding-left: 0; padding-right: 0; }

[dir="rtl"] .border-r { border-right: none; border-left: 1px solid var(--border-light); }
[dir="rtl"] .border-l { border-left: none; border-right: 1px solid var(--border-light); }

/* ===== ANIMATION ADJUSTMENTS FOR RTL ===== */

[dir="rtl"] .slide-in-right {
  animation: slideInLeft var(--transition-normal);
}

[dir="rtl"] .slide-in-left {
  animation: slideInRight var(--transition-normal);
}

[dir="rtl"] .slide-out-right {
  animation: slideOutLeft var(--transition-normal);
}

[dir="rtl"] .slide-out-left {
  animation: slideOutRight var(--transition-normal);
}

/* RTL-specific animations */
@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutLeft {
  from { transform: translateX(0); }
  to { transform: translateX(-100%); }
}

@keyframes slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

/* ===== ACCESSIBILITY FOR RTL ===== */

/* Screen reader improvements for RTL */
[dir="rtl"] .sr-only-rtl {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for RTL */
[dir="rtl"] .focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  outline-style: solid;
}

/* ===== PRINT ADJUSTMENTS FOR RTL ===== */

@media print {
  [dir="rtl"] * {
    direction: rtl !important;
    text-align: right !important;
  }
  
  [dir="rtl"] .ltr-content {
    direction: ltr !important;
    text-align: left !important;
  }
}
