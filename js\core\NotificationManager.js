/**
 * Notification Manager
 * إدارة الإشعارات المتقدمة
 */
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 50;
        this.autoMarkReadDelay = 5000; // 5 seconds
        this.types = {
            info: { icon: 'ℹ️', color: '#007bff' },
            success: { icon: '✅', color: '#28a745' },
            warning: { icon: '⚠️', color: '#ffc107' },
            error: { icon: '❌', color: '#dc3545' },
            invoice: { icon: '📄', color: '#6f42c1' },
            payment: { icon: '💰', color: '#20c997' },
            customer: { icon: '👤', color: '#fd7e14' },
            system: { icon: '⚙️', color: '#6c757d' }
        };
    }

    /**
     * Initialize notification manager
     */
    init() {
        console.log('🔔 Initializing Notification Manager...');
        this.createContainer();
        this.loadNotifications();
        this.setupEventListeners();
        this.requestPermission();
        console.log('✅ Notification Manager initialized');
    }

    /**
     * Create notification container
     */
    createContainer() {
        // Check if container already exists
        const existingContainer = document.getElementById('notification-dropdown');
        if (existingContainer) {
            this.container = existingContainer;
            return;
        }

        // Create dropdown container
        this.container = document.createElement('div');
        this.container.id = 'notification-dropdown';
        this.container.className = 'notification-dropdown';
        this.container.innerHTML = `
            <div class="notification-header">
                <h3>الإشعارات</h3>
                <div class="notification-actions">
                    <button class="mark-all-read-btn" title="تحديد الكل كمقروء">
                        <span>✓</span>
                    </button>
                    <button class="clear-all-btn" title="مسح الكل">
                        <span>🗑️</span>
                    </button>
                </div>
            </div>
            <div class="notification-list"></div>
            <div class="notification-footer">
                <button class="view-all-btn">عرض جميع الإشعارات</button>
            </div>
        `;

        // Add to notification button parent
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn && notificationBtn.parentElement) {
            // Make parent relative for positioning
            notificationBtn.parentElement.style.position = 'relative';
            notificationBtn.parentElement.appendChild(this.container);
        } else {
            // Fallback: add to header actions
            const headerActions = document.querySelector('.header-actions');
            if (headerActions) {
                headerActions.appendChild(this.container);
            }
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Notification button click
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-dropdown') && 
                !e.target.closest('.notification-btn')) {
                this.closeDropdown();
            }
        });

        // Container event listeners
        if (this.container) {
            // Mark all as read
            this.container.querySelector('.mark-all-read-btn').addEventListener('click', () => {
                this.markAllAsRead();
            });

            // Clear all notifications
            this.container.querySelector('.clear-all-btn').addEventListener('click', () => {
                this.clearAll();
            });

            // View all notifications
            this.container.querySelector('.view-all-btn').addEventListener('click', () => {
                this.showAllNotifications();
            });
        }
    }

    /**
     * Request browser notification permission
     */
    async requestPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            await Notification.requestPermission();
        }
    }

    /**
     * Add notification
     */
    addNotification(notification) {
        const id = Date.now() + Math.random();
        const newNotification = {
            id,
            title: notification.title,
            message: notification.message,
            type: notification.type || 'info',
            timestamp: new Date(),
            read: false,
            persistent: notification.persistent || false,
            action: notification.action || null,
            data: notification.data || null
        };

        // Add to beginning of array
        this.notifications.unshift(newNotification);

        // Limit notifications
        if (this.notifications.length > this.maxNotifications) {
            this.notifications = this.notifications.slice(0, this.maxNotifications);
        }

        // Save to localStorage
        this.saveNotifications();

        // Update UI
        this.updateNotificationBadge();
        this.renderNotifications();

        // Show browser notification if permission granted
        this.showBrowserNotification(newNotification);

        // Auto mark as read for non-persistent notifications
        if (!newNotification.persistent) {
            setTimeout(() => {
                this.markAsRead(id);
            }, this.autoMarkReadDelay);
        }

        return id;
    }

    /**
     * Show browser notification
     */
    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const typeInfo = this.types[notification.type] || this.types.info;
            
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                badge: '/favicon.ico',
                tag: notification.id,
                requireInteraction: notification.persistent
            });

            browserNotification.onclick = () => {
                window.focus();
                if (notification.action) {
                    notification.action(notification.data);
                }
                browserNotification.close();
            };

            // Auto close after 5 seconds for non-persistent notifications
            if (!notification.persistent) {
                setTimeout(() => {
                    browserNotification.close();
                }, 5000);
            }
        }
    }

    /**
     * Mark notification as read
     */
    markAsRead(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification && !notification.read) {
            notification.read = true;
            this.saveNotifications();
            this.updateNotificationBadge();
            this.renderNotifications();
        }
    }

    /**
     * Mark all notifications as read
     */
    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.saveNotifications();
        this.updateNotificationBadge();
        this.renderNotifications();
    }

    /**
     * Remove notification
     */
    removeNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
        this.saveNotifications();
        this.updateNotificationBadge();
        this.renderNotifications();
    }

    /**
     * Clear all notifications
     */
    clearAll() {
        window.uiManager?.showConfirmation({
            title: 'مسح جميع الإشعارات',
            message: 'هل أنت متأكد من مسح جميع الإشعارات؟',
            confirmText: 'مسح',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.notifications = [];
                this.saveNotifications();
                this.updateNotificationBadge();
                this.renderNotifications();
            }
        });
    }

    /**
     * Toggle dropdown
     */
    toggleDropdown() {
        if (this.container) {
            this.container.classList.toggle('show');
            if (this.container.classList.contains('show')) {
                this.renderNotifications();
            }
        }
    }

    /**
     * Close dropdown
     */
    closeDropdown() {
        if (this.container) {
            this.container.classList.remove('show');
        }
    }

    /**
     * Update notification badge
     */
    updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        const unreadCount = this.notifications.filter(n => !n.read).length;
        
        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    /**
     * Render notifications in dropdown
     */
    renderNotifications() {
        if (!this.container) return;

        const listContainer = this.container.querySelector('.notification-list');
        if (!listContainer) return;

        if (this.notifications.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-notifications">
                    <div class="empty-icon">🔔</div>
                    <p>لا توجد إشعارات</p>
                </div>
            `;
            return;
        }

        // Show only recent notifications (last 10)
        const recentNotifications = this.notifications.slice(0, 10);
        
        listContainer.innerHTML = recentNotifications.map(notification => {
            const typeInfo = this.types[notification.type] || this.types.info;
            const timeAgo = this.getTimeAgo(notification.timestamp);
            
            return `
                <div class="notification-item ${notification.read ? 'read' : 'unread'}" data-id="${notification.id}">
                    <div class="notification-icon" style="color: ${typeInfo.color}">
                        ${typeInfo.icon}
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${timeAgo}</div>
                    </div>
                    <div class="notification-actions">
                        ${!notification.read ? `
                            <button class="mark-read-btn" data-id="${notification.id}" title="تحديد كمقروء">
                                ✓
                            </button>
                        ` : ''}
                        <button class="remove-btn" data-id="${notification.id}" title="حذف">
                            ✕
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        // Add event listeners to notification items
        this.attachNotificationListeners();
    }

    /**
     * Attach event listeners to notification items
     */
    attachNotificationListeners() {
        if (!this.container) return;

        // Notification item clicks
        this.container.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.closest('.notification-actions')) return;
                
                const id = parseInt(item.dataset.id);
                const notification = this.notifications.find(n => n.id === id);
                
                if (notification) {
                    this.markAsRead(id);
                    if (notification.action) {
                        notification.action(notification.data);
                    }
                    this.closeDropdown();
                }
            });
        });

        // Mark as read buttons
        this.container.querySelectorAll('.mark-read-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.markAsRead(id);
            });
        });

        // Remove buttons
        this.container.querySelectorAll('.remove-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = parseInt(btn.dataset.id);
                this.removeNotification(id);
            });
        });
    }

    /**
     * Show all notifications modal
     */
    showAllNotifications() {
        const modalContent = `
            <div class="all-notifications-modal">
                <div class="notifications-header">
                    <h2>جميع الإشعارات</h2>
                    <div class="notifications-filters">
                        <button class="filter-btn active" data-filter="all">الكل</button>
                        <button class="filter-btn" data-filter="unread">غير مقروءة</button>
                        <button class="filter-btn" data-filter="read">مقروءة</button>
                    </div>
                </div>
                <div class="notifications-content">
                    ${this.renderAllNotifications()}
                </div>
            </div>
        `;

        window.uiManager?.showModal({
            title: 'الإشعارات',
            content: modalContent,
            size: 'large',
            onShow: () => {
                this.attachAllNotificationsListeners();
            }
        });

        this.closeDropdown();
    }

    /**
     * Render all notifications
     */
    renderAllNotifications(filter = 'all') {
        let filteredNotifications = this.notifications;
        
        if (filter === 'unread') {
            filteredNotifications = this.notifications.filter(n => !n.read);
        } else if (filter === 'read') {
            filteredNotifications = this.notifications.filter(n => n.read);
        }

        if (filteredNotifications.length === 0) {
            return `
                <div class="empty-notifications">
                    <div class="empty-icon">🔔</div>
                    <p>لا توجد إشعارات</p>
                </div>
            `;
        }

        return filteredNotifications.map(notification => {
            const typeInfo = this.types[notification.type] || this.types.info;
            const timeAgo = this.getTimeAgo(notification.timestamp);
            
            return `
                <div class="notification-item ${notification.read ? 'read' : 'unread'}" data-id="${notification.id}">
                    <div class="notification-icon" style="color: ${typeInfo.color}">
                        ${typeInfo.icon}
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${timeAgo}</div>
                    </div>
                    <div class="notification-actions">
                        ${!notification.read ? `
                            <button class="mark-read-btn" data-id="${notification.id}" title="تحديد كمقروء">
                                ✓
                            </button>
                        ` : ''}
                        <button class="remove-btn" data-id="${notification.id}" title="حذف">
                            ✕
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Attach listeners for all notifications modal
     */
    attachAllNotificationsListeners() {
        const modal = document.querySelector('.all-notifications-modal');
        if (!modal) return;

        // Filter buttons
        modal.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const filter = btn.dataset.filter;
                const content = modal.querySelector('.notifications-content');
                content.innerHTML = this.renderAllNotifications(filter);
                this.attachAllNotificationsListeners();
            });
        });

        // Notification actions
        this.attachNotificationListeners();
    }

    /**
     * Get time ago string
     */
    getTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        if (hours < 24) return `منذ ${hours} ساعة`;
        if (days < 7) return `منذ ${days} يوم`;
        
        return timestamp.toLocaleDateString('ar-SA');
    }

    /**
     * Save notifications to localStorage
     */
    saveNotifications() {
        try {
            localStorage.setItem('almaher_notifications', JSON.stringify(this.notifications));
        } catch (error) {
            console.warn('Failed to save notifications:', error);
        }
    }

    /**
     * Load notifications from localStorage
     */
    loadNotifications() {
        try {
            const saved = localStorage.getItem('almaher_notifications');
            if (saved) {
                this.notifications = JSON.parse(saved).map(n => ({
                    ...n,
                    timestamp: new Date(n.timestamp)
                }));
                this.updateNotificationBadge();
            }
        } catch (error) {
            console.warn('Failed to load notifications:', error);
            this.notifications = [];
        }
    }

    /**
     * Get unread count
     */
    getUnreadCount() {
        return this.notifications.filter(n => !n.read).length;
    }

    /**
     * Get all notifications
     */
    getAllNotifications() {
        return this.notifications;
    }

    /**
     * Clear old notifications (older than 30 days)
     */
    clearOldNotifications() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        this.notifications = this.notifications.filter(n => n.timestamp > thirtyDaysAgo);
        this.saveNotifications();
        this.updateNotificationBadge();
    }

    // Predefined notification methods
    notifyInvoiceCreated(invoiceNumber, customerName) {
        this.addNotification({
            title: 'فاتورة جديدة',
            message: `تم إنشاء فاتورة رقم ${invoiceNumber} للعميل ${customerName}`,
            type: 'invoice',
            persistent: false
        });
    }

    notifyPaymentReceived(amount, customerName) {
        this.addNotification({
            title: 'دفعة جديدة',
            message: `تم استلام دفعة بقيمة ${amount} ر.س من ${customerName}`,
            type: 'payment',
            persistent: true
        });
    }

    notifyNewCustomer(customerName) {
        this.addNotification({
            title: 'عميل جديد',
            message: `تم إضافة العميل ${customerName} بنجاح`,
            type: 'customer',
            persistent: false
        });
    }

    notifySystemUpdate(message) {
        this.addNotification({
            title: 'تحديث النظام',
            message: message,
            type: 'system',
            persistent: true
        });
    }
}

// Export for use in other modules
window.NotificationManager = NotificationManager;
