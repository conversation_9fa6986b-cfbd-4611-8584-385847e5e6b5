/* ===== RTL FIX CSS ===== */
/* This file ensures complete RTL layout for the entire application */

/* Global RTL enforcement */
* {
  direction: rtl !important;
  text-align: right !important;
}

/* Exceptions for specific elements that should remain LTR */
input[type="number"],
input[type="email"],
input[type="url"],
input[type="tel"],
.ltr-content,
.amount,
.price,
.phone,
.email {
  direction: ltr !important;
  text-align: left !important;
}

/* App container RTL */
.app-container {
  grid-template-areas: "header header" "sidebar main" !important;
  grid-template-columns: var(--sidebar-width) 1fr !important;
}

/* Header RTL */
.header-content {
  direction: rtl !important;
  text-align: right !important;
}

.company-brand {
  text-align: center !important;
}

.header-actions {
  direction: rtl !important;
  flex-direction: row !important;
}

.sidebar-toggle {
  margin-right: auto !important;
  margin-left: 0 !important;
}

/* Sidebar RTL */
.sidebar {
  border-left: 1px solid #e9ecef !important;
  border-right: none !important;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1) !important;
}

.sidebar-content {
  direction: rtl !important;
  text-align: right !important;
}

.sidebar-header {
  direction: rtl !important;
  text-align: right !important;
}

.sidebar-logo {
  direction: rtl !important;
  flex-direction: row !important;
}

/* Navigation RTL */
.nav-menu {
  direction: rtl !important;
  text-align: right !important;
}

.nav-item {
  direction: rtl !important;
  text-align: right !important;
}

.nav-link {
  direction: rtl !important;
  text-align: right !important;
  border-left: 3px solid transparent !important;
  border-right: none !important;
  flex-direction: row !important;
}

.nav-item.active > .nav-link {
  border-left-color: #007bff !important;
  border-right-color: transparent !important;
}

.nav-icon {
  order: 2 !important;
  margin-left: var(--space-md) !important;
  margin-right: 0 !important;
}

.nav-text {
  order: 1 !important;
  text-align: right !important;
}

.nav-arrow {
  order: 3 !important;
  transform: rotate(180deg) !important;
}

.nav-item.expanded > .nav-link .nav-arrow {
  transform: rotate(90deg) !important;
}

.nav-submenu .nav-link {
  padding-left: calc(var(--space-lg) + var(--space-xl)) !important;
  padding-right: var(--space-lg) !important;
}

/* Main content RTL */
.main-content {
  direction: rtl !important;
  text-align: right !important;
}

.content-wrapper {
  direction: rtl !important;
  text-align: right !important;
}

.page-content {
  direction: rtl !important;
  text-align: right !important;
}

/* Breadcrumb RTL */
.breadcrumb {
  direction: rtl !important;
  text-align: right !important;
}

.breadcrumb-list {
  direction: rtl !important;
  flex-direction: row !important;
}

.breadcrumb-list li:not(:last-child)::after {
  content: '▶' !important;
}

/* Dashboard RTL */
.dashboard-header {
  direction: rtl !important;
  text-align: right !important;
}

.dashboard-title {
  direction: rtl !important;
  text-align: right !important;
}

.dashboard-subtitle {
  direction: rtl !important;
  text-align: right !important;
}

.quick-actions-grid {
  direction: rtl !important;
  text-align: right !important;
}

.quick-action-card {
  direction: rtl !important;
  text-align: right !important;
}

.stats-grid {
  direction: rtl !important;
  text-align: right !important;
}

.stat-card {
  direction: rtl !important;
  text-align: right !important;
}

.stat-icon {
  order: 2 !important;
  margin-left: var(--space-md) !important;
  margin-right: 0 !important;
}

.stat-content {
  order: 1 !important;
  text-align: right !important;
}

/* Page header RTL */
.page-header {
  direction: rtl !important;
  text-align: right !important;
}

.page-title-section {
  direction: rtl !important;
  text-align: right !important;
}

.page-actions {
  direction: rtl !important;
  flex-direction: row !important;
}

/* Table RTL */
.table-container {
  direction: rtl !important;
  text-align: right !important;
}

.data-table {
  direction: rtl !important;
  text-align: right !important;
}

.data-table th,
.data-table td {
  text-align: right !important;
}

/* Form RTL */
.form-row {
  direction: rtl !important;
  text-align: right !important;
}

.form-group {
  direction: rtl !important;
  text-align: right !important;
}

.form-group label {
  direction: rtl !important;
  text-align: right !important;
}

.form-group input,
.form-group select,
.form-group textarea {
  direction: rtl !important;
  text-align: right !important;
}

/* Button RTL */
.btn {
  direction: rtl !important;
  text-align: center !important;
}

.btn-icon {
  order: 2 !important;
  margin-left: var(--space-xs) !important;
  margin-right: 0 !important;
}

/* Action buttons RTL */
.action-buttons {
  direction: rtl !important;
  flex-direction: row !important;
}

/* Filter RTL */
.filters-section {
  direction: rtl !important;
  text-align: right !important;
}

.search-box {
  direction: rtl !important;
  text-align: right !important;
}

.search-box input {
  direction: rtl !important;
  text-align: right !important;
  padding: 12px 16px 12px 40px !important;
}

.search-box .search-icon {
  right: 12px !important;
  left: auto !important;
}

.filter-tabs {
  direction: rtl !important;
  flex-direction: row !important;
}

.filter-tab {
  direction: rtl !important;
  text-align: center !important;
}

/* Modal RTL */
.modal {
  direction: rtl !important;
  text-align: right !important;
}

.modal-header {
  direction: rtl !important;
  text-align: right !important;
}

.modal-body {
  direction: rtl !important;
  text-align: right !important;
}

.modal-footer {
  direction: rtl !important;
  justify-content: flex-start !important;
}

/* Toast RTL */
.toast-container {
  right: var(--space-lg) !important;
  left: auto !important;
}

.toast {
  direction: rtl !important;
  text-align: right !important;
}

/* Mobile RTL */
@media (max-width: 768px) {
  .app-container {
    grid-template-areas: "header" "main" !important;
    grid-template-columns: 1fr !important;
  }
  
  .sidebar {
    right: -100% !important;
    left: auto !important;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1) !important;
  }
  
  .sidebar.open {
    right: 0 !important;
    left: auto !important;
  }
  
  .company-brand {
    text-align: center !important;
  }
  
  .page-header {
    flex-direction: column !important;
    align-items: flex-end !important;
  }
  
  .page-actions {
    width: 100% !important;
    justify-content: stretch !important;
  }
}
