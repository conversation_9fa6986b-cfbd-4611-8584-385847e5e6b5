/**
 * Data Abstraction Layer - Core Data Management System
 * Provides a unified interface for data operations regardless of storage backend
 */

import { EventBus } from './EventBus.js';

/**
 * Interface for data adapters
 * All adapters must implement these methods
 */
class IDataAdapter {
    async create(entity, data) {
        throw new Error('create method must be implemented');
    }

    async read(entity, id) {
        throw new Error('read method must be implemented');
    }

    async update(entity, id, data) {
        throw new Error('update method must be implemented');
    }

    async delete(entity, id) {
        throw new Error('delete method must be implemented');
    }

    async query(entity, filters = {}) {
        throw new Error('query method must be implemented');
    }

    async transaction(operations) {
        throw new Error('transaction method must be implemented');
    }

    async backup() {
        throw new Error('backup method must be implemented');
    }

    async restore(backupData) {
        throw new Error('restore method must be implemented');
    }
}

/**
 * LocalStorage Adapter Implementation
 * Handles all data operations using browser's localStorage
 */
class LocalStorageAdapter extends IDataAdapter {
    constructor() {
        super();
        this.storage = window.localStorage;
        this.indexes = new Map();
        this.initializeIndexes();
    }

    initializeIndexes() {
        // Initialize indexes for faster querying
        const entities = ['customers', 'products', 'invoices', 'workOrders', 'staff', 'inventory'];
        entities.forEach(entity => {
            this.buildIndex(entity);
        });
    }

    buildIndex(entity) {
        try {
            const data = this.getAllRecords(entity);
            const index = new Map();
            
            data.forEach(record => {
                // Index by ID
                index.set(record.id, record);
                
                // Additional indexes based on entity type
                switch (entity) {
                    case 'customers':
                        if (record.phone) index.set(`phone:${record.phone}`, record);
                        if (record.email) index.set(`email:${record.email}`, record);
                        break;
                    case 'products':
                        if (record.sku) index.set(`sku:${record.sku}`, record);
                        if (record.barcode) index.set(`barcode:${record.barcode}`, record);
                        break;
                    case 'invoices':
                        if (record.invoiceNumber) index.set(`invoiceNumber:${record.invoiceNumber}`, record);
                        break;
                }
            });
            
            this.indexes.set(entity, index);
        } catch (error) {
            console.warn(`Failed to build index for ${entity}:`, error);
            this.indexes.set(entity, new Map());
        }
    }

    getAllRecords(entity) {
        try {
            const data = this.storage.getItem(`almaher_${entity}`);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error reading ${entity} from localStorage:`, error);
            return [];
        }
    }

    saveAllRecords(entity, records) {
        try {
            this.storage.setItem(`almaher_${entity}`, JSON.stringify(records));
            this.buildIndex(entity); // Rebuild index after save
            return true;
        } catch (error) {
            console.error(`Error saving ${entity} to localStorage:`, error);
            throw new Error(`Failed to save ${entity}: ${error.message}`);
        }
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    validateData(entity, data) {
        // Basic validation - can be extended
        if (!data || typeof data !== 'object') {
            throw new Error('Data must be a valid object');
        }

        // Entity-specific validation
        switch (entity) {
            case 'customers':
                if (!data.name || !data.name.trim()) {
                    throw new Error('Customer name is required');
                }
                break;
            case 'products':
                if (!data.name || !data.name.trim()) {
                    throw new Error('Product name is required');
                }
                if (data.price !== undefined && (isNaN(data.price) || data.price < 0)) {
                    throw new Error('Product price must be a valid positive number');
                }
                break;
            case 'invoices':
                if (!data.customerId) {
                    throw new Error('Invoice must have a customer ID');
                }
                if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
                    throw new Error('Invoice must have at least one item');
                }
                break;
        }
    }

    async create(entity, data) {
        try {
            this.validateData(entity, data);
            
            const records = this.getAllRecords(entity);
            const newRecord = {
                ...data,
                id: this.generateId(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            records.push(newRecord);
            this.saveAllRecords(entity, records);
            
            return newRecord;
        } catch (error) {
            throw new Error(`Failed to create ${entity}: ${error.message}`);
        }
    }

    async read(entity, id) {
        try {
            const index = this.indexes.get(entity);
            if (index && index.has(id)) {
                return index.get(id);
            }
            
            // Fallback to full scan
            const records = this.getAllRecords(entity);
            return records.find(record => record.id === id) || null;
        } catch (error) {
            throw new Error(`Failed to read ${entity} with ID ${id}: ${error.message}`);
        }
    }

    async update(entity, id, data) {
        try {
            this.validateData(entity, data);
            
            const records = this.getAllRecords(entity);
            const index = records.findIndex(record => record.id === id);
            
            if (index === -1) {
                throw new Error(`${entity} with ID ${id} not found`);
            }
            
            const updatedRecord = {
                ...records[index],
                ...data,
                id, // Ensure ID doesn't change
                updatedAt: new Date().toISOString()
            };
            
            records[index] = updatedRecord;
            this.saveAllRecords(entity, records);
            
            return updatedRecord;
        } catch (error) {
            throw new Error(`Failed to update ${entity}: ${error.message}`);
        }
    }

    async delete(entity, id) {
        try {
            const records = this.getAllRecords(entity);
            const index = records.findIndex(record => record.id === id);
            
            if (index === -1) {
                throw new Error(`${entity} with ID ${id} not found`);
            }
            
            const deletedRecord = records[index];
            records.splice(index, 1);
            this.saveAllRecords(entity, records);
            
            return deletedRecord;
        } catch (error) {
            throw new Error(`Failed to delete ${entity}: ${error.message}`);
        }
    }

    async query(entity, filters = {}) {
        try {
            let records = this.getAllRecords(entity);
            
            // Apply filters
            Object.keys(filters).forEach(key => {
                const value = filters[key];
                
                if (value === undefined || value === null) return;
                
                records = records.filter(record => {
                    const recordValue = record[key];
                    
                    if (typeof value === 'string' && value.includes('*')) {
                        // Wildcard search
                        const regex = new RegExp(value.replace(/\*/g, '.*'), 'i');
                        return regex.test(recordValue);
                    } else if (typeof value === 'object' && value.operator) {
                        // Advanced filtering
                        switch (value.operator) {
                            case 'gt': return recordValue > value.value;
                            case 'gte': return recordValue >= value.value;
                            case 'lt': return recordValue < value.value;
                            case 'lte': return recordValue <= value.value;
                            case 'contains': return recordValue && recordValue.toString().toLowerCase().includes(value.value.toLowerCase());
                            case 'startsWith': return recordValue && recordValue.toString().toLowerCase().startsWith(value.value.toLowerCase());
                            case 'endsWith': return recordValue && recordValue.toString().toLowerCase().endsWith(value.value.toLowerCase());
                            default: return recordValue === value.value;
                        }
                    } else {
                        return recordValue === value;
                    }
                });
            });
            
            // Apply sorting if specified
            if (filters._sort) {
                const sortField = filters._sort;
                const sortOrder = filters._order || 'asc';
                
                records.sort((a, b) => {
                    const aVal = a[sortField];
                    const bVal = b[sortField];
                    
                    if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
                    if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
                    return 0;
                });
            }
            
            // Apply pagination if specified
            if (filters._limit) {
                const offset = filters._offset || 0;
                records = records.slice(offset, offset + filters._limit);
            }
            
            return records;
        } catch (error) {
            throw new Error(`Failed to query ${entity}: ${error.message}`);
        }
    }

    async transaction(operations) {
        // Simple transaction implementation for localStorage
        // In a real database, this would be atomic
        const backups = new Map();
        
        try {
            // Create backups before starting
            operations.forEach(op => {
                if (!backups.has(op.entity)) {
                    backups.set(op.entity, this.getAllRecords(op.entity));
                }
            });
            
            const results = [];
            
            // Execute operations
            for (const operation of operations) {
                const { type, entity, id, data } = operation;
                
                switch (type) {
                    case 'create':
                        results.push(await this.create(entity, data));
                        break;
                    case 'update':
                        results.push(await this.update(entity, id, data));
                        break;
                    case 'delete':
                        results.push(await this.delete(entity, id));
                        break;
                    default:
                        throw new Error(`Unknown operation type: ${type}`);
                }
            }
            
            return results;
        } catch (error) {
            // Rollback on error
            backups.forEach((backup, entity) => {
                this.saveAllRecords(entity, backup);
            });
            
            throw new Error(`Transaction failed: ${error.message}`);
        }
    }

    async backup() {
        try {
            const backup = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                data: {}
            };
            
            // Get all data from localStorage
            for (let i = 0; i < this.storage.length; i++) {
                const key = this.storage.key(i);
                if (key && key.startsWith('almaher_')) {
                    backup.data[key] = this.storage.getItem(key);
                }
            }
            
            return backup;
        } catch (error) {
            throw new Error(`Backup failed: ${error.message}`);
        }
    }

    async restore(backupData) {
        try {
            if (!backupData || !backupData.data) {
                throw new Error('Invalid backup data');
            }
            
            // Clear existing data
            const keysToRemove = [];
            for (let i = 0; i < this.storage.length; i++) {
                const key = this.storage.key(i);
                if (key && key.startsWith('almaher_')) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => this.storage.removeItem(key));
            
            // Restore data
            Object.keys(backupData.data).forEach(key => {
                this.storage.setItem(key, backupData.data[key]);
            });
            
            // Rebuild indexes
            this.initializeIndexes();
            
            return true;
        } catch (error) {
            throw new Error(`Restore failed: ${error.message}`);
        }
    }
}

/**
 * Main Data Manager Class
 * Provides a unified interface for all data operations
 */
class DataManager {
    constructor() {
        this.adapter = null;
        this.cache = new Map();
        this.eventBus = new EventBus();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        // Initialize with LocalStorage adapter by default
        this.setAdapter(new LocalStorageAdapter());
    }

    setAdapter(adapter) {
        if (!(adapter instanceof IDataAdapter)) {
            throw new Error('Adapter must implement IDataAdapter interface');
        }
        
        this.adapter = adapter;
        this.clearCache();
        this.eventBus.emit('adapter:changed', { adapter });
    }

    getCacheKey(entity, operation, params) {
        return `${entity}:${operation}:${JSON.stringify(params)}`;
    }

    setCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    getCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    clearCache(entity = null) {
        if (entity) {
            // Clear cache for specific entity
            for (const key of this.cache.keys()) {
                if (key.startsWith(`${entity}:`)) {
                    this.cache.delete(key);
                }
            }
        } else {
            // Clear all cache
            this.cache.clear();
        }
    }

    async create(entity, data) {
        try {
            const result = await this.adapter.create(entity, data);
            this.clearCache(entity);
            this.eventBus.emit('data:created', { entity, data: result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'create', entity, error });
            throw error;
        }
    }

    async read(entity, id) {
        try {
            const cacheKey = this.getCacheKey(entity, 'read', { id });
            const cached = this.getCache(cacheKey);
            
            if (cached) {
                return cached;
            }
            
            const result = await this.adapter.read(entity, id);
            
            if (result) {
                this.setCache(cacheKey, result);
            }
            
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'read', entity, id, error });
            throw error;
        }
    }

    async update(entity, id, data) {
        try {
            const result = await this.adapter.update(entity, id, data);
            this.clearCache(entity);
            this.eventBus.emit('data:updated', { entity, id, data: result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'update', entity, id, error });
            throw error;
        }
    }

    async delete(entity, id) {
        try {
            const result = await this.adapter.delete(entity, id);
            this.clearCache(entity);
            this.eventBus.emit('data:deleted', { entity, id, data: result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'delete', entity, id, error });
            throw error;
        }
    }

    async query(entity, filters = {}) {
        try {
            const cacheKey = this.getCacheKey(entity, 'query', filters);
            const cached = this.getCache(cacheKey);
            
            if (cached) {
                return cached;
            }
            
            const result = await this.adapter.query(entity, filters);
            this.setCache(cacheKey, result);
            
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'query', entity, filters, error });
            throw error;
        }
    }

    async transaction(operations) {
        try {
            const result = await this.adapter.transaction(operations);
            
            // Clear cache for all affected entities
            const entities = [...new Set(operations.map(op => op.entity))];
            entities.forEach(entity => this.clearCache(entity));
            
            this.eventBus.emit('data:transaction', { operations, result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'transaction', operations, error });
            throw error;
        }
    }

    async backup() {
        try {
            const result = await this.adapter.backup();
            this.eventBus.emit('data:backup', { result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'backup', error });
            throw error;
        }
    }

    async restore(backupData) {
        try {
            const result = await this.adapter.restore(backupData);
            this.clearCache();
            this.eventBus.emit('data:restore', { result });
            return result;
        } catch (error) {
            this.eventBus.emit('data:error', { operation: 'restore', error });
            throw error;
        }
    }

    // Event subscription methods
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }

    off(event, callback) {
        return this.eventBus.off(event, callback);
    }

    emit(event, data) {
        return this.eventBus.emit(event, data);
    }
}

// Export classes
export { DataManager, LocalStorageAdapter, IDataAdapter };
