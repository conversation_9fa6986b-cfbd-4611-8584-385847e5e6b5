/* ===== DASHBOARD & PAGES STYLES ===== */

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  direction: rtl;
  text-align: right;
}

.dashboard-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: #495057;
  margin: 0;
}

.dashboard-subtitle {
  color: #6c757d;
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
}

.dashboard-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: 8px 12px;
  background: #e8f5e8;
  border-radius: 20px;
  color: #28a745;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #28a745;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
  padding: 0 var(--space-lg);
}

.quick-action-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: var(--transition-all);
  text-align: center;
  cursor: pointer;
  border: 1px solid #e9ecef;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.quick-action-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-md);
  color: #007bff;
}

.quick-action-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: #495057;
  margin: 0;
}

/* Stats Cards Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
  padding: 0 var(--space-lg);
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
}

.stat-card.spending::before {
  --gradient-start: #ef4444;
  --gradient-end: #f97316;
}

.stat-card.revenue::before {
  --gradient-start: #22c55e;
  --gradient-end: #16a34a;
}

.stat-card.customers::before {
  --gradient-start: #3b82f6;
  --gradient-end: #1d4ed8;
}

.stat-card.orders::before {
  --gradient-start: #8b5cf6;
  --gradient-end: #7c3aed;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-md);
}

.stat-info h3 {
  font-size: var(--font-size-sm);
  color: #6b7280;
  margin: 0 0 var(--space-xs) 0;
  font-weight: var(--font-weight-medium);
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #1f2937;
  margin: 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: white;
}

.stat-card.spending .stat-icon {
  background: linear-gradient(135deg, #ef4444, #f97316);
}

.stat-card.revenue .stat-icon {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.stat-card.customers .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.stat-change.positive {
  color: #16a34a;
}

.stat-change.negative {
  color: #dc2626;
}

.stat-change-icon {
  font-size: var(--font-size-xs);
}

.stat-chart {
  height: 40px;
  margin-top: var(--space-md);
  position: relative;
  overflow: hidden;
}

.mini-chart {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: var(--border-radius-md);
  position: relative;
}

.chart-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  animation: chartGrow 2s ease-out;
}

/* Dashboard Content Grid */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-xl);
  padding: 0 var(--space-lg);
}

.main-charts {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.sidebar-widgets {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.chart-card,
.widget-card {
  background: white;
  border-radius: var(--border-radius-xl);
  padding: var(--space-lg);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.chart-header,
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.chart-title,
.widget-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: #1f2937;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: var(--space-sm);
}

.chart-control {
  padding: var(--space-xs) var(--space-sm);
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius-md);
  background: white;
  color: #6b7280;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-colors);
}

.chart-control:hover,
.chart-control.active {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes chartGrow {
  from { width: 0; }
  to { width: 100%; }
}

/* Chart Placeholders */
.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: var(--border-radius-lg);
  color: #64748b;
}

.chart-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: #1f2937;
  margin-top: var(--space-sm);
}

/* Gantt Chart Styles */
.gantt-placeholder {
  padding: var(--space-lg);
}

.gantt-row {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
  gap: var(--space-lg);
}

.gantt-label {
  min-width: 80px;
  font-size: var(--font-size-sm);
  color: #6b7280;
  font-weight: var(--font-weight-medium);
}

.gantt-bar {
  height: 8px;
  border-radius: var(--border-radius-full);
  position: relative;
  flex: 1;
}

.gantt-bar-1 {
  background: linear-gradient(90deg, #f59e0b, #d97706);
  width: 60%;
}

.gantt-bar-2 {
  background: linear-gradient(90deg, #10b981, #059669);
  width: 45%;
}

.gantt-bar-3 {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  width: 70%;
}

.gantt-bar-4 {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  width: 35%;
}

.gantt-bar-5 {
  background: linear-gradient(90deg, #ef4444, #dc2626);
  width: 25%;
}

/* Calendar Widget */
.calendar-mini {
  margin-bottom: var(--space-lg);
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
}

.calendar-header span {
  text-align: center;
  font-size: var(--font-size-xs);
  color: #6b7280;
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs);
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-xs);
}

.calendar-body span {
  text-align: center;
  padding: var(--space-sm);
  font-size: var(--font-size-sm);
  color: #374151;
  cursor: pointer;
  border-radius: var(--border-radius-md);
  transition: var(--transition-colors);
}

.calendar-body span:hover {
  background: #f3f4f6;
}

.calendar-body span.today {
  background: #6366f1;
  color: white;
  font-weight: var(--font-weight-semibold);
}

/* Events List */
.events-list {
  border-top: 1px solid #e5e7eb;
  padding-top: var(--space-lg);
}

.event-item {
  padding: var(--space-sm) 0;
  border-bottom: 1px solid #f3f4f6;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  font-size: var(--font-size-xs);
  color: #6b7280;
  margin-bottom: var(--space-xs);
}

.event-title {
  font-size: var(--font-size-sm);
  color: #374151;
  font-weight: var(--font-weight-medium);
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    padding: 0 var(--space-md);
  }

  .dashboard-content {
    padding: 0 var(--space-md);
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .page-actions {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .page-actions .btn {
    width: 100%;
  }

  .filter-tabs {
    flex-direction: column;
  }

  .filter-tab {
    text-align: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    min-width: 600px;
  }

  /* Reports Mobile */
  .category-tabs {
    flex-direction: column;
  }

  .category-tab {
    flex-direction: row;
    justify-content: flex-start;
    min-width: auto;
    text-align: right;
  }

  .chart-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }

  /* Settings Mobile */
  .settings-container {
    grid-template-columns: 1fr;
  }

  .settings-sidebar {
    order: 2;
  }

  .settings-content {
    order: 1;
  }

  .settings-tabs {
    flex-direction: row;
    overflow-x: auto;
    gap: var(--space-xs);
  }

  .settings-tab {
    flex-shrink: 0;
    min-width: 120px;
  }

  .backup-actions {
    grid-template-columns: 1fr;
  }

  .backup-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .backup-actions-small {
    align-self: stretch;
    justify-content: center;
  }
}

/* ===== PAGE HEADER ===== */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  direction: rtl;
  text-align: right;
}

.page-title-section h1 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: #495057;
  margin: 0 0 var(--space-xs) 0;
}

.page-title-section p {
  color: #6c757d;
  font-size: var(--font-size-sm);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

/* ===== TABLE STYLES ===== */
.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: var(--space-xl);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  direction: rtl;
  text-align: right;
}

.data-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: right;
  font-weight: var(--font-weight-medium);
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

.data-table .amount {
  font-weight: var(--font-weight-medium);
  text-align: left;
}

.data-table .amount.income {
  color: #28a745;
}

.data-table .amount.expense {
  color: #dc3545;
}

.data-table .amount.paid {
  color: #28a745;
}

.data-table .amount.pending {
  color: #ffc107;
}

/* ===== FILTER STYLES ===== */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-box {
  position: relative;
  margin-bottom: var(--space-md);
}

.search-box input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
}

.search-box .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.filter-tabs {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
  margin-bottom: var(--space-md);
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  background: white;
  color: #6c757d;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition-colors);
}

.filter-tab:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.filter-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.filter-row {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

/* ===== STATUS BADGES ===== */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.paid {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.partial {
  background: #cce5ff;
  color: #004085;
}

.status-badge.overdue {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.accepted {
  background: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.expired {
  background: #e2e3e5;
  color: #383d41;
}

.status-badge.converted {
  background: #d1ecf1;
  color: #0c5460;
}

.status-badge.approved {
  background: #d4edda;
  color: #155724;
}

.status-badge.ordered {
  background: #cce5ff;
  color: #004085;
}

.status-badge.received {
  background: #d4edda;
  color: #155724;
}

.status-badge.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.type-badge.income {
  background: #d4edda;
  color: #155724;
}

.type-badge.expense {
  background: #f8d7da;
  color: #721c24;
}

.type-badge.product {
  background: #cce5ff;
  color: #004085;
}

.type-badge.service {
  background: #fff3cd;
  color: #856404;
}

.method-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  background: #e9ecef;
  color: #495057;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: var(--transition-colors);
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.edit-btn:hover {
  background: #cce5ff;
  color: #004085;
}

.action-btn.delete-btn:hover {
  background: #f8d7da;
  color: #721c24;
}

.action-btn.view-btn:hover {
  background: #d4edda;
  color: #155724;
}

.action-btn.print-btn:hover {
  background: #fff3cd;
  color: #856404;
}

.action-btn.payment-btn:hover {
  background: #d1ecf1;
  color: #0c5460;
}

.action-btn.approve-btn:hover {
  background: #d4edda;
  color: #155724;
}

.action-btn.reject-btn:hover {
  background: #f8d7da;
  color: #721c24;
}

.action-btn.convert-btn:hover {
  background: #d1ecf1;
  color: #0c5460;
}

.action-btn.accept-btn:hover {
  background: #d4edda;
  color: #155724;
}

.action-btn.order-btn:hover {
  background: #cce5ff;
  color: #004085;
}

.action-btn.receive-btn:hover {
  background: #d4edda;
  color: #155724;
}

.action-btn.confirm-btn:hover {
  background: #d4edda;
  color: #155724;
}

/* ===== PAGINATION ===== */
.pagination {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  color: #6c757d;
  cursor: pointer;
  font-size: 14px;
  transition: var(--transition-colors);
}

.pagination-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.pagination-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* ===== EMPTY STATE ===== */
.empty-state {
  text-align: center;
  padding: var(--space-xl);
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.empty-state h3 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* ===== FORM STYLES ===== */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

.form-group {
  margin-bottom: var(--space-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-xs);
  font-weight: var(--font-weight-medium);
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: var(--transition-colors);
  direction: rtl;
  text-align: right;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* ===== STAT CARDS ENHANCEMENTS ===== */
.stat-card.income {
  border-left: 4px solid #28a745;
}

.stat-card.expense {
  border-left: 4px solid #dc3545;
}

.stat-card.profit {
  border-left: 4px solid #28a745;
}

.stat-card.loss {
  border-left: 4px solid #dc3545;
}

/* ===== PRODUCT CARDS ===== */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-lg);
  padding: 0 var(--space-lg);
}

.product-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: var(--transition-all);
  border: 1px solid #e9ecef;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-md);
}

.product-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.product-type.product {
  background: #cce5ff;
  color: #004085;
}

.product-type.service {
  background: #fff3cd;
  color: #856404;
}

.product-actions {
  display: flex;
  gap: 4px;
}

.product-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: #495057;
  margin: 0 0 var(--space-sm) 0;
}

.product-description {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: var(--space-md);
  line-height: 1.4;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-label {
  color: #6c757d;
}

.detail-value {
  font-weight: var(--font-weight-medium);
  color: #495057;
}

.detail-value.price {
  color: #28a745;
  font-weight: var(--font-weight-bold);
}

.detail-value.stock.low {
  color: #dc3545;
  font-weight: var(--font-weight-bold);
}

/* ===== CUSTOMER/SUPPLIER INFO ===== */
.customer-info,
.supplier-info {
  display: flex;
  flex-direction: column;
}

.customer-name,
.supplier-name {
  font-weight: var(--font-weight-medium);
  color: #495057;
  margin-bottom: 2px;
}

.customer-phone,
.supplier-phone {
  font-size: 12px;
  color: #6c757d;
}

/* ===== MODAL CONTENT STYLES ===== */
.invoice-details,
.quotation-details,
.request-details,
.payment-details {
  max-width: 100%;
}

.invoice-header,
.quotation-header,
.request-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid #e9ecef;
}

.customer-section,
.supplier-section,
.invoice-info,
.quotation-info,
.request-info,
.payment-info {
  background: #f8f9fa;
  padding: var(--space-md);
  border-radius: 8px;
}

.customer-section h4,
.supplier-section h4,
.invoice-info h4,
.quotation-info h4,
.request-info h4,
.payment-info h4 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
  font-size: var(--font-size-md);
}

.customer-section p,
.supplier-section p,
.invoice-info p,
.quotation-info p,
.request-info p,
.payment-info p {
  margin: 0 0 var(--space-xs) 0;
  font-size: 14px;
  color: #6c757d;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--space-md) 0;
}

.items-table th,
.items-table td {
  padding: 8px 12px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
}

.items-table th {
  background: #f8f9fa;
  font-weight: var(--font-weight-medium);
  color: #495057;
}

.invoice-totals,
.quotation-totals,
.request-totals {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid #e9ecef;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) 0;
  font-size: 14px;
}

.total-row.final {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: #495057;
  border-top: 1px solid #e9ecef;
  padding-top: var(--space-sm);
  margin-top: var(--space-sm);
}

.invoice-notes,
.quotation-notes,
.request-notes,
.payment-notes {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid #e9ecef;
}

.invoice-notes h4,
.quotation-notes h4,
.request-notes h4,
.payment-notes h4 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
}

.invoice-notes p,
.quotation-notes p,
.request-notes p,
.payment-notes p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

/* ===== PAYMENT FORM STYLES ===== */
.payment-form,
.product-form {
  max-width: 100%;
}

.invoice-summary {
  background: #f8f9fa;
  padding: var(--space-md);
  border-radius: 8px;
  margin-bottom: var(--space-lg);
}

.invoice-summary h4 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
}

.invoice-summary p {
  margin: 0 0 var(--space-xs) 0;
  font-size: 14px;
  color: #6c757d;
}

.payment-details .payment-amount {
  margin: var(--space-lg) 0;
}

.amount-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  border-radius: 8px;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.amount-display.income {
  background: #d4edda;
  color: #155724;
}

.amount-display.expense {
  background: #f8d7da;
  color: #721c24;
}

/* ===== REPORTS STYLES ===== */
.report-categories {
  margin-bottom: var(--space-xl);
}

.category-tabs {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
  background: white;
  padding: var(--space-lg);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  color: #6c757d;
  cursor: pointer;
  transition: var(--transition-all);
  min-width: 120px;
  text-align: center;
}

.category-tab:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.category-tab.active {
  border-color: #007bff;
  background: #e3f2fd;
  color: #007bff;
}

.category-tab .tab-icon {
  font-size: 2rem;
}

.category-tab .tab-text {
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

.report-content {
  margin-bottom: var(--space-xl);
}

.report-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.charts-section {
  margin-bottom: var(--space-xl);
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-lg);
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.chart-header h3 {
  margin: 0;
  color: #495057;
  font-size: var(--font-size-lg);
}

.chart-controls {
  display: flex;
  gap: var(--space-sm);
}

.chart-control {
  padding: 6px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition-colors);
}

.chart-control:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.chart-control.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #6c757d;
}

.chart-placeholder .chart-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.chart-placeholder p {
  margin: 0 0 var(--space-md) 0;
  font-size: 14px;
}

.chart-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: #28a745;
}

.chart-segments {
  display: flex;
  gap: 2px;
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
  margin-top: var(--space-md);
}

.segment {
  height: 100%;
}

.detailed-tables {
  margin-bottom: var(--space-xl);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.table-header h3 {
  margin: 0;
  color: #495057;
}

.table-placeholder {
  background: white;
  border-radius: 12px;
  padding: var(--space-xl);
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
  color: #6c757d;
}

.table-placeholder h3 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
}

.table-placeholder p {
  margin: 0 0 var(--space-lg) 0;
  color: #6c757d;
}

/* ===== SETTINGS STYLES ===== */
.settings-container {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: var(--space-lg);
  min-height: 600px;
}

.settings-sidebar {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: fit-content;
}

.settings-tabs {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: 12px var(--space-md);
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: var(--transition-colors);
  text-align: right;
  width: 100%;
}

.settings-tab:hover {
  background: #f8f9fa;
  color: #495057;
}

.settings-tab.active {
  background: #e3f2fd;
  color: #007bff;
}

.settings-tab .tab-icon {
  font-size: 18px;
}

.settings-tab .tab-text {
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

.settings-content {
  background: white;
  border-radius: 12px;
  padding: var(--space-xl);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.settings-section {
  max-width: 100%;
}

.section-header {
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.section-header h2 {
  margin: 0 0 var(--space-xs) 0;
  color: #495057;
  font-size: var(--font-size-xl);
}

.section-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.settings-form {
  max-width: 100%;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-text {
  user-select: none;
}

.form-help {
  display: block;
  margin-top: var(--space-xs);
  font-size: 12px;
  color: #6c757d;
}

/* Users Table */
.users-table {
  margin-top: var(--space-lg);
}

.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.role-badge.admin {
  background: #d4edda;
  color: #155724;
}

.role-badge.accountant {
  background: #cce5ff;
  color: #004085;
}

.role-badge.user {
  background: #e2e3e5;
  color: #383d41;
}

/* Backup Styles */
.backup-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.backup-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: var(--space-lg);
  text-align: center;
  transition: var(--transition-all);
}

.backup-card:hover {
  border-color: #007bff;
  background: white;
}

.backup-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.backup-card h3 {
  margin: 0 0 var(--space-sm) 0;
  color: #495057;
  font-size: var(--font-size-lg);
}

.backup-card p {
  margin: 0 0 var(--space-lg) 0;
  color: #6c757d;
  font-size: 14px;
}

.backup-history {
  background: #f8f9fa;
  border-radius: 12px;
  padding: var(--space-lg);
}

.backup-history h3 {
  margin: 0 0 var(--space-lg) 0;
  color: #495057;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.backup-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.backup-info strong {
  color: #495057;
  font-size: 14px;
}

.backup-date {
  color: #6c757d;
  font-size: 12px;
}

.backup-size {
  color: #6c757d;
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

.backup-actions-small {
  display: flex;
  gap: var(--space-sm);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
