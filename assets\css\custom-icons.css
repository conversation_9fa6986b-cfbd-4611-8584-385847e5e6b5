/* ===== CUSTOM ICONS FOR ALMAHER ===== */

/* Company Logo and Branding Icons */
.company-logo {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  background: linear-gradient(135deg, #007bff, #0056b3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.5rem;
}

.company-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 6px;
  position: relative;
  margin-left: 8px;
}

.company-icon::before {
  content: 'م';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 14px;
}

/* Navigation Icons - Enhanced */
.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: var(--transition-all);
}

/* Dashboard Icon */
.nav-icon.dashboard {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.nav-icon.dashboard::before {
  content: '📊';
}

/* Customers Icon */
.nav-icon.customers {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.nav-icon.customers::before {
  content: '👥';
}

/* Products Icon */
.nav-icon.products {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.nav-icon.products::before {
  content: '📦';
}

/* Invoices Icon */
.nav-icon.invoices {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
}

.nav-icon.invoices::before {
  content: '📄';
}

/* Quotations Icon */
.nav-icon.quotations {
  background: linear-gradient(135deg, #00BCD4, #0097A7);
  color: white;
}

.nav-icon.quotations::before {
  content: '📋';
}

/* Purchase Requests Icon */
.nav-icon.purchases {
  background: linear-gradient(135deg, #795548, #5D4037);
  color: white;
}

.nav-icon.purchases::before {
  content: '🛒';
}

/* Payments Icon */
.nav-icon.payments {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: white;
}

.nav-icon.payments::before {
  content: '💳';
}

/* Reports Icon */
.nav-icon.reports {
  background: linear-gradient(135deg, #E91E63, #C2185B);
  color: white;
}

.nav-icon.reports::before {
  content: '📊';
}

/* Settings Icon */
.nav-icon.settings {
  background: linear-gradient(135deg, #607D8B, #455A64);
  color: white;
}

.nav-icon.settings::before {
  content: '⚙️';
}

/* Status Icons */
.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.status-icon.active {
  background: #4CAF50;
  color: white;
}

.status-icon.active::before {
  content: '✓';
}

.status-icon.inactive {
  background: #F44336;
  color: white;
}

.status-icon.inactive::before {
  content: '✕';
}

.status-icon.pending {
  background: #FF9800;
  color: white;
}

.status-icon.pending::before {
  content: '⏳';
}

.status-icon.paid {
  background: #4CAF50;
  color: white;
}

.status-icon.paid::before {
  content: '💰';
}

.status-icon.overdue {
  background: #F44336;
  color: white;
}

.status-icon.overdue::before {
  content: '⚠️';
}

/* Action Icons */
.action-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: var(--transition-all);
  cursor: pointer;
  border: none;
  background: var(--bg-tertiary);
}

.action-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.action-icon.edit {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.action-icon.edit::before {
  content: '✏️';
}

.action-icon.delete {
  background: linear-gradient(135deg, #F44336, #D32F2F);
  color: white;
}

.action-icon.delete::before {
  content: '🗑️';
}

.action-icon.view {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: white;
}

.action-icon.view::before {
  content: '👁️';
}

.action-icon.print {
  background: linear-gradient(135deg, #607D8B, #455A64);
  color: white;
}

.action-icon.print::before {
  content: '🖨️';
}

.action-icon.download {
  background: linear-gradient(135deg, #00BCD4, #0097A7);
  color: white;
}

.action-icon.download::before {
  content: '📥';
}

/* Quick Action Icons */
.quick-action-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-md);
  display: block;
  text-align: center;
}

.quick-action-icon.add-customer::before {
  content: '👤➕';
}

.quick-action-icon.add-product::before {
  content: '📦➕';
}

.quick-action-icon.new-invoice::before {
  content: '📄➕';
}

.quick-action-icon.new-quotation::before {
  content: '📋➕';
}

.quick-action-icon.view-reports::before {
  content: '📊📈';
}

.quick-action-icon.settings::before {
  content: '⚙️🔧';
}

/* Maintenance Service Icons */
.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  font-size: 1.5rem;
  margin-bottom: var(--space-sm);
}

.service-icon.ac-maintenance {
  background: linear-gradient(135deg, #00BCD4, #0097A7);
  color: white;
}

.service-icon.ac-maintenance::before {
  content: '❄️';
}

.service-icon.electrical {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.service-icon.electrical::before {
  content: '⚡';
}

.service-icon.plumbing {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.service-icon.plumbing::before {
  content: '🔧';
}

.service-icon.general {
  background: linear-gradient(135deg, #607D8B, #455A64);
  color: white;
}

.service-icon.general::before {
  content: '🛠️';
}

/* Company Branding Elements */
.brand-accent {
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.brand-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-border {
  border: 2px solid var(--color-primary);
  border-radius: 8px;
}

.brand-shadow {
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Animated Icons */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

.icon-pulse {
  animation: pulse 2s infinite;
}

.icon-bounce {
  animation: bounce 1s infinite;
}

/* Hover Effects */
.nav-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.action-icon:hover {
  transform: translateY(-2px) scale(1.05);
}

.service-icon:hover {
  transform: rotate(5deg) scale(1.1);
}

/* Dark Theme Adjustments */
[data-theme="dark"] .company-icon {
  background: linear-gradient(135deg, #64B5F6, #42A5F5);
}

[data-theme="dark"] .action-icon {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
}

/* ===== NOTIFICATION STYLES ===== */
.notification-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  box-shadow: var(--shadow-neumorphic-modal);
  width: 350px;
  max-height: 500px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-normal);
  direction: rtl;
  text-align: right;
}

.notification-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
}

.notification-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.notification-actions {
  display: flex;
  gap: var(--space-xs);
}

.notification-actions button {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-colors);
}

.notification-actions button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
  padding: var(--space-xs);
}

.notification-item {
  display: flex;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-colors);
  margin-bottom: var(--space-xs);
  border: 1px solid transparent;
}

.notification-item:hover {
  background: var(--bg-hover);
}

.notification-item.unread {
  background: var(--bg-active);
  border-color: var(--color-primary);
}

.notification-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  width: 24px;
  text-align: center;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  font-size: 14px;
}

.notification-message {
  color: var(--text-secondary);
  font-size: 13px;
  line-height: 1.3;
  margin-bottom: var(--space-xs);
}

.notification-time {
  color: var(--text-muted);
  font-size: 11px;
}

.notification-item .notification-actions {
  flex-shrink: 0;
  opacity: 0;
  transition: var(--transition-normal);
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.notification-footer {
  padding: var(--space-md);
  border-top: 1px solid var(--border-light);
  text-align: center;
}

.view-all-btn {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: 14px;
  padding: var(--space-sm);
  border-radius: 6px;
  transition: var(--transition-colors);
}

.view-all-btn:hover {
  background: var(--bg-hover);
}

.empty-notifications {
  text-align: center;
  padding: var(--space-xl);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: var(--space-md);
}

/* All notifications modal */
.all-notifications-modal {
  max-width: 600px;
  width: 100%;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-light);
}

.notifications-filters {
  display: flex;
  gap: var(--space-sm);
}

.filter-btn {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 13px;
  transition: var(--transition-colors);
}

.filter-btn:hover {
  background: var(--bg-hover);
}

.filter-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.notifications-content {
  max-height: 400px;
  overflow-y: auto;
}

/* Theme Change Notification */
.theme-change-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: var(--space-xl);
  text-align: center;
  box-shadow: var(--shadow-neumorphic-modal);
  z-index: 10000;
  opacity: 0;
  transition: all 0.3s ease;
  min-width: 300px;
}

.theme-change-notification.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.theme-icon-large {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

.theme-change-notification h3 {
  margin: 0 0 var(--space-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.theme-change-notification p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .nav-icon {
    width: 20px;
    height: 20px;
    font-size: 1rem;
  }

  .action-icon {
    width: 28px;
    height: 28px;
  }

  .service-icon {
    width: 36px;
    height: 36px;
    font-size: 1.3rem;
  }

  .quick-action-icon {
    font-size: 2rem;
  }

  .notification-dropdown {
    width: 300px;
    right: -50px;
  }

  .all-notifications-modal {
    max-width: 95vw;
  }

  .notifications-header {
    flex-direction: column;
    gap: var(--space-md);
    align-items: stretch;
  }

  .notifications-filters {
    justify-content: center;
  }

  .theme-change-notification {
    min-width: 280px;
    padding: var(--space-lg);
  }

  .theme-icon-large {
    font-size: 2.5rem;
  }
}
