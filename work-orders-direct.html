<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أوامر الشغل - اختبار مباشر</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/work-orders.css">
    
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary, #f8f9fa);
        }
        .test-header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .loading { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🔧 أوامر الشغل - اختبار مباشر</h1>
        <div id="status">⏳ جاري التحميل...</div>
    </div>
    
    <div id="work-orders-container">
        <!-- سيتم عرض أوامر الشغل هنا -->
    </div>

    <!-- تحميل الملف مباشرة -->
    <script type="module">
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        async function loadWorkOrders() {
            try {
                updateStatus('🔄 محاولة تحميل وحدة أوامر الشغل...', 'loading');
                
                // Import the module
                const module = await import('/js/components/WorkOrderManagement.js');
                updateStatus('✅ تم تحميل الوحدة بنجاح', 'success');
                
                // Get the class
                const WorkOrderManagement = module.default;
                if (!WorkOrderManagement) {
                    throw new Error('WorkOrderManagement class not found in module');
                }
                
                updateStatus('🔧 إنشاء instance من WorkOrderManagement...', 'loading');
                
                // Create instance
                const workOrderManager = new WorkOrderManagement();
                updateStatus('✅ تم إنشاء instance بنجاح', 'success');
                
                // Get container
                const container = document.getElementById('work-orders-container');
                if (!container) {
                    throw new Error('Container not found');
                }
                
                updateStatus('🎨 عرض أوامر الشغل...', 'loading');
                
                // Render work orders
                workOrderManager.render(container);
                
                updateStatus(`✅ تم عرض ${workOrderManager.workOrders.length} أوامر شغل بنجاح`, 'success');
                
                // Make globally available for debugging
                window.workOrderManager = workOrderManager;
                
            } catch (error) {
                updateStatus(`❌ خطأ: ${error.message}`, 'error');
                console.error('Error loading work orders:', error);
                
                // Show fallback content
                const container = document.getElementById('work-orders-container');
                container.innerHTML = `
                    <div style="background: white; padding: 30px; border-radius: 12px; text-align: center;">
                        <h2>❌ فشل في تحميل أوامر الشغل</h2>
                        <p><strong>الخطأ:</strong> ${error.message}</p>
                        <p><strong>الحل:</strong> تأكد من فتح الصفحة من الخادم المحلي:</p>
                        <code style="background: #f8f9fa; padding: 10px; border-radius: 4px; display: block; margin: 10px 0;">
                            http://localhost:64091/work-orders-direct.html
                        </code>
                        <button onclick="location.reload()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
        
        // Start loading when page is ready
        document.addEventListener('DOMContentLoaded', loadWorkOrders);
    </script>
</body>
</html>
