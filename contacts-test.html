<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جهات الاتصال</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/work-orders.css">
    <link rel="stylesheet" href="assets/css/contacts.css">
    
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary, #f8f9fa);
        }
        .test-header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-tab {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .test-tab:hover {
            background: #0056b3;
        }
        .test-tab.active {
            background: #28a745;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .loading { background: #fff3cd; color: #856404; }
        .test-content {
            display: none;
        }
        .test-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>📞 اختبار جهات الاتصال</h1>
        
        <div class="test-tabs">
            <button class="test-tab active" onclick="showTab('customers')">العملاء</button>
            <button class="test-tab" onclick="showTab('suppliers')">الموردين</button>
            <button class="test-tab" onclick="showTab('contacts')">جهات الاتصال العامة</button>
        </div>
        
        <div id="status">⏳ جاري التحميل...</div>
    </div>
    
    <!-- العملاء -->
    <div id="customers-content" class="test-content active">
        <div id="customers-container"></div>
    </div>
    
    <!-- الموردين -->
    <div id="suppliers-content" class="test-content">
        <div id="suppliers-container"></div>
    </div>
    
    <!-- جهات الاتصال العامة -->
    <div id="contacts-content" class="test-content">
        <div id="contacts-container"></div>
    </div>

    <script>
        let customerManager = null;
        let supplierManager = null;
        let contactManager = null;
        let currentTab = 'customers';

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const colors = {
                info: { bg: '#d1ecf1', color: '#0c5460' },
                success: { bg: '#d4edda', color: '#155724' },
                error: { bg: '#f8d7da', color: '#721c24' },
                loading: { bg: '#fff3cd', color: '#856404' }
            };
            statusDiv.style.background = colors[type].bg;
            statusDiv.style.color = colors[type].color;
            statusDiv.innerHTML = message;
        }

        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.test-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('.test-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabName}-content`).classList.add('active');
            
            currentTab = tabName;
            
            // Load content if not loaded
            if (tabName === 'customers' && !customerManager) {
                loadCustomers();
            } else if (tabName === 'suppliers' && !supplierManager) {
                loadSuppliers();
            } else if (tabName === 'contacts' && !contactManager) {
                loadContacts();
            }
        }

        // Simple Customer Management Class
        class SimpleCustomerManagement {
            constructor() {
                this.customers = [
                    {
                        id: 'CUST-001',
                        name: 'أحمد محمد العلي',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        address: 'حي النزهة، الرياض',
                        customerType: 'فرد',
                        status: 'active'
                    },
                    {
                        id: 'CUST-002',
                        name: 'شركة التقنية المتقدمة',
                        phone: '0112345678',
                        email: '<EMAIL>',
                        address: 'طريق الملك فهد، الرياض',
                        customerType: 'شركة',
                        status: 'active'
                    }
                ];
            }

            render(container) {
                container.innerHTML = `
                    <div class="customer-management">
                        <div class="page-header">
                            <div class="page-title">
                                <h1><span class="page-icon">👤</span> إدارة العملاء</h1>
                                <p class="page-description">إدارة وتتبع جميع العملاء</p>
                            </div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card active">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.customers.filter(c => c.status === 'active').length}</div>
                                    <div class="stat-label">عملاء نشطين</div>
                                </div>
                            </div>
                            <div class="stat-card total">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.customers.length}</div>
                                    <div class="stat-label">إجمالي العملاء</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contacts-section">
                            <div class="section-header">
                                <h2>قائمة العملاء</h2>
                            </div>
                            <div class="contacts-grid">
                                ${this.customers.map(customer => `
                                    <div class="contact-card">
                                        <div class="card-header">
                                            <div class="card-id">${customer.id}</div>
                                            <div class="card-badges">
                                                <span class="status-badge active">✅ نشط</span>
                                            </div>
                                        </div>
                                        <div class="card-content">
                                            <h3 class="contact-name">${customer.name}</h3>
                                            <div class="contact-type">${customer.customerType}</div>
                                            <div class="card-details">
                                                <div class="detail-item">
                                                    <span class="detail-icon">📞</span>
                                                    <span class="detail-text">${customer.phone}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-icon">📧</span>
                                                    <span class="detail-text">${customer.email}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-icon">📍</span>
                                                    <span class="detail-text">${customer.address}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-primary">عرض</button>
                                            <button class="btn btn-sm btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Simple Supplier Management Class
        class SimpleSupplierManagement {
            constructor() {
                this.suppliers = [
                    {
                        id: 'SUP-001',
                        name: 'شركة المواد الكهربائية',
                        contactPerson: 'أحمد السالم',
                        phone: '0112345678',
                        email: '<EMAIL>',
                        supplierType: 'مورد مواد',
                        status: 'active',
                        currentBalance: 15000
                    },
                    {
                        id: 'SUP-002',
                        name: 'مؤسسة قطع الغيار',
                        contactPerson: 'فاطمة عبدالله',
                        phone: '0509876543',
                        email: '<EMAIL>',
                        supplierType: 'مورد قطع غيار',
                        status: 'active',
                        currentBalance: 8500
                    }
                ];
            }

            render(container) {
                container.innerHTML = `
                    <div class="supplier-management">
                        <div class="page-header">
                            <div class="page-title">
                                <h1><span class="page-icon">🏢</span> إدارة الموردين</h1>
                                <p class="page-description">إدارة وتتبع جميع الموردين</p>
                            </div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card active">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.suppliers.filter(s => s.status === 'active').length}</div>
                                    <div class="stat-label">موردين نشطين</div>
                                </div>
                            </div>
                            <div class="stat-card balance">
                                <div class="stat-icon">💰</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.suppliers.reduce((sum, s) => sum + s.currentBalance, 0).toLocaleString()}</div>
                                    <div class="stat-label">إجمالي الأرصدة</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="suppliers-section">
                            <div class="section-header">
                                <h2>قائمة الموردين</h2>
                            </div>
                            <div class="suppliers-grid">
                                ${this.suppliers.map(supplier => `
                                    <div class="supplier-card">
                                        <div class="card-header">
                                            <div class="card-id">${supplier.id}</div>
                                            <div class="card-badges">
                                                <span class="status-badge active">✅ نشط</span>
                                            </div>
                                        </div>
                                        <div class="card-content">
                                            <h3 class="supplier-name">${supplier.name}</h3>
                                            <div class="supplier-type">${supplier.supplierType}</div>
                                            <div class="contact-person">👤 ${supplier.contactPerson}</div>
                                            <div class="card-details">
                                                <div class="detail-item">
                                                    <span class="detail-icon">📞</span>
                                                    <span class="detail-text">${supplier.phone}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-icon">📧</span>
                                                    <span class="detail-text">${supplier.email}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-icon">💰</span>
                                                    <span class="detail-text">الرصيد: ${supplier.currentBalance.toLocaleString()} ر.س</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-primary">عرض</button>
                                            <button class="btn btn-sm btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Simple Contact Management Class
        class SimpleContactManagement {
            constructor() {
                this.contacts = [
                    {
                        id: 'CON-001',
                        name: 'شركة الرياض للتطوير',
                        contactPerson: 'سعد الراشد',
                        position: 'مدير المشاريع',
                        phone: '0112345678',
                        email: '<EMAIL>',
                        contactType: 'شركة',
                        category: 'عميل محتمل',
                        status: 'potential'
                    },
                    {
                        id: 'CON-002',
                        name: 'مكتب الهندسة المتقدمة',
                        contactPerson: 'م. فاطمة الزهراني',
                        position: 'مهندسة معمارية',
                        phone: '0126789012',
                        email: '<EMAIL>',
                        contactType: 'مكتب',
                        category: 'شريك تجاري',
                        status: 'active'
                    }
                ];
            }

            render(container) {
                container.innerHTML = `
                    <div class="contact-management">
                        <div class="page-header">
                            <div class="page-title">
                                <h1><span class="page-icon">📞</span> إدارة جهات الاتصال العامة</h1>
                                <p class="page-description">إدارة وتتبع جميع جهات الاتصال</p>
                            </div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card active">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.contacts.filter(c => c.status === 'active').length}</div>
                                    <div class="stat-label">جهات نشطة</div>
                                </div>
                            </div>
                            <div class="stat-card potential">
                                <div class="stat-icon">🎯</div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.contacts.filter(c => c.status === 'potential').length}</div>
                                    <div class="stat-label">عملاء محتملين</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contacts-section">
                            <div class="section-header">
                                <h2>قائمة جهات الاتصال</h2>
                            </div>
                            <div class="contacts-grid">
                                ${this.contacts.map(contact => `
                                    <div class="contact-card">
                                        <div class="card-header">
                                            <div class="card-id">${contact.id}</div>
                                            <div class="card-badges">
                                                <span class="status-badge ${contact.status}">
                                                    ${contact.status === 'active' ? '✅ نشط' : '🎯 محتمل'}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-content">
                                            <h3 class="contact-name">${contact.name}</h3>
                                            <div class="contact-type">${contact.contactType} - ${contact.category}</div>
                                            <div class="contact-person">👤 ${contact.contactPerson}</div>
                                            <div class="contact-position">${contact.position}</div>
                                            <div class="card-details">
                                                <div class="detail-item">
                                                    <span class="detail-icon">📞</span>
                                                    <span class="detail-text">${contact.phone}</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-icon">📧</span>
                                                    <span class="detail-text">${contact.email}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-primary">عرض</button>
                                            <button class="btn btn-sm btn-secondary">تعديل</button>
                                            <button class="btn btn-sm btn-outline">اتصال</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function loadCustomers() {
            try {
                updateStatus('🔧 تحميل إدارة العملاء...', 'loading');
                customerManager = new SimpleCustomerManagement();
                const container = document.getElementById('customers-container');
                customerManager.render(container);
                updateStatus('✅ تم تحميل العملاء بنجاح', 'success');
            } catch (error) {
                updateStatus(`❌ خطأ في تحميل العملاء: ${error.message}`, 'error');
            }
        }

        function loadSuppliers() {
            try {
                updateStatus('🏢 تحميل إدارة الموردين...', 'loading');
                supplierManager = new SimpleSupplierManagement();
                const container = document.getElementById('suppliers-container');
                supplierManager.render(container);
                updateStatus('✅ تم تحميل الموردين بنجاح', 'success');
            } catch (error) {
                updateStatus(`❌ خطأ في تحميل الموردين: ${error.message}`, 'error');
            }
        }

        function loadContacts() {
            try {
                updateStatus('📞 تحميل إدارة جهات الاتصال...', 'loading');
                contactManager = new SimpleContactManagement();
                const container = document.getElementById('contacts-container');
                contactManager.render(container);
                updateStatus('✅ تم تحميل جهات الاتصال بنجاح', 'success');
            } catch (error) {
                updateStatus(`❌ خطأ في تحميل جهات الاتصال: ${error.message}`, 'error');
            }
        }

        // تشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadCustomers();
        });
    </script>
</body>
</html>
