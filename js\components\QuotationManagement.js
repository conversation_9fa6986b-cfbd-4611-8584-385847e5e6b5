/**
 * Quotation Management Component
 * إدارة عروض الأسعار
 */
class QuotationManagement {
    constructor() {
        this.container = null;
        this.quotations = [];
        this.filteredQuotations = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.selectedStatus = 'all';
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.loadQuotations();
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Load quotations data
     */
    loadQuotations() {
        // Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.quotations = [
            {
                id: 'QUO-001',
                customerName: 'خالد أحمد',
                customerPhone: '0501234567',
                customerEmail: '<EMAIL>',
                date: '2025-01-15',
                validUntil: '2025-02-15',
                total: 850.00,
                status: 'pending',
                items: [
                    { name: 'صيانة مكيف سبليت', quantity: 3, price: 150, total: 450 },
                    { name: 'تنظيف مكيف شباك', quantity: 5, price: 80, total: 400 }
                ],
                notes: 'عرض سعر شامل للصيانة الدورية'
            },
            {
                id: 'QUO-002',
                customerName: 'نورا سعد',
                customerPhone: '0507654321',
                customerEmail: '<EMAIL>',
                date: '2025-01-14',
                validUntil: '2025-02-14',
                total: 320.00,
                status: 'accepted',
                items: [
                    { name: 'تركيب مكيف جديد', quantity: 1, price: 200, total: 200 },
                    { name: 'ريموت كنترول', quantity: 2, price: 45, total: 90 },
                    { name: 'فلتر مكيف', quantity: 1, price: 25, total: 25 }
                ],
                notes: 'تركيب في الدور الثاني'
            },
            {
                id: 'QUO-003',
                customerName: 'عبدالرحمن محمد',
                customerPhone: '0551234567',
                customerEmail: '<EMAIL>',
                date: '2025-01-13',
                validUntil: '2025-01-13',
                total: 600.00,
                status: 'expired',
                items: [
                    { name: 'صيانة مكيف سبليت', quantity: 4, price: 150, total: 600 }
                ],
                notes: 'صيانة شاملة لجميع المكيفات'
            },
            {
                id: 'QUO-004',
                customerName: 'ريم علي',
                customerPhone: '0561234567',
                customerEmail: '<EMAIL>',
                date: '2025-01-12',
                validUntil: '2025-02-12',
                total: 280.00,
                status: 'rejected',
                items: [
                    { name: 'تنظيف مكيف شباك', quantity: 2, price: 80, total: 160 },
                    { name: 'فلتر مكيف', quantity: 4, price: 25, total: 100 },
                    { name: 'ريموت كنترول', quantity: 1, price: 45, total: 45 }
                ],
                notes: 'العميل رفض العرض'
            },
            {
                id: 'QUO-005',
                customerName: 'سلطان خالد',
                customerPhone: '0571234567',
                customerEmail: '<EMAIL>',
                date: '2025-01-11',
                validUntil: '2025-02-11',
                total: 750.00,
                status: 'converted',
                items: [
                    { name: 'تركيب مكيف جديد', quantity: 3, price: 200, total: 600 },
                    { name: 'صيانة مكيف سبليت', quantity: 1, price: 150, total: 150 }
                ],
                notes: 'تم تحويل العرض إلى فاتورة'
            }
        ];
        
        this.filteredQuotations = [...this.quotations];
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">إدارة عروض الأسعار</h1>
                        <p class="page-subtitle">إنشاء ومتابعة عروض الأسعار للعملاء</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-quotation-btn">
                            <span class="btn-icon">➕</span>
                            عرض سعر جديد
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    ${this.renderStatsCards()}
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="search-box">
                        <input type="text" id="quotation-search" placeholder="البحث في عروض الأسعار..." value="${this.searchTerm}">
                        <span class="search-icon">🔍</span>
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab ${this.selectedStatus === 'all' ? 'active' : ''}" data-status="all">
                            الكل (${this.quotations.length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'pending' ? 'active' : ''}" data-status="pending">
                            معلقة (${this.quotations.filter(q => q.status === 'pending').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'accepted' ? 'active' : ''}" data-status="accepted">
                            مقبولة (${this.quotations.filter(q => q.status === 'accepted').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'rejected' ? 'active' : ''}" data-status="rejected">
                            مرفوضة (${this.quotations.filter(q => q.status === 'rejected').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'expired' ? 'active' : ''}" data-status="expired">
                            منتهية الصلاحية (${this.quotations.filter(q => q.status === 'expired').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'converted' ? 'active' : ''}" data-status="converted">
                            محولة لفاتورة (${this.quotations.filter(q => q.status === 'converted').length})
                        </button>
                    </div>
                </div>

                <!-- Quotations Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم العرض</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>صالح حتى</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.renderQuotationRows()}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                ${this.renderPagination()}
            </div>
        `;
    }

    /**
     * Render stats cards
     */
    renderStatsCards() {
        const totalQuotations = this.quotations.length;
        const totalValue = this.quotations.reduce((sum, quo) => sum + quo.total, 0);
        const acceptedQuotations = this.quotations.filter(q => q.status === 'accepted').length;
        const pendingQuotations = this.quotations.filter(q => q.status === 'pending').length;
        const conversionRate = totalQuotations > 0 ? ((acceptedQuotations / totalQuotations) * 100).toFixed(1) : 0;

        return `
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-content">
                    <div class="stat-value">${totalQuotations}</div>
                    <div class="stat-label">إجمالي العروض</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-value">${totalValue.toFixed(2)} ر.س</div>
                    <div class="stat-label">قيمة العروض</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <div class="stat-value">${acceptedQuotations}</div>
                    <div class="stat-label">عروض مقبولة</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <div class="stat-value">${pendingQuotations}</div>
                    <div class="stat-label">عروض معلقة</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <div class="stat-value">${conversionRate}%</div>
                    <div class="stat-label">معدل القبول</div>
                </div>
            </div>
        `;
    }

    /**
     * Render quotation rows
     */
    renderQuotationRows() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedQuotations = this.filteredQuotations.slice(startIndex, endIndex);

        if (paginatedQuotations.length === 0) {
            return `
                <tr>
                    <td colspan="7" class="empty-state">
                        <div class="empty-icon">📋</div>
                        <h3>لا توجد عروض أسعار</h3>
                        <p>لم يتم العثور على عروض أسعار تطابق البحث</p>
                    </td>
                </tr>
            `;
        }

        return paginatedQuotations.map(quotation => {
            const statusText = {
                'pending': 'معلقة',
                'accepted': 'مقبولة',
                'rejected': 'مرفوضة',
                'expired': 'منتهية الصلاحية',
                'converted': 'محولة لفاتورة'
            };

            const isExpired = new Date(quotation.validUntil) < new Date() && quotation.status === 'pending';

            return `
                <tr data-quotation-id="${quotation.id}">
                    <td>
                        <div class="quotation-id">
                            <strong>${quotation.id}</strong>
                        </div>
                    </td>
                    <td>
                        <div class="customer-info">
                            <div class="customer-name">${quotation.customerName}</div>
                            <div class="customer-phone">${quotation.customerPhone}</div>
                        </div>
                    </td>
                    <td>${this.formatDate(quotation.date)}</td>
                    <td class="${isExpired ? 'expired' : ''}">${this.formatDate(quotation.validUntil)}</td>
                    <td class="amount">${quotation.total.toFixed(2)} ر.س</td>
                    <td>
                        <span class="status-badge ${quotation.status}">${statusText[quotation.status]}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" data-action="view" data-id="${quotation.id}" title="عرض">
                                👁️
                            </button>
                            <button class="action-btn edit-btn" data-action="edit" data-id="${quotation.id}" title="تعديل">
                                ✏️
                            </button>
                            <button class="action-btn print-btn" data-action="print" data-id="${quotation.id}" title="طباعة">
                                🖨️
                            </button>
                            ${quotation.status === 'accepted' ? `
                                <button class="action-btn convert-btn" data-action="convert" data-id="${quotation.id}" title="تحويل لفاتورة">
                                    🔄
                                </button>
                            ` : ''}
                            ${quotation.status === 'pending' ? `
                                <button class="action-btn accept-btn" data-action="accept" data-id="${quotation.id}" title="قبول">
                                    ✅
                                </button>
                                <button class="action-btn reject-btn" data-action="reject" data-id="${quotation.id}" title="رفض">
                                    ❌
                                </button>
                            ` : ''}
                            <button class="action-btn delete-btn" data-action="delete" data-id="${quotation.id}" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const totalPages = Math.ceil(this.filteredQuotations.length / this.itemsPerPage);
        
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="pagination-btn active">${i}</button>`;
            } else {
                paginationHTML += `<button class="pagination-btn" data-page="${i}">${i}</button>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }
        
        paginationHTML += '</div>';
        return paginationHTML;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Search functionality
        const searchInput = this.container.querySelector('#quotation-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.filterQuotations();
            });
        }

        // Status filters
        const filterTabs = this.container.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.selectedStatus = e.target.dataset.status;
                this.filterQuotations();
            });
        });

        // Quotation actions
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const quotationId = e.target.dataset.id;

            switch (action) {
                case 'view':
                    this.viewQuotation(quotationId);
                    break;
                case 'edit':
                    this.editQuotation(quotationId);
                    break;
                case 'print':
                    this.printQuotation(quotationId);
                    break;
                case 'accept':
                    this.acceptQuotation(quotationId);
                    break;
                case 'reject':
                    this.rejectQuotation(quotationId);
                    break;
                case 'convert':
                    this.convertToInvoice(quotationId);
                    break;
                case 'delete':
                    this.deleteQuotation(quotationId);
                    break;
            }
        });

        // Add quotation button
        const addBtn = this.container.querySelector('#add-quotation-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addQuotation());
        }

        // Pagination
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn') && e.target.dataset.page) {
                this.currentPage = parseInt(e.target.dataset.page);
                this.renderContent();
                this.attachEventListeners();
            }
        });
    }

    /**
     * Filter quotations
     */
    filterQuotations() {
        this.filteredQuotations = this.quotations.filter(quotation => {
            const matchesSearch = quotation.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                quotation.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                quotation.customerPhone.includes(this.searchTerm);
            const matchesStatus = this.selectedStatus === 'all' || quotation.status === this.selectedStatus;
            
            return matchesSearch && matchesStatus;
        });

        this.currentPage = 1;
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * View quotation details
     */
    viewQuotation(quotationId) {
        const quotation = this.quotations.find(q => q.id === quotationId);
        if (!quotation) return;

        const itemsHTML = quotation.items.map(item => `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.price.toFixed(2)} ر.س</td>
                <td>${item.total.toFixed(2)} ر.س</td>
            </tr>
        `).join('');

        window.uiManager?.showModal({
            title: `تفاصيل عرض السعر ${quotation.id}`,
            content: `
                <div class="quotation-details">
                    <div class="quotation-header">
                        <div class="customer-section">
                            <h4>بيانات العميل</h4>
                            <p><strong>الاسم:</strong> ${quotation.customerName}</p>
                            <p><strong>الهاتف:</strong> ${quotation.customerPhone}</p>
                            <p><strong>البريد الإلكتروني:</strong> ${quotation.customerEmail}</p>
                        </div>
                        <div class="quotation-info">
                            <h4>بيانات العرض</h4>
                            <p><strong>التاريخ:</strong> ${this.formatDate(quotation.date)}</p>
                            <p><strong>صالح حتى:</strong> ${this.formatDate(quotation.validUntil)}</p>
                            <p><strong>الحالة:</strong> <span class="status-badge ${quotation.status}">${this.getStatusText(quotation.status)}</span></p>
                        </div>
                    </div>
                    
                    <div class="quotation-items">
                        <h4>تفاصيل الأصناف</h4>
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itemsHTML}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="quotation-totals">
                        <div class="total-row final">
                            <span>الإجمالي:</span>
                            <span>${quotation.total.toFixed(2)} ر.س</span>
                        </div>
                    </div>
                    
                    ${quotation.notes ? `
                        <div class="quotation-notes">
                            <h4>ملاحظات</h4>
                            <p>${quotation.notes}</p>
                        </div>
                    ` : ''}
                </div>
            `,
            size: 'large',
            actions: [
                {
                    text: 'إغلاق',
                    class: 'btn-secondary',
                    action: 'close'
                },
                {
                    text: 'طباعة',
                    class: 'btn-primary',
                    action: 'print',
                    handler: () => this.printQuotation(quotationId)
                }
            ]
        });
    }

    /**
     * Add new quotation
     */
    addQuotation() {
        window.uiManager?.showToast('سيتم إضافة صفحة إنشاء عروض الأسعار قريباً', 'info');
    }

    /**
     * Edit quotation
     */
    editQuotation(quotationId) {
        window.uiManager?.showToast('سيتم إضافة صفحة تعديل عروض الأسعار قريباً', 'info');
    }

    /**
     * Print quotation
     */
    printQuotation(quotationId) {
        window.uiManager?.showToast('سيتم إضافة وظيفة الطباعة قريباً', 'info');
    }

    /**
     * Accept quotation
     */
    acceptQuotation(quotationId) {
        const quotation = this.quotations.find(q => q.id === quotationId);
        if (!quotation) return;

        quotation.status = 'accepted';
        this.filterQuotations();
        window.uiManager?.showToast('تم قبول عرض السعر', 'success');
    }

    /**
     * Reject quotation
     */
    rejectQuotation(quotationId) {
        const quotation = this.quotations.find(q => q.id === quotationId);
        if (!quotation) return;

        quotation.status = 'rejected';
        this.filterQuotations();
        window.uiManager?.showToast('تم رفض عرض السعر', 'info');
    }

    /**
     * Convert to invoice
     */
    convertToInvoice(quotationId) {
        const quotation = this.quotations.find(q => q.id === quotationId);
        if (!quotation) return;

        window.uiManager?.showConfirmation({
            title: 'تحويل لفاتورة',
            message: `هل تريد تحويل عرض السعر "${quotation.id}" إلى فاتورة؟`,
            confirmText: 'تحويل',
            confirmClass: 'btn-primary',
            onConfirm: () => {
                quotation.status = 'converted';
                this.filterQuotations();
                window.uiManager?.showToast('تم تحويل عرض السعر إلى فاتورة بنجاح', 'success');
            }
        });
    }

    /**
     * Delete quotation
     */
    deleteQuotation(quotationId) {
        const quotation = this.quotations.find(q => q.id === quotationId);
        if (!quotation) return;

        window.uiManager?.showConfirmation({
            title: 'حذف عرض السعر',
            message: `هل أنت متأكد من حذف عرض السعر "${quotation.id}"؟`,
            confirmText: 'حذف',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.quotations = this.quotations.filter(q => q.id !== quotationId);
                this.filterQuotations();
                window.uiManager?.showToast('تم حذف عرض السعر بنجاح', 'success');
            }
        });
    }

    /**
     * Format date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    /**
     * Get status text
     */
    getStatusText(status) {
        const statusText = {
            'pending': 'معلقة',
            'accepted': 'مقبولة',
            'rejected': 'مرفوضة',
            'expired': 'منتهية الصلاحية',
            'converted': 'محولة لفاتورة'
        };
        return statusText[status] || status;
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.QuotationManagement = QuotationManagement;
console.log('📊 QuotationManagement class loaded and made globally available');

// Export for use in other modules
export default QuotationManagement;
