# الماهر للصيانة والخدمات - نظام إدارة الخدمات والمبيعات

## 🌟 نظرة عامة

نظام إدارة شامل للخدمات والمبيعات مصمم خصيصاً للشركات العربية، يتميز بواجهة مستخدم عربية كاملة مع دعم RTL وتصميم Neumorphic حديث.

### ✨ الميزات الرئيسية

- **🎨 تصميم Neumorphic حديث** مع دعم المظهر الفاتح والداكن
- **🌙 نظام تبديل المظاهر** مع انتقالات سلسة
- **📱 تصميم متجاوب** يعمل على جميع الأجهزة
- **🔄 واجهة عربية كاملة** مع دعم RTL
- **💾 نظام تخزين محلي** مع إمكانية التوسع للخادم
- **🏗️ معمارية معيارية** قابلة للصيانة والتطوير
- **⚡ أداء عالي** مع تحميل سريع
- **🔒 نظام أمان متقدم** مع تشفير البيانات

## 🏗️ المعمارية

### طبقة البيانات المجردة (Data Abstraction Layer)

```
┌─────────────────────────────────────┐
│           UI Components             │
├─────────────────────────────────────┤
│         Business Logic              │
├─────────────────────────────────────┤
│      Data Abstraction Layer        │
├─────────────────────────────────────┤
│  LocalStorage │  Future API        │
│   Adapter     │   Adapter          │
└─────────────────────────────────────┘
```

### المكونات الأساسية

- **EventBus**: نظام إدارة الأحداث المركزي
- **DataManager**: إدارة البيانات مع دعم التخزين المحلي والخادم
- **Router**: نظام التوجيه للتطبيق أحادي الصفحة
- **ThemeManager**: إدارة المظاهر والألوان
- **UIManager**: إدارة عناصر الواجهة (Modals, Toasts, etc.)

## 📁 هيكل المشروع

```
almaher-erp/
├── index.html                 # الصفحة الرئيسية
├── manifest.json             # ملف PWA
├── README.md                 # هذا الملف
├── assets/
│   ├── css/
│   │   ├── variables.css     # متغيرات CSS
│   │   ├── base.css         # الأنماط الأساسية
│   │   ├── components.css   # مكونات الواجهة
│   │   ├── layout.css       # تخطيط الصفحة
│   │   ├── themes.css       # أنماط المظاهر
│   │   └── rtl.css         # دعم RTL
│   └── images/             # الصور والأيقونات
└── js/
    ├── app.js              # نقطة دخول التطبيق
    ├── core/               # المكونات الأساسية
    │   ├── EventBus.js
    │   ├── DataManager.js
    │   ├── Router.js
    │   ├── ThemeManager.js
    │   └── UIManager.js
    └── components/         # مكونات الواجهة
        ├── Dashboard.js
        └── NotFound.js
```

## 🚀 التشغيل السريع

### 1. تحميل المشروع

```bash
git clone [repository-url]
cd almaher-erp
```

### 2. تشغيل الخادم المحلي

```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx serve .

# أو باستخدام PHP
php -S localhost:8000
```

### 3. فتح التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:8000`

## 📋 الوحدات والميزات

### 🏠 لوحة التحكم الرئيسية
- عرض المقاييس الرئيسية
- النشاطات الأخيرة
- الإجراءات السريعة
- المهام المعلقة

### 👥 إدارة جهات الاتصال
- **العملاء**: إدارة بيانات العملاء
- **الموردين**: إدارة بيانات الموردين
- **جهات الاتصال العامة**: دليل الاتصالات

### 💰 إدارة المبيعات
- **الفواتير**: إنشاء وإدارة الفواتير
- **إشعارات دائنة**: إدارة المرتجعات
- **عروض الأسعار**: إنشاء العروض
- **طلبات الشراء**: إدارة الطلبات
- **المدفوعات**: تتبع المدفوعات

### 📦 إدارة المنتجات والخدمات
- كتالوج المنتجات
- إدارة الأسعار
- تصنيف المنتجات

### 🛍️ إدارة المشتريات
- فواتير الشراء
- مرتجعات الشراء
- أوامر الشراء
- مدفوعات الشراء

### 📋 إدارة المخزون
- قائمة المنتجات/الخدمات
- الرصيد الافتتاحي
- حركات المخزون
- تحويلات المخزون
- جرد المخزون
- قوائم الأسعار

### 🏦 الإدارة المالية
- الحسابات البنكية وصناديق النقد
- المصروفات
- الأصول الثابتة
- سندات قبض نقدي
- دفتر اليومية العام
- دليل الحسابات

### 🔧 أوامر العمل
- إنشاء أوامر العمل
- تتبع حالة الأعمال
- إدارة الفنيين
- جدولة المهام

### 📈 التقارير والتحليلات
- تقارير المبيعات
- تقارير المشتريات
- تقارير المخزون
- التقارير المالية
- التقارير الضريبية
- سجل النشاطات

### ⚙️ الإعدادات
- إعدادات الحساب
- إعدادات الفواتير
- إعدادات الضرائب
- إدارة المستخدمين
- إدارة الفروع
- قوالب الطباعة
- التكاملات وربط API
- النسخ الاحتياطي والاستعادة
- التخصيص

## 🎨 نظام التصميم

### الألوان الأساسية
- **الأساسي**: #2196F3 (أزرق)
- **الثانوي**: #FF9800 (برتقالي)
- **النجاح**: #10B981 (أخضر)
- **التحذير**: #F59E0B (أصفر)
- **الخطأ**: #EF4444 (أحمر)

### المظاهر
- **المظهر الفاتح**: خلفية فاتحة مع ظلال ناعمة
- **المظهر الداكن**: خلفية داكنة مع تباين عالي

### تصميم Neumorphic
- ظلال ناعمة ومتدرجة
- تأثيرات بصرية ثلاثية الأبعاد
- انتقالات سلسة
- تفاعل بصري متقدم

## 🔧 التطوير

### إضافة مكون جديد

```javascript
// js/components/NewComponent.js
class NewComponent {
    constructor(params = {}) {
        this.params = params;
        this.container = null;
    }

    async render() {
        this.container = document.createElement('div');
        this.container.className = 'new-component';
        
        this.container.innerHTML = `
            <div class="component-content">
                <!-- محتوى المكون -->
            </div>
        `;
        
        this.setupEventListeners();
        return this.container;
    }

    setupEventListeners() {
        // إعداد مستمعي الأحداث
    }

    async init() {
        // تهيئة المكون
    }

    destroy() {
        if (this.container) {
            this.container.remove();
        }
    }
}

export default NewComponent;
```

### إضافة مسار جديد

```javascript
// في Router.js
this.addRoute('/new-route', {
    component: 'NewComponent',
    title: 'عنوان الصفحة',
    icon: '🆕'
});
```

### استخدام DataManager

```javascript
// إنشاء بيانات جديدة
const newCustomer = await dataManager.create('customers', {
    name: 'اسم العميل',
    phone: '0501234567',
    email: '<EMAIL>'
});

// قراءة البيانات
const customer = await dataManager.read('customers', customerId);

// تحديث البيانات
const updatedCustomer = await dataManager.update('customers', customerId, {
    name: 'الاسم المحدث'
});

// حذف البيانات
await dataManager.delete('customers', customerId);

// البحث والاستعلام
const customers = await dataManager.query('customers', {
    name: { operator: 'contains', value: 'أحمد' }
});
```

## 🔒 الأمان

### تشفير البيانات
- تشفير البيانات الحساسة في التخزين المحلي
- حماية من XSS و CSRF
- تنظيف المدخلات

### إدارة الصلاحيات
- نظام أدوار المستخدمين
- تحكم في الوصول للميزات
- تسجيل العمليات

## 📱 دعم PWA

التطبيق يدعم Progressive Web App مع:
- تثبيت على الجهاز
- العمل بدون اتصال
- إشعارات push
- تحديثات تلقائية

## 🌐 دعم المتصفحات

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للتفاصيل.

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: **********
- الموقع: almaher-est.com

---

**الماهر للصيانة والخدمات** - حلول تقنية متقدمة لإدارة الأعمال
