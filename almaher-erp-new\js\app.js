/**
 * نظام الماهر للصيانة والخدمات ERP
 * الملف الرئيسي للتطبيق
 */

// ========== إعدادات التطبيق ========== 
const APP_CONFIG = {
    name: 'الماهر للصيانة والخدمات',
    version: '1.0.0',
    description: 'نظام إدارة موارد المؤسسة',
    company: {
        name: 'الماهر للصيانة والخدمات',
        phone: '0112345678',
        mobile: '0501234567',
        email: '<EMAIL>',
        address: 'الرياض، المملكة العربية السعودية',
        taxNumber: '300123456789003',
        website: 'www.almaher-services.com'
    },
    routes: {
        '/': 'الرئيسية',
        '/customers': 'العملاء',
        '/suppliers': 'الموردين', 
        '/contacts': 'جهات الاتصال',
        '/products': 'المنتجات',
        '/invoices': 'الفواتير',
        '/quotations': 'عروض الأسعار',
        '/purchases': 'المشتريات',
        '/payments': 'المدفوعات',
        '/work-orders': 'أوامر الشغل',
        '/reports': 'التقارير',
        '/settings': 'الإعدادات'
    }
};

// ========== فئة التطبيق الرئيسية ==========
class AlmaherERP {
    constructor() {
        this.currentRoute = '/';
        this.currentTheme = 'light';
        this.sidebarOpen = true;
        this.notifications = [];
        this.data = {};
        
        console.log('🚀 تم تهيئة نظام الماهر للصيانة والخدمات');
    }

    // ========== تهيئة التطبيق ==========
    async init() {
        try {
            console.log('🔧 بدء تهيئة النظام...');
            
            // إخفاء شاشة التحميل
            await this.hideLoadingScreen();
            
            // تهيئة المكونات
            this.initializeTheme();
            this.initializeNavigation();
            this.initializeEventListeners();
            this.initializeData();
            
            // تحميل الصفحة الأولى
            this.navigate(this.getCurrentRoute());
            
            console.log('✅ تم تهيئة النظام بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة النظام:', error);
            this.showError('حدث خطأ في تهيئة النظام');
        }
    }

    // ========== إخفاء شاشة التحميل ==========
    async hideLoadingScreen() {
        return new Promise(resolve => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                const app = document.getElementById('app');
                
                if (loadingScreen && app) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        app.style.display = 'grid';
                        resolve();
                    }, 500);
                } else {
                    resolve();
                }
            }, 1500); // عرض شاشة التحميل لمدة 1.5 ثانية
        });
    }

    // ========== تهيئة الثيم ==========
    initializeTheme() {
        // قراءة الثيم المحفوظ
        const savedTheme = localStorage.getItem('almaher_theme') || 'light';
        this.setTheme(savedTheme);
        
        // إعداد منتقي الثيم
        const themeToggle = document.getElementById('theme-toggle');
        const themeDropdown = document.getElementById('theme-dropdown');
        
        if (themeToggle && themeDropdown) {
            themeToggle.addEventListener('click', () => {
                themeDropdown.classList.toggle('show');
            });
            
            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!themeToggle.contains(e.target) && !themeDropdown.contains(e.target)) {
                    themeDropdown.classList.remove('show');
                }
            });
            
            // معالجة اختيار الثيم
            themeDropdown.addEventListener('click', (e) => {
                const themeOption = e.target.closest('.theme-option');
                if (themeOption) {
                    const theme = themeOption.dataset.theme;
                    this.setTheme(theme);
                    themeDropdown.classList.remove('show');
                }
            });
        }
    }

    // ========== تعيين الثيم ==========
    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('almaher_theme', theme);
        
        // تحديث الثيم النشط في القائمة
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });
        
        console.log(`🎨 تم تغيير الثيم إلى: ${theme}`);
    }

    // ========== تهيئة التنقل ==========
    initializeNavigation() {
        this.renderNavigation();
        
        // معالجة تغيير الهاش
        window.addEventListener('hashchange', () => {
            this.navigate(this.getCurrentRoute());
        });
        
        // معالجة زر القائمة للشاشات الصغيرة
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
                this.sidebarOpen = !this.sidebarOpen;
            });
        }
    }

    // ========== رسم التنقل ==========
    renderNavigation() {
        const sidebarNav = document.getElementById('sidebar-nav');
        if (!sidebarNav) return;

        const navItems = [
            { path: '/', icon: '🏠', text: 'الرئيسية' },
            { path: '/customers', icon: '👥', text: 'العملاء' },
            { path: '/suppliers', icon: '🏢', text: 'الموردين' },
            { path: '/contacts', icon: '📞', text: 'جهات الاتصال' },
            { path: '/products', icon: '📦', text: 'المنتجات' },
            { path: '/invoices', icon: '🧾', text: 'الفواتير' },
            { path: '/quotations', icon: '💰', text: 'عروض الأسعار' },
            { path: '/purchases', icon: '🛒', text: 'المشتريات' },
            { path: '/payments', icon: '💳', text: 'المدفوعات' },
            { path: '/work-orders', icon: '🔧', text: 'أوامر الشغل' },
            { path: '/reports', icon: '📊', text: 'التقارير' },
            { path: '/settings', icon: '⚙️', text: 'الإعدادات' }
        ];

        const navHTML = `
            <ul class="nav-menu">
                ${navItems.map(item => `
                    <li class="nav-item">
                        <a href="#${item.path}" class="nav-link" data-route="${item.path}">
                            <span class="nav-icon">${item.icon}</span>
                            <span class="nav-text">${item.text}</span>
                        </a>
                    </li>
                `).join('')}
            </ul>
        `;

        sidebarNav.innerHTML = navHTML;
    }

    // ========== الحصول على المسار الحالي ==========
    getCurrentRoute() {
        const hash = window.location.hash;
        return hash ? hash.substring(1) : '/';
    }

    // ========== التنقل ==========
    navigate(route) {
        console.log(`🧭 الانتقال إلى: ${route}`);
        
        this.currentRoute = route;
        
        // تحديث الهاش
        if (window.location.hash !== `#${route}`) {
            window.location.hash = route;
        }
        
        // تحديث التنقل النشط
        this.updateActiveNavigation(route);
        
        // تحميل المحتوى
        this.loadContent(route);
    }

    // ========== تحديث التنقل النشط ==========
    updateActiveNavigation(route) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const linkRoute = link.dataset.route;
            link.classList.toggle('active', linkRoute === route);
        });
    }

    // ========== تحميل المحتوى ==========
    loadContent(route) {
        const container = document.getElementById('page-container');
        if (!container) return;

        // عرض مؤشر التحميل
        container.innerHTML = '<div class="loading">جاري التحميل...</div>';

        // محاكاة تحميل المحتوى
        setTimeout(() => {
            let content = '';
            
            switch (route) {
                case '/':
                    content = this.renderDashboard();
                    break;
                case '/customers':
                    content = this.renderCustomers();
                    break;
                case '/suppliers':
                    content = this.renderSuppliers();
                    break;
                case '/contacts':
                    content = this.renderContacts();
                    break;
                case '/products':
                    content = this.renderProducts();
                    break;
                case '/invoices':
                    content = this.renderInvoices();
                    break;
                case '/quotations':
                    content = this.renderQuotations();
                    break;
                case '/purchases':
                    content = this.renderPurchases();
                    break;
                case '/payments':
                    content = this.renderPayments();
                    break;
                case '/work-orders':
                    content = this.renderWorkOrders();
                    break;
                case '/reports':
                    content = this.renderReports();
                    break;
                case '/settings':
                    content = this.renderSettings();
                    break;
                default:
                    content = this.render404();
            }
            
            container.innerHTML = content;
            
        }, 300);
    }

    // ========== تهيئة مستمعي الأحداث ==========
    initializeEventListeners() {
        // معالجة الإشعارات
        const notificationToggle = document.getElementById('notification-toggle');
        const notificationDropdown = document.getElementById('notification-dropdown');
        
        if (notificationToggle && notificationDropdown) {
            notificationToggle.addEventListener('click', () => {
                notificationDropdown.classList.toggle('show');
            });
        }
        
        // إغلاق القوائم المنسدلة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.theme-selector')) {
                document.getElementById('theme-dropdown')?.classList.remove('show');
            }
            if (!e.target.closest('.notifications')) {
                document.getElementById('notification-dropdown')?.classList.remove('show');
            }
        });
    }

    // ========== تهيئة البيانات ==========
    initializeData() {
        // تحميل البيانات من localStorage أو إنشاء بيانات تجريبية
        this.loadData();
    }

    // ========== تحميل البيانات ==========
    loadData() {
        // محاولة تحميل البيانات المحفوظة
        try {
            const savedData = localStorage.getItem('almaher_data');
            if (savedData) {
                this.data = JSON.parse(savedData);
            } else {
                this.createSampleData();
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.createSampleData();
        }
    }

    // ========== إنشاء بيانات تجريبية ==========
    createSampleData() {
        this.data = {
            customers: [],
            suppliers: [],
            contacts: [],
            products: [],
            invoices: [],
            quotations: [],
            purchases: [],
            payments: [],
            workOrders: [],
            settings: {
                company: APP_CONFIG.company,
                theme: 'light',
                language: 'ar'
            }
        };
        
        this.saveData();
        console.log('📊 تم إنشاء بيانات تجريبية');
    }

    // ========== حفظ البيانات ==========
    saveData() {
        try {
            localStorage.setItem('almaher_data', JSON.stringify(this.data));
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
        }
    }

    // ========== عرض خطأ ==========
    showError(message) {
        console.error(message);
        // يمكن إضافة نظام إشعارات هنا
    }

    // ========== رسم لوحة التحكم ==========
    renderDashboard() {
        return `
            <div class="page-header">
                <h1>🏠 لوحة التحكم</h1>
                <p>مرحباً بك في نظام الماهر للصيانة والخدمات</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">العملاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏢</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">الموردين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📦</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">المنتجات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🧾</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">الفواتير</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">الأنشطة الأخيرة</h2>
                </div>
                <div class="card-body">
                    <p>لا توجد أنشطة حتى الآن</p>
                </div>
            </div>
        `;
    }

    // ========== رسم صفحة العملاء ==========
    renderCustomers() {
        return `
            <div class="page-header">
                <h1>👥 إدارة العملاء</h1>
                <p>إدارة وتتبع جميع العملاء والزبائن</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">قائمة العملاء</h2>
                    <div class="card-actions">
                        <button class="btn">➕ عميل جديد</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <h3>لا يوجد عملاء</h3>
                        <p>ابدأ بإضافة عملائك الأوائل</p>
                        <button class="btn">إضافة عميل جديد</button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========== رسم صفحة الموردين ==========
    renderSuppliers() {
        return `
            <div class="page-header">
                <h1>🏢 إدارة الموردين</h1>
                <p>إدارة وتتبع جميع الموردين والشركاء</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">قائمة الموردين</h2>
                    <div class="card-actions">
                        <button class="btn">➕ مورد جديد</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">🏢</div>
                        <h3>لا يوجد موردين</h3>
                        <p>ابدأ بإضافة الموردين والشركاء</p>
                        <button class="btn">إضافة مورد جديد</button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========== رسم صفحة جهات الاتصال ==========
    renderContacts() {
        return `
            <div class="page-header">
                <h1>📞 جهات الاتصال</h1>
                <p>إدارة جميع جهات الاتصال والأشخاص المهمين</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">دليل جهات الاتصال</h2>
                    <div class="card-actions">
                        <button class="btn">➕ جهة اتصال جديدة</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">📞</div>
                        <h3>لا توجد جهات اتصال</h3>
                        <p>ابدأ بإضافة جهات الاتصال المهمة</p>
                        <button class="btn">إضافة جهة اتصال</button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========== رسم صفحة المنتجات ==========
    renderProducts() {
        return `
            <div class="page-header">
                <h1>📦 إدارة المنتجات</h1>
                <p>إدارة كتالوج المنتجات والخدمات</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">كتالوج المنتجات</h2>
                    <div class="card-actions">
                        <button class="btn">➕ منتج جديد</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">📦</div>
                        <h3>لا توجد منتجات</h3>
                        <p>ابدأ بإضافة منتجاتك وخدماتك</p>
                        <button class="btn">إضافة منتج جديد</button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========== رسم صفحة الفواتير ==========
    renderInvoices() {
        return `
            <div class="page-header">
                <h1>🧾 إدارة الفواتير</h1>
                <p>إنشاء وإدارة جميع الفواتير</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">قائمة الفواتير</h2>
                    <div class="card-actions">
                        <button class="btn">➕ فاتورة جديدة</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">🧾</div>
                        <h3>لا توجد فواتير</h3>
                        <p>ابدأ بإنشاء فاتورتك الأولى</p>
                        <button class="btn">إنشاء فاتورة جديدة</button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========== رسم باقي الصفحات ==========
    renderQuotations() {
        return this.renderPlaceholder('💰', 'عروض الأسعار', 'إنشاء وإدارة عروض الأسعار للعملاء');
    }

    renderPurchases() {
        return this.renderPlaceholder('🛒', 'المشتريات', 'إدارة طلبات الشراء والمشتريات');
    }

    renderPayments() {
        return this.renderPlaceholder('💳', 'المدفوعات', 'تتبع وإدارة جميع المدفوعات');
    }

    renderWorkOrders() {
        return this.renderPlaceholder('🔧', 'أوامر الشغل', 'إدارة أوامر الصيانة والخدمات');
    }

    renderReports() {
        return this.renderPlaceholder('📊', 'التقارير', 'عرض التقارير والإحصائيات');
    }

    renderSettings() {
        return this.renderPlaceholder('⚙️', 'الإعدادات', 'إعدادات النظام والشركة');
    }

    render404() {
        return `
            <div class="empty-state">
                <div class="empty-icon">❌</div>
                <h3>الصفحة غير موجودة</h3>
                <p>عذراً، الصفحة التي تبحث عنها غير موجودة</p>
                <button class="btn" onclick="app.navigate('/')">العودة للرئيسية</button>
            </div>
        `;
    }

    // ========== رسم صفحة مؤقتة ==========
    renderPlaceholder(icon, title, description) {
        return `
            <div class="page-header">
                <h1>${icon} ${title}</h1>
                <p>${description}</p>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-icon">${icon}</div>
                        <h3>${title}</h3>
                        <p>هذه الصفحة قيد التطوير وستكون متاحة قريباً</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// ========== تهيئة التطبيق عند تحميل الصفحة ==========
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📄 تم تحميل الصفحة - بدء تشغيل النظام...');

    // إنشاء مثيل التطبيق
    window.app = new AlmaherERP();

    // تهيئة التطبيق
    await window.app.init();

    console.log('🎉 تم تشغيل النظام بنجاح!');
});
