/**
 * Theme Manager - Handles Dark/Light Theme Toggle with Neumorphic Design
 * Manages theme state, persistence, and dynamic CSS variable updates
 */

import { EventBus } from './EventBus.js';

class ThemeManager {
    constructor() {
        this.eventBus = new EventBus();
        this.currentTheme = 'light';
        this.storageKey = 'almaher_theme_preference';
        this.systemPreferenceQuery = null;
        this.initialized = false;
        
        // Theme configurations
        this.themes = {
            light: {
                name: 'light',
                displayName: 'المظهر الفاتح',
                icon: '☀️',
                className: 'theme-light'
            },
            dark: {
                name: 'dark',
                displayName: 'المظهر الداكن',
                icon: '🌙',
                className: 'theme-dark'
            }
        };
        
        this.init();
    }

    /**
     * Initialize theme manager
     */
    init() {
        if (this.initialized) return;
        
        try {
            // Set up system preference detection
            this.setupSystemPreferenceDetection();
            
            // Load saved theme or detect system preference
            this.loadTheme();
            
            // Set up theme toggle button
            this.setupThemeToggle();
            
            // Apply initial theme
            this.applyTheme(this.currentTheme);
            
            this.initialized = true;
            this.eventBus.emit('theme:initialized', { theme: this.currentTheme });
            
        } catch (error) {
            console.error('ThemeManager: Failed to initialize:', error);
            // Fallback to light theme
            this.applyTheme('light');
        }
    }

    /**
     * Set up system preference detection
     */
    setupSystemPreferenceDetection() {
        if (window.matchMedia) {
            this.systemPreferenceQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            // Listen for system preference changes
            this.systemPreferenceQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't set a preference
                const savedTheme = localStorage.getItem(this.storageKey);
                if (!savedTheme) {
                    const newTheme = e.matches ? 'dark' : 'light';
                    this.setTheme(newTheme, false); // Don't save to localStorage
                }
            });
        }
    }

    /**
     * Load theme from localStorage or detect system preference
     */
    loadTheme() {
        try {
            const savedTheme = localStorage.getItem(this.storageKey);
            
            if (savedTheme && this.themes[savedTheme]) {
                this.currentTheme = savedTheme;
            } else {
                // Detect system preference
                this.currentTheme = this.getSystemPreference();
            }
        } catch (error) {
            console.warn('ThemeManager: Failed to load theme from localStorage:', error);
            this.currentTheme = 'light';
        }
    }

    /**
     * Get system color scheme preference
     */
    getSystemPreference() {
        if (this.systemPreferenceQuery && this.systemPreferenceQuery.matches) {
            return 'dark';
        }
        return 'light';
    }

    /**
     * Set up theme toggle button
     */
    setupThemeToggle() {
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            // Update button appearance
            this.updateToggleButton(toggleButton);
        }
    }

    /**
     * Update theme toggle button appearance
     */
    updateToggleButton(button) {
        if (!button) return;
        
        const lightIcon = button.querySelector('.light-icon');
        const darkIcon = button.querySelector('.dark-icon');
        
        if (lightIcon && darkIcon) {
            if (this.currentTheme === 'light') {
                lightIcon.style.display = 'none';
                darkIcon.style.display = 'inline';
                button.setAttribute('aria-label', 'تبديل إلى المظهر الداكن');
            } else {
                lightIcon.style.display = 'inline';
                darkIcon.style.display = 'none';
                button.setAttribute('aria-label', 'تبديل إلى المظهر الفاتح');
            }
        }
        
        // Add neumorphic effect to button
        button.classList.remove('neumorphic-pressed');
        setTimeout(() => {
            button.classList.add('neumorphic-pressed');
            setTimeout(() => {
                button.classList.remove('neumorphic-pressed');
            }, 150);
        }, 50);
    }

    /**
     * Apply theme to the document
     */
    applyTheme(themeName, animate = true) {
        if (!this.themes[themeName]) {
            console.warn(`ThemeManager: Unknown theme "${themeName}"`);
            return;
        }

        const theme = this.themes[themeName];
        const body = document.body;
        
        // Add transition class for smooth animation
        if (animate) {
            body.classList.add('theme-transitioning');
        }
        
        // Remove all theme classes
        Object.values(this.themes).forEach(t => {
            body.classList.remove(t.className);
        });
        
        // Add new theme class
        body.classList.add(theme.className);
        
        // Set data attribute for CSS selectors
        body.setAttribute('data-theme', themeName);
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(themeName);
        
        // Update toggle button
        const toggleButton = document.getElementById('theme-toggle');
        this.updateToggleButton(toggleButton);
        
        // Remove transition class after animation
        if (animate) {
            setTimeout(() => {
                body.classList.remove('theme-transitioning');
            }, 300);
        }
        
        // Emit theme change event
        this.eventBus.emit('theme:changed', { 
            theme: themeName, 
            previousTheme: this.currentTheme 
        });
        
        this.currentTheme = themeName;
    }

    /**
     * Update meta theme-color for mobile browsers
     */
    updateMetaThemeColor(themeName) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            const colors = {
                light: '#F5F7FA',
                dark: '#1A202C'
            };
            metaThemeColor.setAttribute('content', colors[themeName] || colors.light);
        }
    }

    /**
     * Set theme and optionally save preference
     */
    setTheme(themeName, savePreference = true) {
        if (!this.themes[themeName]) {
            console.warn(`ThemeManager: Unknown theme "${themeName}"`);
            return;
        }

        this.applyTheme(themeName);
        
        if (savePreference) {
            this.saveThemePreference(themeName);
        }
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        
        // Emit toggle event
        this.eventBus.emit('theme:toggled', { 
            from: this.currentTheme === 'light' ? 'dark' : 'light',
            to: newTheme 
        });
    }

    /**
     * Save theme preference to localStorage
     */
    saveThemePreference(themeName) {
        try {
            localStorage.setItem(this.storageKey, themeName);
        } catch (error) {
            console.warn('ThemeManager: Failed to save theme preference:', error);
        }
    }

    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get theme configuration
     */
    getThemeConfig(themeName = null) {
        if (themeName) {
            return this.themes[themeName] || null;
        }
        return this.themes;
    }

    /**
     * Check if dark theme is active
     */
    isDarkTheme() {
        return this.currentTheme === 'dark';
    }

    /**
     * Check if light theme is active
     */
    isLightTheme() {
        return this.currentTheme === 'light';
    }

    /**
     * Get CSS custom property value for current theme
     */
    getCSSVariable(propertyName) {
        return getComputedStyle(document.documentElement)
            .getPropertyValue(propertyName)
            .trim();
    }

    /**
     * Set CSS custom property value
     */
    setCSSVariable(propertyName, value) {
        document.documentElement.style.setProperty(propertyName, value);
    }

    /**
     * Apply custom theme colors (for advanced customization)
     */
    applyCustomColors(colorOverrides) {
        Object.entries(colorOverrides).forEach(([property, value]) => {
            this.setCSSVariable(property, value);
        });
        
        this.eventBus.emit('theme:custom-colors-applied', { colorOverrides });
    }

    /**
     * Reset to default theme colors
     */
    resetToDefaultColors() {
        // Remove any custom CSS variables
        const style = document.documentElement.style;
        const customProperties = [];
        
        for (let i = 0; i < style.length; i++) {
            const property = style[i];
            if (property.startsWith('--')) {
                customProperties.push(property);
            }
        }
        
        customProperties.forEach(property => {
            style.removeProperty(property);
        });
        
        this.eventBus.emit('theme:colors-reset');
    }

    /**
     * Get contrast ratio between two colors (for accessibility)
     */
    getContrastRatio(color1, color2) {
        // Simplified contrast ratio calculation
        // In a real implementation, you'd want a more robust color parsing library
        const getLuminance = (color) => {
            // This is a simplified version - you'd want proper color parsing
            const rgb = color.match(/\d+/g);
            if (!rgb || rgb.length < 3) return 0.5;
            
            const [r, g, b] = rgb.map(x => {
                x = parseInt(x) / 255;
                return x <= 0.03928 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);
            });
            
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        };
        
        const lum1 = getLuminance(color1);
        const lum2 = getLuminance(color2);
        const brightest = Math.max(lum1, lum2);
        const darkest = Math.min(lum1, lum2);
        
        return (brightest + 0.05) / (darkest + 0.05);
    }

    /**
     * Check if current theme meets accessibility standards
     */
    checkAccessibility() {
        const textColor = this.getCSSVariable('--text-primary');
        const backgroundColor = this.getCSSVariable('--bg-primary');
        
        const contrastRatio = this.getContrastRatio(textColor, backgroundColor);
        
        return {
            contrastRatio,
            meetsAA: contrastRatio >= 4.5,
            meetsAAA: contrastRatio >= 7,
            theme: this.currentTheme
        };
    }

    /**
     * Event subscription methods
     */
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }

    off(event, callback) {
        return this.eventBus.off(event, callback);
    }

    /**
     * Destroy theme manager and clean up
     */
    destroy() {
        if (this.systemPreferenceQuery) {
            this.systemPreferenceQuery.removeEventListener('change', this.handleSystemPreferenceChange);
        }
        
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.removeEventListener('click', this.toggleTheme);
        }
        
        this.eventBus.clear();
        this.initialized = false;
    }
}

// Export the ThemeManager class
export { ThemeManager };
