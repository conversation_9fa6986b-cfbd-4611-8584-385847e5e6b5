/* ===== MAIN LAYOUT STRUCTURE ===== */

.app-container {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main";
  grid-template-rows: var(--header-height) 1fr;
  grid-template-columns: var(--sidebar-width) 1fr;
  height: 100vh;
  overflow: hidden;
  transition: grid-template-columns var(--transition-normal);
}

/* ===== HEADER ===== */
.app-header {
  grid-area: header;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-neumorphic-outset);
  z-index: var(--z-sticky);
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-lg);
}

/* Menu Toggle Button */
.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: var(--border-radius-md);
  transition: var(--transition-colors);
  gap: 3px;
}

.menu-toggle:hover {
  background-color: var(--bg-hover);
}

.hamburger-line {
  width: 18px;
  height: 2px;
  background-color: var(--text-primary);
  transition: var(--transition-fast);
  border-radius: 1px;
}

.menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Company Branding */
.company-brand {
  flex: 1;
  text-align: center;
}

.company-brand h1 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1.2;
}

.company-subtitle {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: 2px;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.theme-toggle,
.notification-btn,
.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: var(--border-radius-full);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all);
  box-shadow: var(--shadow-neumorphic-outset);
  position: relative;
}

.theme-toggle:hover,
.notification-btn:hover,
.user-avatar:hover {
  box-shadow: var(--shadow-neumorphic-hover);
  transform: translateY(-1px);
}

.theme-toggle:active,
.notification-btn:active,
.user-avatar:active {
  box-shadow: var(--shadow-neumorphic-pressed);
  transform: translateY(0);
}

.theme-icon {
  font-size: var(--font-size-base);
}

.notification-icon {
  font-size: var(--font-size-base);
}

.notification-badge {
  position: absolute;
  top: -2px;
  left: -2px;
  background-color: var(--color-error);
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--border-radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.avatar-text {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

/* ===== SIDEBAR ===== */
.sidebar {
  grid-area: sidebar;
  background-color: var(--bg-secondary);
  border-left: 1px solid var(--border-light);
  box-shadow: var(--shadow-neumorphic-outset);
  overflow-y: auto;
  overflow-x: hidden;
  transition: var(--transition-normal);
  z-index: var(--z-fixed);
  position: sticky;
  top: 0;
  height: 100vh;
}

.sidebar-content {
  padding: var(--space-lg) 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 8px;
}

.sidebar::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-full);
  margin: var(--space-sm) 0;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--border-radius-full);
  transition: var(--transition-colors);
  border: 1px solid var(--bg-secondary);
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.sidebar::-webkit-scrollbar-thumb:active {
  background: var(--color-primary-dark);
}

/* Firefox scrollbar */
.sidebar {
  scrollbar-width: thin;
  scrollbar-color: var(--border-medium) var(--bg-tertiary);
  scroll-behavior: smooth;
}

/* Smooth scrolling for navigation menu */
.nav-menu {
  scroll-behavior: smooth;
}

/* Better scrolling performance */
.sidebar,
.nav-menu {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Scroll padding for better navigation */
.nav-menu {
  scroll-padding-top: var(--space-lg);
  scroll-padding-bottom: var(--space-lg);
}

/* Scroll indicators */
.sidebar-content::before,
.sidebar-content::after {
  content: '';
  position: sticky;
  display: block;
  height: 1px;
  background: linear-gradient(to left, transparent, var(--border-light), transparent);
  margin: 0 var(--space-md);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.sidebar-content::before {
  top: 0;
}

.sidebar-content::after {
  bottom: 0;
}

/* Show scroll indicators when content is scrollable */
.sidebar:not(.at-top) .sidebar-content::before,
.sidebar:not(.at-bottom) .sidebar-content::after {
  opacity: 1;
}

/* Navigation Menu */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
  padding-bottom: var(--space-lg);
}

.nav-item {
  margin-bottom: var(--space-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md) var(--space-lg);
  color: var(--text-primary);
  text-decoration: none;
  transition: var(--transition-colors);
  position: relative;
  border-radius: 0;
}

.nav-link:hover {
  background-color: var(--bg-hover);
  color: var(--color-primary);
}

.nav-item.active > .nav-link {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
  font-weight: var(--font-weight-semibold);
}

.nav-item.active > .nav-link::before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--color-primary);
}

.nav-icon {
  font-size: var(--font-size-lg);
  width: 1.5rem;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  font-size: var(--font-size-sm);
}

.nav-arrow {
  font-size: var(--font-size-xs);
  transition: var(--transition-fast);
  transform: rotate(0deg);
}

.nav-item.expanded > .nav-link .nav-arrow {
  transform: rotate(-90deg);
}

/* Submenu */
.nav-submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
  background-color: var(--bg-tertiary);
}

.nav-item.expanded > .nav-submenu {
  max-height: 500px; /* Adjust based on content */
}

.nav-submenu .nav-link {
  padding-right: calc(var(--space-lg) + var(--space-xl));
  font-size: var(--font-size-sm);
}

.nav-submenu .nav-icon {
  font-size: var(--font-size-base);
  width: 1.25rem;
}

/* ===== MAIN CONTENT ===== */
.main-content {
  grid-area: main;
  background-color: var(--bg-primary);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.content-wrapper {
  padding: var(--space-lg);
  max-width: 1400px;
  margin: 0 auto;
}

/* Breadcrumb */
.breadcrumb {
  margin-bottom: var(--space-lg);
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: var(--font-size-sm);
}

.breadcrumb-list li {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.breadcrumb-list li:not(:last-child)::after {
  content: '◀';
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

.breadcrumb-list a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-colors);
}

.breadcrumb-list a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.breadcrumb-list .current {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Page Content */
.page-content {
  min-height: calc(100vh - var(--header-height) - 4rem);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 1024px) {
  .app-container {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr;
  }

  .sidebar {
    height: calc(100vh - var(--header-height));
    top: var(--header-height);
  }

  .nav-text,
  .nav-arrow {
    display: none;
  }

  .nav-link {
    justify-content: center;
    padding: var(--space-md);
  }

  .nav-submenu {
    display: none;
  }

  .company-brand h1 {
    font-size: var(--font-size-base);
  }

  .company-subtitle {
    display: none;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .app-container {
    grid-template-areas: 
      "header"
      "main";
    grid-template-rows: var(--header-height) 1fr;
    grid-template-columns: 1fr;
  }
  
  .menu-toggle {
    display: flex;
  }
  
  .sidebar {
    position: fixed;
    top: var(--header-height);
    right: 0;
    bottom: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    z-index: var(--z-modal);
    overflow-y: auto;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar-open::before {
    content: '';
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-modal) - 1);
  }
  
  .nav-text,
  .nav-arrow {
    display: block;
  }
  
  .nav-link {
    justify-content: flex-start;
    padding: var(--space-md) var(--space-lg);
  }
  
  .nav-submenu {
    display: block;
  }
  
  .content-wrapper {
    padding: var(--space-md);
  }
  
  .company-brand {
    text-align: right;
  }
  
  .header-actions {
    gap: var(--space-xs);
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--space-md);
  }
  
  .company-brand h1 {
    font-size: var(--font-size-sm);
  }
  
  .theme-toggle,
  .notification-btn,
  .user-avatar {
    width: 2rem;
    height: 2rem;
  }
  
  .content-wrapper {
    padding: var(--space-sm);
  }
  
  .breadcrumb-list {
    font-size: var(--font-size-xs);
  }
}

/* ===== THEME TRANSITIONS ===== */
.theme-transitioning * {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal),
              box-shadow var(--transition-normal) !important;
}

/* ===== PRINT STYLES ===== */
@media print {
  .app-header,
  .sidebar,
  .breadcrumb {
    display: none !important;
  }
  
  .app-container {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }
  
  .main-content {
    overflow: visible;
  }
  
  .content-wrapper {
    padding: 0;
    max-width: none;
  }
  
  .page-content {
    min-height: auto;
  }
  
  /* Hide interactive elements */
  .btn,
  .form-control,
  .modal,
  .toast,
  .dropdown {
    display: none !important;
  }
  
  /* Ensure good contrast for printing */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .card,
  .table-container {
    border: 1px solid #ccc !important;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-link:hover,
  .nav-item.active > .nav-link {
    outline: 2px solid currentColor;
    outline-offset: -2px;
  }
  
  .btn:focus,
  .form-control:focus {
    outline: 3px solid currentColor;
    outline-offset: 2px;
  }
}

/* Focus indicators for keyboard navigation */
.nav-link:focus,
.btn:focus,
.form-control:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip to main content link for screen readers */
.skip-to-main {
  position: absolute;
  top: -40px;
  right: 6px;
  background: var(--color-primary);
  color: var(--text-inverse);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-md);
  z-index: 9999;
}

.skip-to-main:focus {
  top: 6px;
}
