/**
 * Report Management Component
 * إدارة التقارير
 */
class ReportManagement {
    constructor() {
        this.container = null;
        this.reports = [];
        this.selectedPeriod = 'month';
        this.selectedReport = 'sales';
        this.chartManager = null;
        this.charts = new Map();
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.initializeChartManager();
        this.loadReports();
        this.renderContent();
        this.attachEventListeners();
        this.renderCharts();
    }

    /**
     * Initialize chart manager
     */
    initializeChartManager() {
        if (window.ChartManager) {
            this.chartManager = new ChartManager();
            this.chartManager.init();
        }
    }

    /**
     * Load reports data
     */
    loadReports() {
        // Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.reports = {
            sales: {
                title: 'تقرير المبيعات',
                icon: '📊',
                data: {
                    totalSales: 15750.00,
                    totalInvoices: 25,
                    averageInvoice: 630.00,
                    topCustomer: 'أحمد محمد',
                    topProduct: 'صيانة مكيف سبليت'
                }
            },
            purchases: {
                title: 'تقرير المشتريات',
                icon: '🛒',
                data: {
                    totalPurchases: 8500.00,
                    totalOrders: 12,
                    averageOrder: 708.33,
                    topSupplier: 'شركة قطع الغيار المتقدمة',
                    topItem: 'فلتر مكيف'
                }
            },
            inventory: {
                title: 'تقرير المخزون',
                icon: '📦',
                data: {
                    totalItems: 45,
                    lowStockItems: 8,
                    outOfStockItems: 2,
                    totalValue: 12500.00,
                    topItem: 'فلتر مكيف'
                }
            },
            financial: {
                title: 'التقرير المالي',
                icon: '💰',
                data: {
                    totalRevenue: 15750.00,
                    totalExpenses: 8500.00,
                    netProfit: 7250.00,
                    profitMargin: 46.03,
                    cashFlow: 5200.00
                }
            }
        };
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">التقارير والإحصائيات</h1>
                        <p class="page-subtitle">تقارير شاملة عن أداء النشاط التجاري</p>
                    </div>
                    <div class="page-actions">
                        <select id="period-select" class="filter-select">
                            <option value="week">هذا الأسبوع</option>
                            <option value="month" selected>هذا الشهر</option>
                            <option value="quarter">هذا الربع</option>
                            <option value="year">هذا العام</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                        <div class="btn-group">
                            <button class="btn btn-success" id="export-excel-btn">
                                <span class="btn-icon">📊</span>
                                Excel
                            </button>
                            <button class="btn btn-danger" id="export-pdf-btn">
                                <span class="btn-icon">📄</span>
                                PDF
                            </button>
                            <button class="btn btn-info" id="export-csv-btn">
                                <span class="btn-icon">📋</span>
                                CSV
                            </button>
                            <button class="btn btn-secondary" id="print-report-btn">
                                <span class="btn-icon">🖨️</span>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Categories -->
                <div class="report-categories">
                    <div class="category-tabs">
                        <button class="category-tab ${this.selectedReport === 'sales' ? 'active' : ''}" data-report="sales">
                            <span class="tab-icon">📊</span>
                            <span class="tab-text">تقارير المبيعات</span>
                        </button>
                        <button class="category-tab ${this.selectedReport === 'purchases' ? 'active' : ''}" data-report="purchases">
                            <span class="tab-icon">🛒</span>
                            <span class="tab-text">تقارير المشتريات</span>
                        </button>
                        <button class="category-tab ${this.selectedReport === 'inventory' ? 'active' : ''}" data-report="inventory">
                            <span class="tab-icon">📦</span>
                            <span class="tab-text">تقارير المخزون</span>
                        </button>
                        <button class="category-tab ${this.selectedReport === 'financial' ? 'active' : ''}" data-report="financial">
                            <span class="tab-icon">💰</span>
                            <span class="tab-text">التقارير المالية</span>
                        </button>
                    </div>
                </div>

                <!-- Report Content -->
                <div class="report-content">
                    ${this.renderReportContent()}
                </div>

                <!-- Charts Section -->
                <div class="charts-section">
                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>الاتجاه الشهري</h3>
                                <div class="chart-controls">
                                    <button class="chart-control active" data-chart="sales">المبيعات</button>
                                    <button class="chart-control" data-chart="purchases">المشتريات</button>
                                    <button class="chart-control" data-chart="profit">الأرباح</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="trend-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>توزيع الخدمات</h3>
                                <div class="chart-controls">
                                    <button class="chart-control active" data-export="service-chart">تصدير</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="service-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>نمو العملاء</h3>
                                <div class="chart-controls">
                                    <button class="chart-control active" data-period="6months">6 أشهر</button>
                                    <button class="chart-control" data-period="year">سنة</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="customer-growth-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>مقارنة الإيرادات</h3>
                                <div class="chart-controls">
                                    <button class="chart-control active" data-comparison="monthly">شهري</button>
                                    <button class="chart-control" data-comparison="quarterly">ربعي</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="revenue-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Tables -->
                <div class="detailed-tables">
                    ${this.renderDetailedTable()}
                </div>
            </div>
        `;
    }

    /**
     * Render report content based on selected report
     */
    renderReportContent() {
        const report = this.reports[this.selectedReport];
        if (!report) return '';

        switch (this.selectedReport) {
            case 'sales':
                return this.renderSalesReport(report);
            case 'purchases':
                return this.renderPurchasesReport(report);
            case 'inventory':
                return this.renderInventoryReport(report);
            case 'financial':
                return this.renderFinancialReport(report);
            default:
                return '';
        }
    }

    /**
     * Render sales report
     */
    renderSalesReport(report) {
        return `
            <div class="report-stats">
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalSales.toFixed(2)} ر.س</div>
                        <div class="stat-label">إجمالي المبيعات</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📄</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalInvoices}</div>
                        <div class="stat-label">عدد الفواتير</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.averageInvoice.toFixed(2)} ر.س</div>
                        <div class="stat-label">متوسط الفاتورة</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👤</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.topCustomer}</div>
                        <div class="stat-label">أفضل عميل</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏆</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.topProduct}</div>
                        <div class="stat-label">أفضل منتج</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render purchases report
     */
    renderPurchasesReport(report) {
        return `
            <div class="report-stats">
                <div class="stat-card">
                    <div class="stat-icon">🛒</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalPurchases.toFixed(2)} ر.س</div>
                        <div class="stat-label">إجمالي المشتريات</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalOrders}</div>
                        <div class="stat-label">عدد الطلبات</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.averageOrder.toFixed(2)} ر.س</div>
                        <div class="stat-label">متوسط الطلب</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏢</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.topSupplier}</div>
                        <div class="stat-label">أفضل مورد</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏆</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.topItem}</div>
                        <div class="stat-label">أكثر صنف شراءً</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render inventory report
     */
    renderInventoryReport(report) {
        return `
            <div class="report-stats">
                <div class="stat-card">
                    <div class="stat-icon">📦</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalItems}</div>
                        <div class="stat-label">إجمالي الأصناف</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.lowStockItems}</div>
                        <div class="stat-label">أصناف قليلة المخزون</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">❌</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.outOfStockItems}</div>
                        <div class="stat-label">أصناف نفدت</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalValue.toFixed(2)} ر.س</div>
                        <div class="stat-label">قيمة المخزون</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏆</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.topItem}</div>
                        <div class="stat-label">أكثر صنف حركة</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render financial report
     */
    renderFinancialReport(report) {
        return `
            <div class="report-stats">
                <div class="stat-card income">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalRevenue.toFixed(2)} ر.س</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                </div>
                <div class="stat-card expense">
                    <div class="stat-icon">💸</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.totalExpenses.toFixed(2)} ر.س</div>
                        <div class="stat-label">إجمالي المصروفات</div>
                    </div>
                </div>
                <div class="stat-card ${report.data.netProfit >= 0 ? 'profit' : 'loss'}">
                    <div class="stat-icon">${report.data.netProfit >= 0 ? '📈' : '📉'}</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.netProfit.toFixed(2)} ر.س</div>
                        <div class="stat-label">صافي الربح</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.profitMargin.toFixed(1)}%</div>
                        <div class="stat-label">هامش الربح</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💵</div>
                    <div class="stat-content">
                        <div class="stat-value">${report.data.cashFlow.toFixed(2)} ر.س</div>
                        <div class="stat-label">التدفق النقدي</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render detailed table
     */
    renderDetailedTable() {
        return `
            <div class="table-container">
                <div class="table-header">
                    <h3>تفاصيل ${this.reports[this.selectedReport].title}</h3>
                </div>
                <div class="table-placeholder">
                    <div class="placeholder-icon">📊</div>
                    <h3>جدول تفصيلي</h3>
                    <p>سيتم عرض البيانات التفصيلية هنا</p>
                    <button class="btn btn-primary">عرض التفاصيل</button>
                </div>
            </div>
        `;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Period selection
        const periodSelect = this.container.querySelector('#period-select');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.selectedPeriod = e.target.value;
                this.loadReports();
                this.renderContent();
                this.attachEventListeners();
            });
        }

        // Report category tabs
        const categoryTabs = this.container.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const reportType = e.currentTarget.dataset.report;
                this.selectedReport = reportType;
                this.renderContent();
                this.attachEventListeners();
            });
        });

        // Export buttons
        const exportExcelBtn = this.container.querySelector('#export-excel-btn');
        const exportPdfBtn = this.container.querySelector('#export-pdf-btn');
        const exportCsvBtn = this.container.querySelector('#export-csv-btn');
        const printReportBtn = this.container.querySelector('#print-report-btn');

        if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', () => this.exportToExcel());
        }

        if (exportPdfBtn) {
            exportPdfBtn.addEventListener('click', () => this.exportToPDF());
        }

        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', () => this.exportToCSV());
        }

        if (printReportBtn) {
            printReportBtn.addEventListener('click', () => this.printReport());
        }

        // Chart controls
        const chartControls = this.container.querySelectorAll('.chart-control');
        chartControls.forEach(control => {
            control.addEventListener('click', (e) => {
                // Remove active class from all controls
                chartControls.forEach(c => c.classList.remove('active'));
                // Add active class to clicked control
                e.target.classList.add('active');
            });
        });
    }

    /**
     * Render charts
     */
    renderCharts() {
        if (!this.chartManager) {
            console.warn('Chart Manager not available');
            return;
        }

        if (!window.Chart) {
            console.warn('Chart.js not loaded');
            // Try to load Chart.js again
            setTimeout(() => {
                if (window.Chart) {
                    this.renderCharts();
                }
            }, 1000);
            return;
        }

        // Destroy existing charts
        this.destroyCharts();

        // Wait for DOM to be ready
        setTimeout(() => {
            try {
                this.renderTrendChart();
                this.renderServiceChart();
                this.renderCustomerGrowthChart();
                this.renderRevenueChart();
                console.log('✅ Charts rendered successfully');
            } catch (error) {
                console.error('❌ Error rendering charts:', error);
            }
        }, 100);
    }

    /**
     * Render trend chart
     */
    renderTrendChart() {
        const salesData = [
            { month: 'يناير', sales: 12500 },
            { month: 'فبراير', sales: 15200 },
            { month: 'مارس', sales: 18700 },
            { month: 'أبريل', sales: 16800 },
            { month: 'مايو', sales: 21300 },
            { month: 'يونيو', sales: 19500 }
        ];

        const chart = this.chartManager.createSalesTrendChart('trend-chart', salesData);
        if (chart) {
            this.charts.set('trend-chart', chart);
        }
    }

    /**
     * Render service distribution chart
     */
    renderServiceChart() {
        const serviceData = [
            { service: 'صيانة مكيفات', count: 45 },
            { service: 'أعمال كهربائية', count: 32 },
            { service: 'أعمال سباكة', count: 28 },
            { service: 'صيانة عامة', count: 35 },
            { service: 'خدمات أخرى', count: 15 }
        ];

        const chart = this.chartManager.createServiceDistributionChart('service-chart', serviceData);
        if (chart) {
            this.charts.set('service-chart', chart);
        }
    }

    /**
     * Render customer growth chart
     */
    renderCustomerGrowthChart() {
        const growthData = [
            { month: 'يناير', newCustomers: 8, totalCustomers: 125 },
            { month: 'فبراير', newCustomers: 12, totalCustomers: 137 },
            { month: 'مارس', newCustomers: 15, totalCustomers: 152 },
            { month: 'أبريل', newCustomers: 10, totalCustomers: 162 },
            { month: 'مايو', newCustomers: 18, totalCustomers: 180 },
            { month: 'يونيو', newCustomers: 14, totalCustomers: 194 }
        ];

        const chart = this.chartManager.createCustomerGrowthChart('customer-growth-chart', growthData);
        if (chart) {
            this.charts.set('customer-growth-chart', chart);
        }
    }

    /**
     * Render revenue comparison chart
     */
    renderRevenueChart() {
        const revenueData = [
            { category: 'صيانة مكيفات', revenue: 45000 },
            { category: 'أعمال كهربائية', revenue: 32000 },
            { category: 'أعمال سباكة', revenue: 28000 },
            { category: 'صيانة عامة', revenue: 35000 },
            { category: 'خدمات أخرى', revenue: 15000 }
        ];

        const chart = this.chartManager.createRevenueComparisonChart('revenue-chart', revenueData);
        if (chart) {
            this.charts.set('revenue-chart', chart);
        }
    }

    /**
     * Destroy all charts
     */
    destroyCharts() {
        this.charts.forEach((chart, id) => {
            if (this.chartManager) {
                this.chartManager.destroyChart(id);
            }
        });
        this.charts.clear();
    }

    /**
     * Export to Excel
     */
    exportToExcel() {
        console.log('🔄 Attempting Excel export...');

        if (!window.exportManager) {
            console.error('Export Manager not available');
            window.uiManager?.showToast('مدير التصدير غير متوفر', 'error');
            return;
        }

        if (!window.XLSX) {
            console.error('XLSX library not loaded');
            window.uiManager?.showToast('مكتبة Excel غير محملة', 'error');
            return;
        }

        try {
            const reportData = this.getReportData();
            const filename = `تقرير-${this.reports[this.selectedReport].title}-${new Date().toLocaleDateString('ar-SA')}`;

            window.exportManager.exportToExcel(reportData, filename);
            console.log('✅ Excel export initiated');
        } catch (error) {
            console.error('❌ Excel export failed:', error);
            window.uiManager?.showToast('خطأ في تصدير Excel', 'error');
        }
    }

    /**
     * Export to PDF
     */
    exportToPDF() {
        if (!window.exportManager) {
            window.uiManager?.showToast('مدير التصدير غير متوفر', 'error');
            return;
        }

        const reportData = this.getReportData();
        const filename = `تقرير-${this.reports[this.selectedReport].title}-${new Date().toLocaleDateString('ar-SA')}`;
        const title = `تقرير ${this.reports[this.selectedReport].title}`;

        window.exportManager.exportToPDF(reportData, filename, title);
    }

    /**
     * Export to CSV
     */
    exportToCSV() {
        if (!window.exportManager) {
            window.uiManager?.showToast('مدير التصدير غير متوفر', 'error');
            return;
        }

        const reportData = this.getReportData();
        const filename = `تقرير-${this.reports[this.selectedReport].title}-${new Date().toLocaleDateString('ar-SA')}`;

        window.exportManager.exportToCSV(reportData, filename);
    }

    /**
     * Print report
     */
    printReport() {
        if (!window.printManager) {
            window.uiManager?.showToast('مدير الطباعة غير متوفر', 'error');
            return;
        }

        const reportData = this.getReportData();
        const title = `تقرير ${this.reports[this.selectedReport].title}`;

        window.printManager.printReport(reportData, title);
    }

    /**
     * Get report data for export
     */
    getReportData() {
        const report = this.reports[this.selectedReport];
        if (!report) return [];

        // Convert report data to table format
        const data = [];

        switch (this.selectedReport) {
            case 'sales':
                data.push(
                    { 'البيان': 'إجمالي المبيعات', 'القيمة': `${report.data.totalSales.toFixed(2)} ر.س` },
                    { 'البيان': 'عدد الفواتير', 'القيمة': report.data.totalInvoices },
                    { 'البيان': 'متوسط الفاتورة', 'القيمة': `${report.data.averageInvoice.toFixed(2)} ر.س` },
                    { 'البيان': 'أفضل عميل', 'القيمة': report.data.topCustomer },
                    { 'البيان': 'أفضل منتج', 'القيمة': report.data.topProduct }
                );
                break;
            case 'purchases':
                data.push(
                    { 'البيان': 'إجمالي المشتريات', 'القيمة': `${report.data.totalPurchases.toFixed(2)} ر.س` },
                    { 'البيان': 'عدد الطلبات', 'القيمة': report.data.totalOrders },
                    { 'البيان': 'متوسط الطلب', 'القيمة': `${report.data.averageOrder.toFixed(2)} ر.س` },
                    { 'البيان': 'أفضل مورد', 'القيمة': report.data.topSupplier },
                    { 'البيان': 'أكثر صنف شراءً', 'القيمة': report.data.topItem }
                );
                break;
            case 'inventory':
                data.push(
                    { 'البيان': 'إجمالي الأصناف', 'القيمة': report.data.totalItems },
                    { 'البيان': 'أصناف قليلة المخزون', 'القيمة': report.data.lowStockItems },
                    { 'البيان': 'أصناف نفدت', 'القيمة': report.data.outOfStockItems },
                    { 'البيان': 'قيمة المخزون', 'القيمة': `${report.data.totalValue.toFixed(2)} ر.س` },
                    { 'البيان': 'أكثر صنف حركة', 'القيمة': report.data.topItem }
                );
                break;
            case 'financial':
                data.push(
                    { 'البيان': 'إجمالي الإيرادات', 'القيمة': `${report.data.totalRevenue.toFixed(2)} ر.س` },
                    { 'البيان': 'إجمالي المصروفات', 'القيمة': `${report.data.totalExpenses.toFixed(2)} ر.س` },
                    { 'البيان': 'صافي الربح', 'القيمة': `${report.data.netProfit.toFixed(2)} ر.س` },
                    { 'البيان': 'هامش الربح', 'القيمة': `${report.data.profitMargin.toFixed(1)}%` },
                    { 'البيان': 'التدفق النقدي', 'القيمة': `${report.data.cashFlow.toFixed(2)} ر.س` }
                );
                break;
        }

        return data;
    }

    /**
     * Cleanup
     */
    destroy() {
        this.destroyCharts();
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.ReportManagement = ReportManagement;
console.log('📈 ReportManagement class loaded and made globally available');

// Export for use in other modules
export default ReportManagement;
