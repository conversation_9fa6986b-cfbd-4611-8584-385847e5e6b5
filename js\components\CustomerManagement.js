/**
 * Customer Management Component - إدارة العملاء
 * Complete customer management system with CRUD operations
 */

class CustomerManagement {
    constructor(params = {}) {
        this.params = params;
        this.container = null;
        this.customers = [];
        this.filteredCustomers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.filterType = 'all';
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.selectedCustomers = new Set();
    }

    /**
     * Render the customer management page
     */
    async render() {
        this.container = document.createElement('div');
        this.container.className = 'customer-management-container';
        
        // Load customers data
        await this.loadCustomers();
        
        this.container.innerHTML = `
            <div class="page-header">
                <div class="header-content">
                    <div class="header-title">
                        <h1 class="page-title">
                            <span class="page-icon">👥</span>
                            إدارة العملاء
                        </h1>
                        <p class="page-subtitle">إدارة وتنظيم بيانات العملاء</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-primary" id="add-customer-btn">
                            <span>➕</span>
                            إضافة عميل جديد
                        </button>
                        <button class="btn btn-secondary" id="export-customers-btn">
                            <span>📤</span>
                            تصدير القائمة
                        </button>
                    </div>
                </div>
            </div>

            <div class="customers-content">
                <!-- Search and Filter Section -->
                <div class="customers-toolbar">
                    <div class="search-section">
                        <div class="search-box">
                            <input type="text" 
                                   id="customer-search" 
                                   class="form-control" 
                                   placeholder="البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني...)"
                                   value="${this.searchTerm}">
                            <button class="search-btn" id="search-btn">
                                <span>🔍</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <select id="customer-type-filter" class="form-control">
                            <option value="all">جميع العملاء</option>
                            <option value="individual">أفراد</option>
                            <option value="company">شركات</option>
                            <option value="vip">عملاء مميزين</option>
                        </select>
                        
                        <select id="sort-by" class="form-control">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="createdAt">ترتيب حسب تاريخ الإضافة</option>
                            <option value="lastOrder">ترتيب حسب آخر طلب</option>
                        </select>
                        
                        <button class="btn btn-secondary" id="clear-filters-btn">
                            <span>🗑️</span>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="customers-stats">
                    ${this.renderStatsCards()}
                </div>

                <!-- Customers Table -->
                <div class="customers-table-section">
                    <div class="table-header">
                        <div class="table-title">
                            <h3>قائمة العملاء (${this.filteredCustomers.length})</h3>
                        </div>
                        <div class="table-actions">
                            <button class="btn btn-sm btn-secondary" id="select-all-btn">
                                تحديد الكل
                            </button>
                            <button class="btn btn-sm btn-warning" id="bulk-delete-btn" style="display: none;">
                                <span>🗑️</span>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="table customers-table">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all-checkbox">
                                    </th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>المدينة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الحالة</th>
                                    <th width="120">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customers-table-body">
                                ${this.renderCustomersTable()}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="pagination-container">
                        ${this.renderPagination()}
                    </div>
                </div>
            </div>

            <!-- Customer Modal -->
            <div id="customer-modal" class="modal" style="display: none;">
                <div class="modal-backdrop"></div>
                <div class="modal-dialog modal-large">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title" id="customer-modal-title">إضافة عميل جديد</h3>
                            <button class="modal-close" id="close-customer-modal">×</button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCustomerForm()}
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" id="cancel-customer-btn">إلغاء</button>
                            <button class="btn btn-primary" id="save-customer-btn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Set up event listeners
        this.setupEventListeners();

        return this.container;
    }

    /**
     * Load customers from data manager
     */
    async loadCustomers() {
        try {
            const dataManager = window.dataManager;
            this.customers = await dataManager.query('customers', {
                _sort: this.sortBy,
                _order: this.sortOrder
            });
            this.applyFilters();
        } catch (error) {
            console.error('Failed to load customers:', error);
            window.uiManager?.showToast('خطأ في تحميل بيانات العملاء', 'error');
            this.customers = [];
            this.filteredCustomers = [];
        }
    }

    /**
     * Apply search and filter
     */
    applyFilters() {
        let filtered = [...this.customers];

        // Apply search
        if (this.searchTerm) {
            const searchLower = this.searchTerm.toLowerCase();
            filtered = filtered.filter(customer => 
                customer.name?.toLowerCase().includes(searchLower) ||
                customer.phone?.includes(this.searchTerm) ||
                customer.email?.toLowerCase().includes(searchLower) ||
                customer.address?.toLowerCase().includes(searchLower) ||
                customer.city?.toLowerCase().includes(searchLower)
            );
        }

        // Apply type filter
        if (this.filterType !== 'all') {
            filtered = filtered.filter(customer => customer.type === this.filterType);
        }

        this.filteredCustomers = filtered;
        this.currentPage = 1; // Reset to first page
    }

    /**
     * Render statistics cards
     */
    renderStatsCards() {
        const totalCustomers = this.customers.length;
        const individualCustomers = this.customers.filter(c => c.type === 'individual').length;
        const companyCustomers = this.customers.filter(c => c.type === 'company').length;
        const vipCustomers = this.customers.filter(c => c.type === 'vip').length;

        return `
            <div class="stats-grid">
                <div class="stat-card neumorphic">
                    <div class="stat-icon stat-icon-primary">👥</div>
                    <div class="stat-details">
                        <h3 class="stat-value">${totalCustomers}</h3>
                        <p class="stat-label">إجمالي العملاء</p>
                    </div>
                </div>
                
                <div class="stat-card neumorphic">
                    <div class="stat-icon stat-icon-info">👤</div>
                    <div class="stat-details">
                        <h3 class="stat-value">${individualCustomers}</h3>
                        <p class="stat-label">عملاء أفراد</p>
                    </div>
                </div>
                
                <div class="stat-card neumorphic">
                    <div class="stat-icon stat-icon-success">🏢</div>
                    <div class="stat-details">
                        <h3 class="stat-value">${companyCustomers}</h3>
                        <p class="stat-label">عملاء شركات</p>
                    </div>
                </div>
                
                <div class="stat-card neumorphic">
                    <div class="stat-icon stat-icon-warning">⭐</div>
                    <div class="stat-details">
                        <h3 class="stat-value">${vipCustomers}</h3>
                        <p class="stat-label">عملاء مميزين</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render customers table
     */
    renderCustomersTable() {
        if (this.filteredCustomers.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="text-center text-muted">
                        <div class="empty-state">
                            <div class="empty-icon">👥</div>
                            <h3>لا توجد عملاء</h3>
                            <p>ابدأ بإضافة عميل جديد لرؤية القائمة هنا</p>
                            <button class="btn btn-primary" onclick="document.getElementById('add-customer-btn').click()">
                                إضافة عميل جديد
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageCustomers = this.filteredCustomers.slice(startIndex, endIndex);

        return pageCustomers.map(customer => `
            <tr class="customer-row" data-customer-id="${customer.id}">
                <td>
                    <input type="checkbox" class="customer-checkbox" value="${customer.id}">
                </td>
                <td>
                    <div class="customer-name">
                        <strong>${customer.name || 'غير محدد'}</strong>
                        ${customer.company ? `<br><small class="text-muted">${customer.company}</small>` : ''}
                    </div>
                </td>
                <td>
                    <span class="badge badge-${this.getTypeColor(customer.type)}">
                        ${this.getTypeLabel(customer.type)}
                    </span>
                </td>
                <td>
                    <a href="tel:${customer.phone}" class="phone-link">
                        ${customer.phone || 'غير محدد'}
                    </a>
                </td>
                <td>
                    ${customer.email ? `<a href="mailto:${customer.email}">${customer.email}</a>` : 'غير محدد'}
                </td>
                <td>${customer.city || 'غير محدد'}</td>
                <td>${this.formatDate(customer.createdAt)}</td>
                <td>
                    <span class="badge badge-${customer.status === 'active' ? 'success' : 'warning'}">
                        ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary view-customer-btn" 
                                data-customer-id="${customer.id}" 
                                title="عرض التفاصيل">
                            👁️
                        </button>
                        <button class="btn btn-sm btn-primary edit-customer-btn" 
                                data-customer-id="${customer.id}" 
                                title="تعديل">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-error delete-customer-btn" 
                                data-customer-id="${customer.id}" 
                                title="حذف">
                            🗑️
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const totalPages = Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            return '';
        }

        let pagination = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            pagination += `<button class="btn btn-sm btn-secondary pagination-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                pagination += `<button class="btn btn-sm btn-primary pagination-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination += `<button class="btn btn-sm btn-secondary pagination-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination += '<span class="pagination-dots">...</span>';
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            pagination += `<button class="btn btn-sm btn-secondary pagination-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }
        
        pagination += '</div>';
        
        // Add page info
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredCustomers.length);
        
        pagination += `
            <div class="pagination-info">
                عرض ${startItem} - ${endItem} من ${this.filteredCustomers.length} عميل
            </div>
        `;
        
        return pagination;
    }

    /**
     * Get type color for badge
     */
    getTypeColor(type) {
        const colors = {
            individual: 'info',
            company: 'success',
            vip: 'warning'
        };
        return colors[type] || 'secondary';
    }

    /**
     * Get type label
     */
    getTypeLabel(type) {
        const labels = {
            individual: 'فرد',
            company: 'شركة',
            vip: 'مميز'
        };
        return labels[type] || 'غير محدد';
    }

    /**
     * Format date
     */
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';

        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Render customer form
     */
    renderCustomerForm() {
        return `
            <form id="customer-form" class="customer-form">
                <div class="form-grid">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="form-section-title">المعلومات الأساسية</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customer-name">
                                    الاسم الكامل <span class="required">*</span>
                                </label>
                                <input type="text"
                                       id="customer-name"
                                       name="name"
                                       class="form-control"
                                       placeholder="أدخل الاسم الكامل"
                                       required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-type">
                                    نوع العميل <span class="required">*</span>
                                </label>
                                <select id="customer-type" name="type" class="form-control" required>
                                    <option value="">اختر نوع العميل</option>
                                    <option value="individual">فرد</option>
                                    <option value="company">شركة</option>
                                    <option value="vip">عميل مميز</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row" id="company-row" style="display: none;">
                            <div class="form-group">
                                <label class="form-label" for="customer-company">اسم الشركة</label>
                                <input type="text"
                                       id="customer-company"
                                       name="company"
                                       class="form-control"
                                       placeholder="أدخل اسم الشركة">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-tax-id">الرقم الضريبي</label>
                                <input type="text"
                                       id="customer-tax-id"
                                       name="taxId"
                                       class="form-control"
                                       placeholder="أدخل الرقم الضريبي">
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h4 class="form-section-title">معلومات الاتصال</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customer-phone">
                                    رقم الهاتف <span class="required">*</span>
                                </label>
                                <input type="tel"
                                       id="customer-phone"
                                       name="phone"
                                       class="form-control"
                                       placeholder="05xxxxxxxx"
                                       required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-phone2">رقم هاتف إضافي</label>
                                <input type="tel"
                                       id="customer-phone2"
                                       name="phone2"
                                       class="form-control"
                                       placeholder="05xxxxxxxx">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customer-email">البريد الإلكتروني</label>
                                <input type="email"
                                       id="customer-email"
                                       name="email"
                                       class="form-control"
                                       placeholder="<EMAIL>">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-website">الموقع الإلكتروني</label>
                                <input type="url"
                                       id="customer-website"
                                       name="website"
                                       class="form-control"
                                       placeholder="https://example.com">
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="form-section">
                        <h4 class="form-section-title">معلومات العنوان</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customer-city">المدينة</label>
                                <select id="customer-city" name="city" class="form-control">
                                    <option value="">اختر المدينة</option>
                                    <option value="الرياض">الرياض</option>
                                    <option value="جدة">جدة</option>
                                    <option value="مكة المكرمة">مكة المكرمة</option>
                                    <option value="المدينة المنورة">المدينة المنورة</option>
                                    <option value="الدمام">الدمام</option>
                                    <option value="الخبر">الخبر</option>
                                    <option value="الطائف">الطائف</option>
                                    <option value="أبها">أبها</option>
                                    <option value="تبوك">تبوك</option>
                                    <option value="بريدة">بريدة</option>
                                    <option value="خميس مشيط">خميس مشيط</option>
                                    <option value="حائل">حائل</option>
                                    <option value="نجران">نجران</option>
                                    <option value="الجبيل">الجبيل</option>
                                    <option value="ينبع">ينبع</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-district">الحي</label>
                                <input type="text"
                                       id="customer-district"
                                       name="district"
                                       class="form-control"
                                       placeholder="أدخل اسم الحي">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="customer-address">العنوان التفصيلي</label>
                            <textarea id="customer-address"
                                      name="address"
                                      class="form-control"
                                      rows="3"
                                      placeholder="أدخل العنوان التفصيلي"></textarea>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="form-section">
                        <h4 class="form-section-title">معلومات إضافية</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customer-status">حالة العميل</label>
                                <select id="customer-status" name="status" class="form-control">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="customer-credit-limit">الحد الائتماني</label>
                                <input type="number"
                                       id="customer-credit-limit"
                                       name="creditLimit"
                                       class="form-control"
                                       placeholder="0.00"
                                       min="0"
                                       step="0.01">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="customer-notes">ملاحظات</label>
                            <textarea id="customer-notes"
                                      name="notes"
                                      class="form-control"
                                      rows="3"
                                      placeholder="أدخل أي ملاحظات إضافية"></textarea>
                        </div>
                    </div>
                </div>

                <input type="hidden" id="customer-id" name="id">
            </form>
        `;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Add customer button
        const addCustomerBtn = this.container.querySelector('#add-customer-btn');
        addCustomerBtn?.addEventListener('click', () => this.openCustomerModal());

        // Export customers button
        const exportBtn = this.container.querySelector('#export-customers-btn');
        exportBtn?.addEventListener('click', () => this.exportCustomers());

        // Search functionality
        const searchInput = this.container.querySelector('#customer-search');
        searchInput?.addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.applyFilters();
            this.refreshTable();
        });

        // Filter functionality
        const typeFilter = this.container.querySelector('#customer-type-filter');
        typeFilter?.addEventListener('change', (e) => {
            this.filterType = e.target.value;
            this.applyFilters();
            this.refreshTable();
        });

        const sortBy = this.container.querySelector('#sort-by');
        sortBy?.addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.loadCustomers();
            this.refreshTable();
        });

        // Clear filters
        const clearFiltersBtn = this.container.querySelector('#clear-filters-btn');
        clearFiltersBtn?.addEventListener('click', () => this.clearFilters());

        // Select all functionality
        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        selectAllCheckbox?.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));

        const selectAllBtn = this.container.querySelector('#select-all-btn');
        selectAllBtn?.addEventListener('click', () => this.toggleSelectAll(true));

        // Bulk delete
        const bulkDeleteBtn = this.container.querySelector('#bulk-delete-btn');
        bulkDeleteBtn?.addEventListener('click', () => this.bulkDeleteCustomers());

        // Table event delegation
        const tableBody = this.container.querySelector('#customers-table-body');
        tableBody?.addEventListener('click', (e) => this.handleTableClick(e));

        // Pagination
        const paginationContainer = this.container.querySelector('.pagination-container');
        paginationContainer?.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn')) {
                const page = parseInt(e.target.getAttribute('data-page'));
                this.currentPage = page;
                this.refreshTable();
            }
        });

        // Modal events
        this.setupModalEvents();

        // Customer type change in form
        const customerTypeSelect = this.container.querySelector('#customer-type');
        customerTypeSelect?.addEventListener('change', (e) => {
            const companyRow = this.container.querySelector('#company-row');
            if (e.target.value === 'company') {
                companyRow.style.display = 'block';
            } else {
                companyRow.style.display = 'none';
            }
        });
    }

    /**
     * Set up modal events
     */
    setupModalEvents() {
        const modal = this.container.querySelector('#customer-modal');
        const closeBtn = this.container.querySelector('#close-customer-modal');
        const cancelBtn = this.container.querySelector('#cancel-customer-btn');
        const saveBtn = this.container.querySelector('#save-customer-btn');

        closeBtn?.addEventListener('click', () => this.closeCustomerModal());
        cancelBtn?.addEventListener('click', () => this.closeCustomerModal());
        saveBtn?.addEventListener('click', () => this.saveCustomer());

        // Close modal when clicking backdrop
        modal?.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeCustomerModal();
            }
        });
    }

    /**
     * Handle table clicks (view, edit, delete)
     */
    handleTableClick(e) {
        const customerId = e.target.getAttribute('data-customer-id');

        if (e.target.classList.contains('view-customer-btn')) {
            this.viewCustomer(customerId);
        } else if (e.target.classList.contains('edit-customer-btn')) {
            this.editCustomer(customerId);
        } else if (e.target.classList.contains('delete-customer-btn')) {
            this.deleteCustomer(customerId);
        } else if (e.target.classList.contains('customer-checkbox')) {
            this.toggleCustomerSelection(customerId, e.target.checked);
        }
    }

    /**
     * Toggle customer selection
     */
    toggleCustomerSelection(customerId, selected) {
        if (selected) {
            this.selectedCustomers.add(customerId);
        } else {
            this.selectedCustomers.delete(customerId);
        }

        this.updateBulkActions();
    }

    /**
     * Toggle select all
     */
    toggleSelectAll(selectAll) {
        const checkboxes = this.container.querySelectorAll('.customer-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.toggleCustomerSelection(checkbox.value, selectAll);
        });

        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = selectAll;
        }
    }

    /**
     * Update bulk actions visibility
     */
    updateBulkActions() {
        const bulkDeleteBtn = this.container.querySelector('#bulk-delete-btn');
        if (this.selectedCustomers.size > 0) {
            bulkDeleteBtn.style.display = 'inline-flex';
            bulkDeleteBtn.textContent = `🗑️ حذف المحدد (${this.selectedCustomers.size})`;
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    /**
     * Clear filters
     */
    clearFilters() {
        this.searchTerm = '';
        this.filterType = 'all';

        const searchInput = this.container.querySelector('#customer-search');
        const typeFilter = this.container.querySelector('#customer-type-filter');

        if (searchInput) searchInput.value = '';
        if (typeFilter) typeFilter.value = 'all';

        this.applyFilters();
        this.refreshTable();
    }

    /**
     * Open customer modal for adding new customer
     */
    openCustomerModal(customer = null) {
        const modal = this.container.querySelector('#customer-modal');
        const modalTitle = this.container.querySelector('#customer-modal-title');
        const form = this.container.querySelector('#customer-form');

        // Reset form
        form.reset();

        if (customer) {
            // Edit mode
            modalTitle.textContent = 'تعديل بيانات العميل';
            this.populateForm(customer);
        } else {
            // Add mode
            modalTitle.textContent = 'إضافة عميل جديد';
            // Set default values
            const statusSelect = form.querySelector('#customer-status');
            if (statusSelect) statusSelect.value = 'active';
        }

        // Show modal
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);

        // Focus on first input
        const firstInput = form.querySelector('input[type="text"]');
        if (firstInput) firstInput.focus();
    }

    /**
     * Close customer modal
     */
    closeCustomerModal() {
        const modal = this.container.querySelector('#customer-modal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    /**
     * Populate form with customer data
     */
    populateForm(customer) {
        const form = this.container.querySelector('#customer-form');

        Object.keys(customer).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = customer[key] || '';
            }
        });

        // Handle company row visibility
        const companyRow = this.container.querySelector('#company-row');
        if (customer.type === 'company') {
            companyRow.style.display = 'block';
        }
    }

    /**
     * Save customer (add or update)
     */
    async saveCustomer() {
        const form = this.container.querySelector('#customer-form');
        const formData = new FormData(form);
        const customerData = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            customerData[key] = value.trim();
        }

        // Validation
        if (!this.validateCustomerData(customerData)) {
            return;
        }

        try {
            const dataManager = window.dataManager;
            const customerId = customerData.id;

            if (customerId) {
                // Update existing customer
                await dataManager.update('customers', customerId, customerData);
                window.uiManager?.showToast('تم تحديث بيانات العميل بنجاح', 'success');
            } else {
                // Add new customer
                await dataManager.create('customers', customerData);
                window.uiManager?.showToast('تم إضافة العميل بنجاح', 'success');
            }

            // Refresh data and close modal
            await this.loadCustomers();
            this.refreshTable();
            this.closeCustomerModal();

        } catch (error) {
            console.error('Failed to save customer:', error);
            window.uiManager?.showToast('خطأ في حفظ بيانات العميل', 'error');
        }
    }

    /**
     * Validate customer data
     */
    validateCustomerData(data) {
        const errors = [];

        // Required fields
        if (!data.name) errors.push('الاسم مطلوب');
        if (!data.type) errors.push('نوع العميل مطلوب');
        if (!data.phone) errors.push('رقم الهاتف مطلوب');

        // Phone validation
        if (data.phone && !/^05\d{8}$/.test(data.phone)) {
            errors.push('رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام');
        }

        // Email validation
        if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            errors.push('البريد الإلكتروني غير صحيح');
        }

        // Credit limit validation
        if (data.creditLimit && isNaN(parseFloat(data.creditLimit))) {
            errors.push('الحد الائتماني يجب أن يكون رقم صحيح');
        }

        if (errors.length > 0) {
            window.uiManager?.showToast(errors.join('<br>'), 'error');
            return false;
        }

        return true;
    }

    /**
     * View customer details
     */
    async viewCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            // Create customer details modal
            const modalContent = this.createCustomerDetailsModal(customer);

            window.uiManager?.showModal({
                title: `تفاصيل العميل: ${customer.name}`,
                content: modalContent,
                size: 'large',
                actions: [
                    {
                        text: 'تعديل',
                        class: 'btn-primary',
                        action: 'edit',
                        handler: () => {
                            window.uiManager?.closeAllModals();
                            this.editCustomer(customerId);
                        }
                    },
                    {
                        text: 'إغلاق',
                        class: 'btn-secondary',
                        action: 'close',
                        handler: (e, modalId) => window.uiManager?.closeModal(modalId)
                    }
                ]
            });

        } catch (error) {
            console.error('Failed to view customer:', error);
            window.uiManager?.showToast('خطأ في عرض تفاصيل العميل', 'error');
        }
    }

    /**
     * Create customer details modal content
     */
    createCustomerDetailsModal(customer) {
        return `
            <div class="customer-details">
                <div class="details-grid">
                    <div class="detail-section">
                        <h4>المعلومات الأساسية</h4>
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${customer.name || 'غير محدد'}</span>
                        </div>
                        <div class="detail-item">
                            <label>النوع:</label>
                            <span class="badge badge-${this.getTypeColor(customer.type)}">
                                ${this.getTypeLabel(customer.type)}
                            </span>
                        </div>
                        ${customer.company ? `
                            <div class="detail-item">
                                <label>الشركة:</label>
                                <span>${customer.company}</span>
                            </div>
                        ` : ''}
                        ${customer.taxId ? `
                            <div class="detail-item">
                                <label>الرقم الضريبي:</label>
                                <span>${customer.taxId}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>معلومات الاتصال</h4>
                        <div class="detail-item">
                            <label>الهاتف:</label>
                            <span><a href="tel:${customer.phone}">${customer.phone || 'غير محدد'}</a></span>
                        </div>
                        ${customer.phone2 ? `
                            <div class="detail-item">
                                <label>هاتف إضافي:</label>
                                <span><a href="tel:${customer.phone2}">${customer.phone2}</a></span>
                            </div>
                        ` : ''}
                        ${customer.email ? `
                            <div class="detail-item">
                                <label>البريد الإلكتروني:</label>
                                <span><a href="mailto:${customer.email}">${customer.email}</a></span>
                            </div>
                        ` : ''}
                        ${customer.website ? `
                            <div class="detail-item">
                                <label>الموقع الإلكتروني:</label>
                                <span><a href="${customer.website}" target="_blank">${customer.website}</a></span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>العنوان</h4>
                        ${customer.city ? `
                            <div class="detail-item">
                                <label>المدينة:</label>
                                <span>${customer.city}</span>
                            </div>
                        ` : ''}
                        ${customer.district ? `
                            <div class="detail-item">
                                <label>الحي:</label>
                                <span>${customer.district}</span>
                            </div>
                        ` : ''}
                        ${customer.address ? `
                            <div class="detail-item">
                                <label>العنوان التفصيلي:</label>
                                <span>${customer.address}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>معلومات إضافية</h4>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span class="badge badge-${customer.status === 'active' ? 'success' : 'warning'}">
                                ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                        ${customer.creditLimit ? `
                            <div class="detail-item">
                                <label>الحد الائتماني:</label>
                                <span>${parseFloat(customer.creditLimit).toFixed(2)} ر.س</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${this.formatDate(customer.createdAt)}</span>
                        </div>
                        ${customer.notes ? `
                            <div class="detail-item">
                                <label>ملاحظات:</label>
                                <span>${customer.notes}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Edit customer
     */
    async editCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            this.openCustomerModal(customer);

        } catch (error) {
            console.error('Failed to edit customer:', error);
            window.uiManager?.showToast('خطأ في تحميل بيانات العميل', 'error');
        }
    }

    /**
     * Delete customer
     */
    async deleteCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            const confirmed = await window.uiManager?.showConfirmation({
                title: 'تأكيد الحذف',
                message: `هل أنت متأكد من حذف العميل "${customer.name}"؟<br><small class="text-muted">لا يمكن التراجع عن هذا الإجراء</small>`,
                confirmText: 'حذف',
                cancelText: 'إلغاء',
                confirmClass: 'btn-error'
            });

            if (confirmed) {
                await dataManager.delete('customers', customerId);
                window.uiManager?.showToast('تم حذف العميل بنجاح', 'success');

                // Refresh data
                await this.loadCustomers();
                this.refreshTable();
            }

        } catch (error) {
            console.error('Failed to delete customer:', error);
            window.uiManager?.showToast('خطأ في حذف العميل', 'error');
        }
    }

    /**
     * Bulk delete customers
     */
    async bulkDeleteCustomers() {
        if (this.selectedCustomers.size === 0) {
            window.uiManager?.showToast('يرجى تحديد العملاء المراد حذفهم', 'warning');
            return;
        }

        const confirmed = await window.uiManager?.showConfirmation({
            title: 'تأكيد الحذف المتعدد',
            message: `هل أنت متأكد من حذف ${this.selectedCustomers.size} عميل؟<br><small class="text-muted">لا يمكن التراجع عن هذا الإجراء</small>`,
            confirmText: 'حذف الكل',
            cancelText: 'إلغاء',
            confirmClass: 'btn-error'
        });

        if (confirmed) {
            try {
                const dataManager = window.dataManager;
                const deletePromises = Array.from(this.selectedCustomers).map(id =>
                    dataManager.delete('customers', id)
                );

                await Promise.all(deletePromises);

                window.uiManager?.showToast(`تم حذف ${this.selectedCustomers.size} عميل بنجاح`, 'success');

                // Clear selection and refresh
                this.selectedCustomers.clear();
                await this.loadCustomers();
                this.refreshTable();
                this.updateBulkActions();

            } catch (error) {
                console.error('Failed to bulk delete customers:', error);
                window.uiManager?.showToast('خطأ في حذف العملاء', 'error');
            }
        }
    }

    /**
     * Export customers to CSV
     */
    exportCustomers() {
        try {
            const csvData = this.generateCSV();
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `customers_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                window.uiManager?.showToast('تم تصدير قائمة العملاء بنجاح', 'success');
            }
        } catch (error) {
            console.error('Failed to export customers:', error);
            window.uiManager?.showToast('خطأ في تصدير قائمة العملاء', 'error');
        }
    }

    /**
     * Generate CSV data
     */
    generateCSV() {
        const headers = [
            'الاسم',
            'النوع',
            'الشركة',
            'الهاتف',
            'هاتف إضافي',
            'البريد الإلكتروني',
            'المدينة',
            'الحي',
            'العنوان',
            'الحالة',
            'الحد الائتماني',
            'تاريخ الإضافة',
            'ملاحظات'
        ];

        const csvRows = [headers.join(',')];

        this.filteredCustomers.forEach(customer => {
            const row = [
                this.escapeCsvValue(customer.name || ''),
                this.escapeCsvValue(this.getTypeLabel(customer.type)),
                this.escapeCsvValue(customer.company || ''),
                this.escapeCsvValue(customer.phone || ''),
                this.escapeCsvValue(customer.phone2 || ''),
                this.escapeCsvValue(customer.email || ''),
                this.escapeCsvValue(customer.city || ''),
                this.escapeCsvValue(customer.district || ''),
                this.escapeCsvValue(customer.address || ''),
                this.escapeCsvValue(customer.status === 'active' ? 'نشط' : 'غير نشط'),
                this.escapeCsvValue(customer.creditLimit || ''),
                this.escapeCsvValue(this.formatDate(customer.createdAt)),
                this.escapeCsvValue(customer.notes || '')
            ];
            csvRows.push(row.join(','));
        });

        return '\uFEFF' + csvRows.join('\n'); // Add BOM for Arabic support
    }

    /**
     * Escape CSV value
     */
    escapeCsvValue(value) {
        if (value === null || value === undefined) return '';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
    }

    /**
     * Refresh table content
     */
    refreshTable() {
        // Update stats
        const statsContainer = this.container.querySelector('.customers-stats');
        if (statsContainer) {
            statsContainer.innerHTML = this.renderStatsCards();
        }

        // Update table
        const tableBody = this.container.querySelector('#customers-table-body');
        if (tableBody) {
            tableBody.innerHTML = this.renderCustomersTable();
        }

        // Update pagination
        const paginationContainer = this.container.querySelector('.pagination-container');
        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }

        // Update table title
        const tableTitle = this.container.querySelector('.table-title h3');
        if (tableTitle) {
            tableTitle.textContent = `قائمة العملاء (${this.filteredCustomers.length})`;
        }

        // Clear selections
        this.selectedCustomers.clear();
        this.updateBulkActions();
    }

    /**
     * Initialize component
     */
    async init() {
        console.log('Customer Management component initialized');

        // Load initial data if not already loaded
        if (this.customers.length === 0) {
            await this.loadCustomers();
            this.refreshTable();
        }
    }

    /**
     * Cleanup component
     */
    destroy() {
        if (this.container) {
            this.container.remove();
        }
    }
}

// Export the CustomerManagement component
export default CustomerManagement;
