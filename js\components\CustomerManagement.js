/**
 * Customer Management Component
 * إدارة العملاء
 */
export default class CustomerManagement {
    constructor() {
        console.log('👤 CustomerManagement constructor called');
        this.container = null;
        this.customers = [];
        this.filteredCustomers = [];
        this.currentFilter = 'all';
        this.currentSort = 'name';
        this.searchTerm = '';

        // أنواع العملاء
        this.customerTypes = [
            'فرد',
            'شركة',
            'مؤسسة',
            'عميل مميز'
        ];

        // حالات العملاء
        this.statuses = {
            'active': { name: 'نشط', color: '#28a745', icon: '✅' },
            'inactive': { name: 'غير نشط', color: '#6c757d', icon: '⏸️' },
            'vip': { name: 'مميز', color: '#ffc107', icon: '⭐' }
        };

        this.loadCustomers();
    }

    /**
     * Load customers from storage
     */
    loadCustomers() {
        const saved = localStorage.getItem('almaher_customers');
        if (saved) {
            this.customers = JSON.parse(saved);
        } else {
            this.createSampleData();
        }
        this.filteredCustomers = [...this.customers];
    }

    /**
     * Save customers to storage
     */
    saveCustomers() {
        localStorage.setItem('almaher_customers', JSON.stringify(this.customers));
    }

    /**
     * Create sample customers
     */
    createSampleData() {
        const sampleCustomers = [
            {
                id: 'CUST-001',
                name: 'أحمد محمد العلي',
                phone: '0501234567',
                mobile: '0501234567',
                email: '<EMAIL>',
                address: 'حي النزهة، شارع الملك فهد، الرياض',
                city: 'الرياض',
                customerType: 'فرد',
                status: 'active',
                notes: 'عميل منتظم، يطلب خدمات صيانة دورية',
                createdDate: '2024-01-01',
                lastOrderDate: '2024-01-10',
                totalOrders: 5,
                totalSpent: 2500
            },
            {
                id: 'CUST-002',
                name: 'شركة التقنية المتقدمة',
                contactPerson: 'سارة أحمد الغامدي',
                phone: '0112345678',
                mobile: '0551234567',
                email: '<EMAIL>',
                website: 'www.tech-advanced.com',
                address: 'طريق الملك عبدالعزيز، برج الأعمال، الدور 15',
                city: 'الرياض',
                customerType: 'شركة',
                status: 'vip',
                taxNumber: '300123456789003',
                notes: 'شركة تقنية كبيرة، عقد صيانة سنوي',
                createdDate: '2023-12-01',
                lastOrderDate: '2024-01-12',
                totalOrders: 12,
                totalSpent: 15000
            },
            {
                id: 'CUST-003',
                name: 'فاطمة سعد الزهراني',
                phone: '0509876543',
                mobile: '0509876543',
                email: '<EMAIL>',
                address: 'حي الملك فهد، شارع التحلية، جدة',
                city: 'جدة',
                customerType: 'فرد',
                status: 'active',
                notes: 'عميلة جديدة، مهتمة بخدمات التكييف',
                createdDate: '2024-01-05',
                lastOrderDate: '2024-01-08',
                totalOrders: 2,
                totalSpent: 800
            },
            {
                id: 'CUST-004',
                name: 'مؤسسة الخليج للمقاولات',
                contactPerson: 'محمد علي القحطاني',
                phone: '0138901234',
                mobile: '0551234567',
                email: '<EMAIL>',
                website: 'www.gulf-contracting.com',
                address: 'المنطقة الصناعية الثانية، الدمام',
                city: 'الدمام',
                customerType: 'مؤسسة',
                status: 'active',
                taxNumber: '300987654321003',
                notes: 'مؤسسة مقاولات، طلبات كبيرة للمشاريع',
                createdDate: '2023-11-15',
                lastOrderDate: '2024-01-06',
                totalOrders: 8,
                totalSpent: 25000
            }
        ];

        this.customers = sampleCustomers;
        this.saveCustomers();
    }

    /**
     * Render the customer management interface
     */
    render(container) {
        console.log('👤 CustomerManagement render called', container);
        this.container = container;
        this.renderContent();
        this.attachEventListeners();
        this.applyFilters();
        console.log('✅ CustomerManagement render completed');
    }

    /**
     * Render the main content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="customer-management">
                <!-- Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <span class="page-icon">👤</span>
                            إدارة العملاء
                        </h1>
                        <p class="page-description">إدارة وتتبع جميع العملاء والزبائن</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-customer-btn">
                            <span class="btn-icon">➕</span>
                            عميل جديد
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card active">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-active">0</div>
                            <div class="stat-label">عملاء نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card vip">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-vip">0</div>
                            <div class="stat-label">عملاء مميزين</div>
                        </div>
                    </div>
                    <div class="stat-card companies">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-companies">0</div>
                            <div class="stat-label">شركات</div>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label">إجمالي العملاء</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث في العملاء..." class="search-input">
                            <span class="search-icon">🔍</span>
                        </div>

                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="status-filter" class="filter-select">
                                <option value="all">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="vip">مميز</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>النوع:</label>
                            <select id="type-filter" class="filter-select">
                                <option value="all">جميع الأنواع</option>
                                ${this.customerTypes.map(type =>
                                    `<option value="${type}">${type}</option>`
                                ).join('')}
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>الترتيب:</label>
                            <select id="sort-select" class="filter-select">
                                <option value="name">حسب الاسم</option>
                                <option value="totalSpent">حسب المبلغ المنفق</option>
                                <option value="lastOrder">آخر طلب</option>
                                <option value="created">تاريخ الإضافة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Customers List -->
                <div class="customers-section">
                    <div class="section-header">
                        <h2>قائمة العملاء</h2>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="cards">
                                <span>📋</span> بطاقات
                            </button>
                            <button class="view-btn" data-view="table">
                                <span>📊</span> جدول
                            </button>
                        </div>
                    </div>

                    <div id="customers-container" class="customers-container">
                        <!-- Customers will be rendered here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update statistics
     */
    updateStatistics() {
        const stats = {
            active: this.customers.filter(c => c.status === 'active').length,
            vip: this.customers.filter(c => c.status === 'vip').length,
            companies: this.customers.filter(c => c.customerType === 'شركة' || c.customerType === 'مؤسسة').length,
            total: this.customers.length
        };

        document.getElementById('stat-active').textContent = stats.active;
        document.getElementById('stat-vip').textContent = stats.vip;
        document.getElementById('stat-companies').textContent = stats.companies;
        document.getElementById('stat-total').textContent = stats.total;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Add new customer button
        const addBtn = this.container.querySelector('#add-customer-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddCustomerModal());
        }

        // Search input
        const searchInput = this.container.querySelector('#search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.applyFilters();
            });
        }

        // Filter selects
        const statusFilter = this.container.querySelector('#status-filter');
        const typeFilter = this.container.querySelector('#type-filter');
        const sortSelect = this.container.querySelector('#sort-select');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.typeFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.applyFilters();
            });
        }

        // View toggle buttons
        const viewBtns = this.container.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentView = btn.dataset.view;
                this.renderCustomers();
            });
        });
    }

    /**
     * Apply filters and search
     */
    applyFilters() {
        let filtered = [...this.customers];

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(c => c.status === this.currentFilter);
        }

        // Apply type filter
        if (this.typeFilter && this.typeFilter !== 'all') {
            filtered = filtered.filter(c => c.customerType === this.typeFilter);
        }

        // Apply search
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(c =>
                c.name.toLowerCase().includes(term) ||
                c.phone.includes(term) ||
                c.email.toLowerCase().includes(term) ||
                c.address.toLowerCase().includes(term) ||
                c.city.toLowerCase().includes(term)
            );
        }

        // Apply sorting
        this.sortCustomers(filtered);

        this.filteredCustomers = filtered;
        this.renderCustomers();
        this.updateStatistics();
    }

    /**
     * Sort customers
     */
    sortCustomers(customers) {
        switch (this.currentSort) {
            case 'name':
                customers.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
                break;
            case 'totalSpent':
                customers.sort((a, b) => (b.totalSpent || 0) - (a.totalSpent || 0));
                break;
            case 'lastOrder':
                customers.sort((a, b) => new Date(b.lastOrderDate) - new Date(a.lastOrderDate));
                break;
            case 'created':
                customers.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));
                break;
        }
    }

    /**
     * Render customers list
     */
    renderCustomers() {
        const container = this.container.querySelector('#customers-container');
        if (!container) return;

        if (this.filteredCustomers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">👤</div>
                    <h3>لا يوجد عملاء</h3>
                    <p>لم يتم العثور على عملاء تطابق المعايير المحددة</p>
                    <button class="btn btn-primary" onclick="document.getElementById('add-customer-btn').click()">
                        إضافة عميل جديد
                    </button>
                </div>
            `;
            return;
        }

        const currentView = this.container.querySelector('.view-btn.active')?.dataset.view || 'cards';

        if (currentView === 'cards') {
            this.renderCardsView(container);
        } else {
            this.renderTableView(container);
        }
    }

    /**
     * Render cards view
     */
    renderCardsView(container) {
        container.innerHTML = `
            <div class="customers-grid">
                ${this.filteredCustomers.map(customer => this.renderCustomerCard(customer)).join('')}
            </div>
        `;
    }

    /**
     * Render single customer card
     */
    renderCustomerCard(customer) {
        const status = this.statuses[customer.status];

        return `
            <div class="customer-card" data-id="${customer.id}">
                <div class="card-header">
                    <div class="card-id">${customer.id}</div>
                    <div class="card-badges">
                        <span class="status-badge ${customer.status}" title="${status.name}">
                            ${status.icon} ${status.name}
                        </span>
                    </div>
                </div>

                <div class="card-content">
                    <h3 class="customer-name">${customer.name}</h3>
                    <div class="customer-type">${customer.customerType}</div>
                    ${customer.contactPerson ? `<div class="contact-person">👤 ${customer.contactPerson}</div>` : ''}

                    <div class="card-details">
                        <div class="detail-item">
                            <span class="detail-icon">📞</span>
                            <span class="detail-text">${customer.phone}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📧</span>
                            <span class="detail-text">${customer.email}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📍</span>
                            <span class="detail-text">${customer.city}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">💰</span>
                            <span class="detail-text">أنفق: ${customer.totalSpent.toLocaleString()} ر.س</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📋</span>
                            <span class="detail-text">الطلبات: ${customer.totalOrders}</span>
                        </div>
                    </div>
                </div>

                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="customerManagement.viewCustomer('${customer.id}')">
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="customerManagement.editCustomer('${customer.id}')">
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="customerManagement.createOrder('${customer.id}')">
                        أمر شغل
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Render table view
     */
    renderTableView(container) {
        container.innerHTML = `
            <div class="customers-table-container">
                <table class="customers-table">
                    <thead>
                        <tr>
                            <th>رقم العميل</th>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>المدينة</th>
                            <th>الحالة</th>
                            <th>إجمالي المنفق</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.filteredCustomers.map(customer => this.renderCustomerRow(customer)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Render single customer table row
     */
    renderCustomerRow(customer) {
        const status = this.statuses[customer.status];

        return `
            <tr class="customer-row" data-id="${customer.id}">
                <td class="customer-id">${customer.id}</td>
                <td class="customer-info">
                    <div class="customer-name">${customer.name}</div>
                    ${customer.contactPerson ? `<div class="customer-contact">${customer.contactPerson}</div>` : ''}
                </td>
                <td class="customer-type">${customer.customerType}</td>
                <td class="phone">${customer.phone}</td>
                <td class="email">${customer.email}</td>
                <td class="city">${customer.city}</td>
                <td class="status">
                    <span class="status-badge ${customer.status}">
                        ${status.icon} ${status.name}
                    </span>
                </td>
                <td class="total-spent">${customer.totalSpent.toLocaleString()} ر.س</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="customerManagement.viewCustomer('${customer.id}')" title="عرض">
                            👁️
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="customerManagement.editCustomer('${customer.id}')" title="تعديل">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="customerManagement.createOrder('${customer.id}')" title="أمر شغل">
                            🔧
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Show add customer modal
     */
    showAddCustomerModal() {
        window.uiManager?.showToast('سيتم إضافة نموذج إنشاء عميل جديد قريباً', 'info');
    }

    /**
     * View customer details
     */
    viewCustomer(customerId) {
        window.uiManager?.showToast(`عرض تفاصيل العميل: ${customerId}`, 'info');
    }

    /**
     * Edit customer
     */
    editCustomer(customerId) {
        window.uiManager?.showToast(`تعديل العميل: ${customerId}`, 'info');
    }

    /**
     * Create order for customer
     */
    createOrder(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (customer) {
            window.uiManager?.showToast(`إنشاء أمر شغل للعميل: ${customer.name}`, 'info');
            // يمكن هنا الانتقال لصفحة أوامر الشغل مع تحديد العميل
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Get customer by ID
     */
    getCustomer(id) {
        return this.customers.find(c => c.id === id);
    }

    /**
     * Add new customer
     */
    addCustomer(customerData) {
        const newCustomer = {
            id: this.generateCustomerId(),
            createdDate: new Date().toISOString().split('T')[0],
            status: 'active',
            totalOrders: 0,
            totalSpent: 0,
            ...customerData
        };

        this.customers.unshift(newCustomer);
        this.saveCustomers();
        this.applyFilters();

        return newCustomer;
    }

    /**
     * Update customer
     */
    updateCustomer(id, updates) {
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
            this.customers[index] = { ...this.customers[index], ...updates };
            this.saveCustomers();
            this.applyFilters();
            return this.customers[index];
        }
        return null;
    }

    /**
     * Delete customer
     */
    deleteCustomer(id) {
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
            this.customers.splice(index, 1);
            this.saveCustomers();
            this.applyFilters();
            return true;
        }
        return false;
    }

    /**
     * Generate unique customer ID
     */
    generateCustomerId() {
        const year = new Date().getFullYear();
        const existingIds = this.customers
            .map(c => c.id)
            .filter(id => id.startsWith(`CUST-${year}-`))
            .map(id => parseInt(id.split('-')[2]))
            .filter(num => !isNaN(num));

        const nextNumber = existingIds.length > 0 ? Math.max(...existingIds) + 1 : 1;
        return `CUST-${year}-${nextNumber.toString().padStart(3, '0')}`;
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.CustomerManagement = CustomerManagement;
console.log('👤 CustomerManagement class loaded and made globally available');

        // Search functionality
        const searchInput = this.container.querySelector('#customer-search');
        searchInput?.addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.applyFilters();
            this.refreshTable();
        });

        // Filter functionality
        const typeFilter = this.container.querySelector('#customer-type-filter');
        typeFilter?.addEventListener('change', (e) => {
            this.filterType = e.target.value;
            this.applyFilters();
            this.refreshTable();
        });

        const sortBy = this.container.querySelector('#sort-by');
        sortBy?.addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.loadCustomers();
            this.refreshTable();
        });

        // Clear filters
        const clearFiltersBtn = this.container.querySelector('#clear-filters-btn');
        clearFiltersBtn?.addEventListener('click', () => this.clearFilters());

        // Select all functionality
        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        selectAllCheckbox?.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));

        const selectAllBtn = this.container.querySelector('#select-all-btn');
        selectAllBtn?.addEventListener('click', () => this.toggleSelectAll(true));

        // Bulk delete
        const bulkDeleteBtn = this.container.querySelector('#bulk-delete-btn');
        bulkDeleteBtn?.addEventListener('click', () => this.bulkDeleteCustomers());

        // Table event delegation
        const tableBody = this.container.querySelector('#customers-table-body');
        tableBody?.addEventListener('click', (e) => this.handleTableClick(e));

        // Pagination
        const paginationContainer = this.container.querySelector('.pagination-container');
        paginationContainer?.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn')) {
                const page = parseInt(e.target.getAttribute('data-page'));
                this.currentPage = page;
                this.refreshTable();
            }
        });

        // Modal events
        this.setupModalEvents();

        // Customer type change in form
        const customerTypeSelect = this.container.querySelector('#customer-type');
        customerTypeSelect?.addEventListener('change', (e) => {
            const companyRow = this.container.querySelector('#company-row');
            if (e.target.value === 'company') {
                companyRow.style.display = 'block';
            } else {
                companyRow.style.display = 'none';
            }
        });
    }

    /**
     * Set up modal events
     */
    setupModalEvents() {
        const modal = this.container.querySelector('#customer-modal');
        const closeBtn = this.container.querySelector('#close-customer-modal');
        const cancelBtn = this.container.querySelector('#cancel-customer-btn');
        const saveBtn = this.container.querySelector('#save-customer-btn');

        closeBtn?.addEventListener('click', () => this.closeCustomerModal());
        cancelBtn?.addEventListener('click', () => this.closeCustomerModal());
        saveBtn?.addEventListener('click', () => this.saveCustomer());

        // Close modal when clicking backdrop
        modal?.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeCustomerModal();
            }
        });
    }

    /**
     * Handle table clicks (view, edit, delete)
     */
    handleTableClick(e) {
        const customerId = e.target.getAttribute('data-customer-id');

        if (e.target.classList.contains('view-customer-btn')) {
            this.viewCustomer(customerId);
        } else if (e.target.classList.contains('edit-customer-btn')) {
            this.editCustomer(customerId);
        } else if (e.target.classList.contains('delete-customer-btn')) {
            this.deleteCustomer(customerId);
        } else if (e.target.classList.contains('customer-checkbox')) {
            this.toggleCustomerSelection(customerId, e.target.checked);
        }
    }

    /**
     * Toggle customer selection
     */
    toggleCustomerSelection(customerId, selected) {
        if (selected) {
            this.selectedCustomers.add(customerId);
        } else {
            this.selectedCustomers.delete(customerId);
        }

        this.updateBulkActions();
    }

    /**
     * Toggle select all
     */
    toggleSelectAll(selectAll) {
        const checkboxes = this.container.querySelectorAll('.customer-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.toggleCustomerSelection(checkbox.value, selectAll);
        });

        const selectAllCheckbox = this.container.querySelector('#select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = selectAll;
        }
    }

    /**
     * Update bulk actions visibility
     */
    updateBulkActions() {
        const bulkDeleteBtn = this.container.querySelector('#bulk-delete-btn');
        if (this.selectedCustomers.size > 0) {
            bulkDeleteBtn.style.display = 'inline-flex';
            bulkDeleteBtn.textContent = `🗑️ حذف المحدد (${this.selectedCustomers.size})`;
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    /**
     * Clear filters
     */
    clearFilters() {
        this.searchTerm = '';
        this.filterType = 'all';

        const searchInput = this.container.querySelector('#customer-search');
        const typeFilter = this.container.querySelector('#customer-type-filter');

        if (searchInput) searchInput.value = '';
        if (typeFilter) typeFilter.value = 'all';

        this.applyFilters();
        this.refreshTable();
    }

    /**
     * Open customer modal for adding new customer
     */
    openCustomerModal(customer = null) {
        const modal = this.container.querySelector('#customer-modal');
        const modalTitle = this.container.querySelector('#customer-modal-title');
        const form = this.container.querySelector('#customer-form');

        // Reset form
        form.reset();

        if (customer) {
            // Edit mode
            modalTitle.textContent = 'تعديل بيانات العميل';
            this.populateForm(customer);
        } else {
            // Add mode
            modalTitle.textContent = 'إضافة عميل جديد';
            // Set default values
            const statusSelect = form.querySelector('#customer-status');
            if (statusSelect) statusSelect.value = 'active';
        }

        // Show modal
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);

        // Focus on first input
        const firstInput = form.querySelector('input[type="text"]');
        if (firstInput) firstInput.focus();
    }

    /**
     * Close customer modal
     */
    closeCustomerModal() {
        const modal = this.container.querySelector('#customer-modal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    /**
     * Populate form with customer data
     */
    populateForm(customer) {
        const form = this.container.querySelector('#customer-form');

        Object.keys(customer).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = customer[key] || '';
            }
        });

        // Handle company row visibility
        const companyRow = this.container.querySelector('#company-row');
        if (customer.type === 'company') {
            companyRow.style.display = 'block';
        }
    }

    /**
     * Save customer (add or update)
     */
    async saveCustomer() {
        const form = this.container.querySelector('#customer-form');
        const formData = new FormData(form);
        const customerData = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            customerData[key] = value.trim();
        }

        // Validation
        if (!this.validateCustomerData(customerData)) {
            return;
        }

        try {
            const dataManager = window.dataManager;
            const customerId = customerData.id;

            if (customerId) {
                // Update existing customer
                await dataManager.update('customers', customerId, customerData);
                window.uiManager?.showToast('تم تحديث بيانات العميل بنجاح', 'success');
            } else {
                // Add new customer
                await dataManager.create('customers', customerData);
                window.uiManager?.showToast('تم إضافة العميل بنجاح', 'success');
            }

            // Refresh data and close modal
            await this.loadCustomers();
            this.refreshTable();
            this.closeCustomerModal();

        } catch (error) {
            console.error('Failed to save customer:', error);
            window.uiManager?.showToast('خطأ في حفظ بيانات العميل', 'error');
        }
    }

    /**
     * Validate customer data
     */
    validateCustomerData(data) {
        const errors = [];

        // Required fields
        if (!data.name) errors.push('الاسم مطلوب');
        if (!data.type) errors.push('نوع العميل مطلوب');
        if (!data.phone) errors.push('رقم الهاتف مطلوب');

        // Phone validation
        if (data.phone && !/^05\d{8}$/.test(data.phone)) {
            errors.push('رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام');
        }

        // Email validation
        if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            errors.push('البريد الإلكتروني غير صحيح');
        }

        // Credit limit validation
        if (data.creditLimit && isNaN(parseFloat(data.creditLimit))) {
            errors.push('الحد الائتماني يجب أن يكون رقم صحيح');
        }

        if (errors.length > 0) {
            window.uiManager?.showToast(errors.join('<br>'), 'error');
            return false;
        }

        return true;
    }

    /**
     * View customer details
     */
    async viewCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            // Create customer details modal
            const modalContent = this.createCustomerDetailsModal(customer);

            window.uiManager?.showModal({
                title: `تفاصيل العميل: ${customer.name}`,
                content: modalContent,
                size: 'large',
                actions: [
                    {
                        text: 'تعديل',
                        class: 'btn-primary',
                        action: 'edit',
                        handler: () => {
                            window.uiManager?.closeAllModals();
                            this.editCustomer(customerId);
                        }
                    },
                    {
                        text: 'إغلاق',
                        class: 'btn-secondary',
                        action: 'close',
                        handler: (e, modalId) => window.uiManager?.closeModal(modalId)
                    }
                ]
            });

        } catch (error) {
            console.error('Failed to view customer:', error);
            window.uiManager?.showToast('خطأ في عرض تفاصيل العميل', 'error');
        }
    }

    /**
     * Create customer details modal content
     */
    createCustomerDetailsModal(customer) {
        return `
            <div class="customer-details">
                <div class="details-grid">
                    <div class="detail-section">
                        <h4>المعلومات الأساسية</h4>
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${customer.name || 'غير محدد'}</span>
                        </div>
                        <div class="detail-item">
                            <label>النوع:</label>
                            <span class="badge badge-${this.getTypeColor(customer.type)}">
                                ${this.getTypeLabel(customer.type)}
                            </span>
                        </div>
                        ${customer.company ? `
                            <div class="detail-item">
                                <label>الشركة:</label>
                                <span>${customer.company}</span>
                            </div>
                        ` : ''}
                        ${customer.taxId ? `
                            <div class="detail-item">
                                <label>الرقم الضريبي:</label>
                                <span>${customer.taxId}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>معلومات الاتصال</h4>
                        <div class="detail-item">
                            <label>الهاتف:</label>
                            <span><a href="tel:${customer.phone}">${customer.phone || 'غير محدد'}</a></span>
                        </div>
                        ${customer.phone2 ? `
                            <div class="detail-item">
                                <label>هاتف إضافي:</label>
                                <span><a href="tel:${customer.phone2}">${customer.phone2}</a></span>
                            </div>
                        ` : ''}
                        ${customer.email ? `
                            <div class="detail-item">
                                <label>البريد الإلكتروني:</label>
                                <span><a href="mailto:${customer.email}">${customer.email}</a></span>
                            </div>
                        ` : ''}
                        ${customer.website ? `
                            <div class="detail-item">
                                <label>الموقع الإلكتروني:</label>
                                <span><a href="${customer.website}" target="_blank">${customer.website}</a></span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>العنوان</h4>
                        ${customer.city ? `
                            <div class="detail-item">
                                <label>المدينة:</label>
                                <span>${customer.city}</span>
                            </div>
                        ` : ''}
                        ${customer.district ? `
                            <div class="detail-item">
                                <label>الحي:</label>
                                <span>${customer.district}</span>
                            </div>
                        ` : ''}
                        ${customer.address ? `
                            <div class="detail-item">
                                <label>العنوان التفصيلي:</label>
                                <span>${customer.address}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="detail-section">
                        <h4>معلومات إضافية</h4>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span class="badge badge-${customer.status === 'active' ? 'success' : 'warning'}">
                                ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                        ${customer.creditLimit ? `
                            <div class="detail-item">
                                <label>الحد الائتماني:</label>
                                <span>${parseFloat(customer.creditLimit).toFixed(2)} ر.س</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${this.formatDate(customer.createdAt)}</span>
                        </div>
                        ${customer.notes ? `
                            <div class="detail-item">
                                <label>ملاحظات:</label>
                                <span>${customer.notes}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Edit customer
     */
    async editCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            this.openCustomerModal(customer);

        } catch (error) {
            console.error('Failed to edit customer:', error);
            window.uiManager?.showToast('خطأ في تحميل بيانات العميل', 'error');
        }
    }

    /**
     * Delete customer
     */
    async deleteCustomer(customerId) {
        try {
            const dataManager = window.dataManager;
            const customer = await dataManager.read('customers', customerId);

            if (!customer) {
                window.uiManager?.showToast('العميل غير موجود', 'error');
                return;
            }

            const confirmed = await window.uiManager?.showConfirmation({
                title: 'تأكيد الحذف',
                message: `هل أنت متأكد من حذف العميل "${customer.name}"؟<br><small class="text-muted">لا يمكن التراجع عن هذا الإجراء</small>`,
                confirmText: 'حذف',
                cancelText: 'إلغاء',
                confirmClass: 'btn-error'
            });

            if (confirmed) {
                await dataManager.delete('customers', customerId);
                window.uiManager?.showToast('تم حذف العميل بنجاح', 'success');

                // Refresh data
                await this.loadCustomers();
                this.refreshTable();
            }

        } catch (error) {
            console.error('Failed to delete customer:', error);
            window.uiManager?.showToast('خطأ في حذف العميل', 'error');
        }
    }

    /**
     * Bulk delete customers
     */
    async bulkDeleteCustomers() {
        if (this.selectedCustomers.size === 0) {
            window.uiManager?.showToast('يرجى تحديد العملاء المراد حذفهم', 'warning');
            return;
        }

        const confirmed = await window.uiManager?.showConfirmation({
            title: 'تأكيد الحذف المتعدد',
            message: `هل أنت متأكد من حذف ${this.selectedCustomers.size} عميل؟<br><small class="text-muted">لا يمكن التراجع عن هذا الإجراء</small>`,
            confirmText: 'حذف الكل',
            cancelText: 'إلغاء',
            confirmClass: 'btn-error'
        });

        if (confirmed) {
            try {
                const dataManager = window.dataManager;
                const deletePromises = Array.from(this.selectedCustomers).map(id =>
                    dataManager.delete('customers', id)
                );

                await Promise.all(deletePromises);

                window.uiManager?.showToast(`تم حذف ${this.selectedCustomers.size} عميل بنجاح`, 'success');

                // Clear selection and refresh
                this.selectedCustomers.clear();
                await this.loadCustomers();
                this.refreshTable();
                this.updateBulkActions();

            } catch (error) {
                console.error('Failed to bulk delete customers:', error);
                window.uiManager?.showToast('خطأ في حذف العملاء', 'error');
            }
        }
    }

    /**
     * Export customers to CSV
     */
    exportCustomers() {
        try {
            const csvData = this.generateCSV();
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `customers_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                window.uiManager?.showToast('تم تصدير قائمة العملاء بنجاح', 'success');
            }
        } catch (error) {
            console.error('Failed to export customers:', error);
            window.uiManager?.showToast('خطأ في تصدير قائمة العملاء', 'error');
        }
    }

    /**
     * Generate CSV data
     */
    generateCSV() {
        const headers = [
            'الاسم',
            'النوع',
            'الشركة',
            'الهاتف',
            'هاتف إضافي',
            'البريد الإلكتروني',
            'المدينة',
            'الحي',
            'العنوان',
            'الحالة',
            'الحد الائتماني',
            'تاريخ الإضافة',
            'ملاحظات'
        ];

        const csvRows = [headers.join(',')];

        this.filteredCustomers.forEach(customer => {
            const row = [
                this.escapeCsvValue(customer.name || ''),
                this.escapeCsvValue(this.getTypeLabel(customer.type)),
                this.escapeCsvValue(customer.company || ''),
                this.escapeCsvValue(customer.phone || ''),
                this.escapeCsvValue(customer.phone2 || ''),
                this.escapeCsvValue(customer.email || ''),
                this.escapeCsvValue(customer.city || ''),
                this.escapeCsvValue(customer.district || ''),
                this.escapeCsvValue(customer.address || ''),
                this.escapeCsvValue(customer.status === 'active' ? 'نشط' : 'غير نشط'),
                this.escapeCsvValue(customer.creditLimit || ''),
                this.escapeCsvValue(this.formatDate(customer.createdAt)),
                this.escapeCsvValue(customer.notes || '')
            ];
            csvRows.push(row.join(','));
        });

        return '\uFEFF' + csvRows.join('\n'); // Add BOM for Arabic support
    }

    /**
     * Escape CSV value
     */
    escapeCsvValue(value) {
        if (value === null || value === undefined) return '';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
    }

    /**
     * Refresh table content
     */
    refreshTable() {
        // Update stats
        const statsContainer = this.container.querySelector('.customers-stats');
        if (statsContainer) {
            statsContainer.innerHTML = this.renderStatsCards();
        }

        // Update table
        const tableBody = this.container.querySelector('#customers-table-body');
        if (tableBody) {
            tableBody.innerHTML = this.renderCustomersTable();
        }

        // Update pagination
        const paginationContainer = this.container.querySelector('.pagination-container');
        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }

        // Update table title
        const tableTitle = this.container.querySelector('.table-title h3');
        if (tableTitle) {
            tableTitle.textContent = `قائمة العملاء (${this.filteredCustomers.length})`;
        }

        // Clear selections
        this.selectedCustomers.clear();
        this.updateBulkActions();
    }

    /**
     * Initialize component
     */
    async init() {
        console.log('Customer Management component initialized');

        // Load initial data if not already loaded
        if (this.customers.length === 0) {
            await this.loadCustomers();
            this.refreshTable();
        }
    }

    /**
     * Cleanup component
     */
    destroy() {
        if (this.container) {
            this.container.remove();
        }
    }
}

// Export the CustomerManagement component
export default CustomerManagement;
