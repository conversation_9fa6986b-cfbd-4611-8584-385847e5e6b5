/**
 * Work Order Management Component
 * إدارة أوامر الشغل
 */
export default class WorkOrderManagement {
    constructor() {
        console.log('🔧 WorkOrderManagement constructor called');
        this.container = null;
        this.workOrders = [];
        this.filteredWorkOrders = [];
        this.currentFilter = 'all';
        this.currentSort = 'newest';
        this.searchTerm = '';
        
        // حالات أوامر الشغل
        this.statuses = {
            'new': { name: 'جديد', color: '#007bff', icon: '🆕' },
            'in_progress': { name: 'قيد التنفيذ', color: '#ffc107', icon: '⚙️' },
            'completed': { name: 'مكتمل', color: '#28a745', icon: '✅' },
            'cancelled': { name: 'ملغي', color: '#dc3545', icon: '❌' }
        };
        
        // أنواع الخدمات
        this.serviceTypes = [
            'صيانة مكيفات',
            'أعمال كهربائية', 
            'أعمال سباكة',
            'صيانة عامة',
            'تركيب أجهزة',
            'خدمات أخرى'
        ];
        
        // مستويات الأولوية
        this.priorities = {
            'urgent': { name: 'عاجل', color: '#dc3545', icon: '🔥' },
            'normal': { name: 'عادي', color: '#007bff', icon: '📋' },
            'low': { name: 'منخفض', color: '#6c757d', icon: '📝' }
        };
        
        this.loadWorkOrders();
    }

    /**
     * Render the work order management interface
     */
    render(container) {
        console.log('🔧 WorkOrderManagement render called', container);
        this.container = container;
        this.renderContent();
        this.attachEventListeners();
        this.applyFilters();
        console.log('✅ WorkOrderManagement render completed');
    }

    /**
     * Load work orders from storage
     */
    loadWorkOrders() {
        const saved = localStorage.getItem('almaher_work_orders');
        if (saved) {
            this.workOrders = JSON.parse(saved);
        } else {
            // إنشاء بيانات تجريبية
            this.createSampleData();
        }
        this.filteredWorkOrders = [...this.workOrders];
    }

    /**
     * Save work orders to storage
     */
    saveWorkOrders() {
        localStorage.setItem('almaher_work_orders', JSON.stringify(this.workOrders));
    }

    /**
     * Create sample work orders
     */
    createSampleData() {
        const sampleOrders = [
            {
                id: 'WO-2024-001',
                customerId: 'CUST-001',
                customerName: 'أحمد محمد العلي',
                customerPhone: '0501234567',
                customerAddress: 'حي النزهة، الرياض',
                serviceType: 'صيانة مكيفات',
                description: 'صيانة دورية لمكيف سبليت 2 طن - تنظيف وفحص شامل',
                priority: 'normal',
                status: 'new',
                assignedTo: 'فني أحمد سالم',
                scheduledDate: '2024-01-15',
                scheduledTime: '09:00',
                createdDate: '2024-01-10',
                estimatedCost: 200,
                actualCost: 0,
                estimatedDuration: '2 ساعة',
                notes: 'العميل طلب الحضور في الصباح الباكر',
                materials: []
            },
            {
                id: 'WO-2024-002',
                customerId: 'CUST-002',
                customerName: 'فاطمة سعد الغامدي',
                customerPhone: '0509876543',
                customerAddress: 'حي الملك فهد، جدة',
                serviceType: 'أعمال كهربائية',
                description: 'إصلاح عطل في لوحة الكهرباء الرئيسية',
                priority: 'urgent',
                status: 'in_progress',
                assignedTo: 'فني محمد علي',
                scheduledDate: '2024-01-12',
                scheduledTime: '14:00',
                createdDate: '2024-01-11',
                estimatedCost: 350,
                actualCost: 320,
                estimatedDuration: '3 ساعات',
                notes: 'عطل عاجل - انقطاع كهرباء جزئي',
                materials: [
                    { name: 'قاطع كهرباء 32 أمبير', quantity: 2, cost: 80 },
                    { name: 'أسلاك كهربائية', quantity: 10, cost: 50 }
                ]
            },
            {
                id: 'WO-2024-003',
                customerId: 'CUST-003',
                customerName: 'شركة التقنية المتقدمة',
                customerPhone: '0112345678',
                customerAddress: 'طريق الملك عبدالعزيز، الرياض',
                serviceType: 'صيانة عامة',
                description: 'صيانة شاملة لمكاتب الشركة - تكييف وكهرباء وسباكة',
                priority: 'normal',
                status: 'completed',
                assignedTo: 'فريق الصيانة الشاملة',
                scheduledDate: '2024-01-08',
                scheduledTime: '08:00',
                createdDate: '2024-01-05',
                estimatedCost: 1500,
                actualCost: 1420,
                estimatedDuration: '8 ساعات',
                notes: 'عقد صيانة شهري - تم التنفيذ بنجاح',
                materials: [
                    { name: 'فلاتر مكيفات', quantity: 6, cost: 180 },
                    { name: 'مواد تنظيف', quantity: 1, cost: 120 },
                    { name: 'قطع غيار متنوعة', quantity: 1, cost: 200 }
                ]
            }
        ];
        
        this.workOrders = sampleOrders;
        this.saveWorkOrders();
    }

    /**
     * Render the main content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="work-order-management">
                <!-- Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <span class="page-icon">🔧</span>
                            إدارة أوامر الشغل
                        </h1>
                        <p class="page-description">إدارة وتتبع جميع أوامر الصيانة والخدمات</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-work-order-btn">
                            <span class="btn-icon">➕</span>
                            أمر شغل جديد
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card new">
                        <div class="stat-icon">🆕</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-new">0</div>
                            <div class="stat-label">أوامر جديدة</div>
                        </div>
                    </div>
                    <div class="stat-card in-progress">
                        <div class="stat-icon">⚙️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-in-progress">0</div>
                            <div class="stat-label">قيد التنفيذ</div>
                        </div>
                    </div>
                    <div class="stat-card completed">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-completed">0</div>
                            <div class="stat-label">مكتملة</div>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label">إجمالي الأوامر</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث في أوامر الشغل..." class="search-input">
                            <span class="search-icon">🔍</span>
                        </div>
                        
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="status-filter" class="filter-select">
                                <option value="all">جميع الحالات</option>
                                <option value="new">جديد</option>
                                <option value="in_progress">قيد التنفيذ</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>نوع الخدمة:</label>
                            <select id="service-filter" class="filter-select">
                                <option value="all">جميع الخدمات</option>
                                ${this.serviceTypes.map(service => 
                                    `<option value="${service}">${service}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>الترتيب:</label>
                            <select id="sort-select" class="filter-select">
                                <option value="newest">الأحدث أولاً</option>
                                <option value="oldest">الأقدم أولاً</option>
                                <option value="priority">حسب الأولوية</option>
                                <option value="status">حسب الحالة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Work Orders List -->
                <div class="work-orders-section">
                    <div class="section-header">
                        <h2>قائمة أوامر الشغل</h2>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="cards">
                                <span>📋</span> بطاقات
                            </button>
                            <button class="view-btn" data-view="table">
                                <span>📊</span> جدول
                            </button>
                        </div>
                    </div>
                    
                    <div id="work-orders-container" class="work-orders-container">
                        <!-- Work orders will be rendered here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update statistics
     */
    updateStatistics() {
        const stats = {
            new: this.workOrders.filter(wo => wo.status === 'new').length,
            in_progress: this.workOrders.filter(wo => wo.status === 'in_progress').length,
            completed: this.workOrders.filter(wo => wo.status === 'completed').length,
            total: this.workOrders.length
        };

        document.getElementById('stat-new').textContent = stats.new;
        document.getElementById('stat-in-progress').textContent = stats.in_progress;
        document.getElementById('stat-completed').textContent = stats.completed;
        document.getElementById('stat-total').textContent = stats.total;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Add new work order button
        const addBtn = this.container.querySelector('#add-work-order-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddWorkOrderModal());
        }

        // Search input
        const searchInput = this.container.querySelector('#search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.applyFilters();
            });
        }

        // Filter selects
        const statusFilter = this.container.querySelector('#status-filter');
        const serviceFilter = this.container.querySelector('#service-filter');
        const sortSelect = this.container.querySelector('#sort-select');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (serviceFilter) {
            serviceFilter.addEventListener('change', (e) => {
                this.serviceFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.applyFilters();
            });
        }

        // View toggle buttons
        const viewBtns = this.container.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentView = btn.dataset.view;
                this.renderWorkOrders();
            });
        });
    }

    /**
     * Apply filters and search
     */
    applyFilters() {
        let filtered = [...this.workOrders];

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(wo => wo.status === this.currentFilter);
        }

        // Apply service filter
        if (this.serviceFilter && this.serviceFilter !== 'all') {
            filtered = filtered.filter(wo => wo.serviceType === this.serviceFilter);
        }

        // Apply search
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(wo => 
                wo.customerName.toLowerCase().includes(term) ||
                wo.description.toLowerCase().includes(term) ||
                wo.id.toLowerCase().includes(term) ||
                wo.serviceType.toLowerCase().includes(term)
            );
        }

        // Apply sorting
        this.sortWorkOrders(filtered);

        this.filteredWorkOrders = filtered;
        this.renderWorkOrders();
        this.updateStatistics();
    }

    /**
     * Sort work orders
     */
    sortWorkOrders(orders) {
        switch (this.currentSort) {
            case 'newest':
                orders.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));
                break;
            case 'oldest':
                orders.sort((a, b) => new Date(a.createdDate) - new Date(b.createdDate));
                break;
            case 'priority':
                const priorityOrder = { urgent: 3, normal: 2, low: 1 };
                orders.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
                break;
            case 'status':
                const statusOrder = { new: 4, in_progress: 3, completed: 2, cancelled: 1 };
                orders.sort((a, b) => statusOrder[b.status] - statusOrder[a.status]);
                break;
        }
    }

    /**
     * Render work orders list
     */
    renderWorkOrders() {
        const container = this.container.querySelector('#work-orders-container');
        if (!container) return;

        if (this.filteredWorkOrders.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>لا توجد أوامر شغل</h3>
                    <p>لم يتم العثور على أوامر شغل تطابق المعايير المحددة</p>
                    <button class="btn btn-primary" onclick="document.getElementById('add-work-order-btn').click()">
                        إنشاء أمر شغل جديد
                    </button>
                </div>
            `;
            return;
        }

        const currentView = this.container.querySelector('.view-btn.active')?.dataset.view || 'cards';
        
        if (currentView === 'cards') {
            this.renderCardsView(container);
        } else {
            this.renderTableView(container);
        }
    }

    /**
     * Render cards view
     */
    renderCardsView(container) {
        container.innerHTML = `
            <div class="work-orders-grid">
                ${this.filteredWorkOrders.map(wo => this.renderWorkOrderCard(wo)).join('')}
            </div>
        `;

        // Attach card event listeners
        this.attachCardEventListeners();
    }

    /**
     * Render single work order card
     */
    renderWorkOrderCard(workOrder) {
        const status = this.statuses[workOrder.status];
        const priority = this.priorities[workOrder.priority];

        return `
            <div class="work-order-card" data-id="${workOrder.id}">
                <div class="card-header">
                    <div class="card-id">${workOrder.id}</div>
                    <div class="card-badges">
                        <span class="priority-badge ${workOrder.priority}" title="${priority.name}">
                            ${priority.icon}
                        </span>
                        <span class="status-badge ${workOrder.status}" title="${status.name}">
                            ${status.icon} ${status.name}
                        </span>
                    </div>
                </div>

                <div class="card-content">
                    <h3 class="customer-name">${workOrder.customerName}</h3>
                    <div class="service-type">${workOrder.serviceType}</div>
                    <p class="description">${workOrder.description}</p>

                    <div class="card-details">
                        <div class="detail-item">
                            <span class="detail-icon">📅</span>
                            <span class="detail-text">${this.formatDate(workOrder.scheduledDate)} ${workOrder.scheduledTime || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">👨‍🔧</span>
                            <span class="detail-text">${workOrder.assignedTo}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">💰</span>
                            <span class="detail-text">${workOrder.estimatedCost} ر.س</span>
                        </div>
                    </div>
                </div>

                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="workOrderManagement.viewWorkOrder('${workOrder.id}')">
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="workOrderManagement.editWorkOrder('${workOrder.id}')">
                        تعديل
                    </button>
                    <div class="status-dropdown">
                        <button class="btn btn-sm btn-outline" onclick="workOrderManagement.showStatusMenu('${workOrder.id}', event)">
                            تغيير الحالة ▼
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render table view
     */
    renderTableView(container) {
        container.innerHTML = `
            <div class="work-orders-table-container">
                <table class="work-orders-table">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>العميل</th>
                            <th>نوع الخدمة</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>التاريخ المجدول</th>
                            <th>المكلف</th>
                            <th>التكلفة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.filteredWorkOrders.map(wo => this.renderWorkOrderRow(wo)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        // Attach table event listeners
        this.attachTableEventListeners();
    }

    /**
     * Render single work order table row
     */
    renderWorkOrderRow(workOrder) {
        const status = this.statuses[workOrder.status];
        const priority = this.priorities[workOrder.priority];

        return `
            <tr class="work-order-row" data-id="${workOrder.id}">
                <td class="wo-id">${workOrder.id}</td>
                <td class="customer-info">
                    <div class="customer-name">${workOrder.customerName}</div>
                    <div class="customer-phone">${workOrder.customerPhone}</div>
                </td>
                <td class="service-type">${workOrder.serviceType}</td>
                <td class="status">
                    <span class="status-badge ${workOrder.status}">
                        ${status.icon} ${status.name}
                    </span>
                </td>
                <td class="priority">
                    <span class="priority-badge ${workOrder.priority}">
                        ${priority.icon} ${priority.name}
                    </span>
                </td>
                <td class="scheduled-date">${this.formatDate(workOrder.scheduledDate)}</td>
                <td class="assigned-to">${workOrder.assignedTo}</td>
                <td class="cost">${workOrder.estimatedCost} ر.س</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="workOrderManagement.viewWorkOrder('${workOrder.id}')" title="عرض">
                            👁️
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="workOrderManagement.editWorkOrder('${workOrder.id}')" title="تعديل">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="workOrderManagement.showStatusMenu('${workOrder.id}', event)" title="تغيير الحالة">
                            🔄
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Attach card event listeners
     */
    attachCardEventListeners() {
        // Card click to view details
        const cards = this.container.querySelectorAll('.work-order-card');
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.card-actions')) {
                    const workOrderId = card.dataset.id;
                    this.viewWorkOrder(workOrderId);
                }
            });
        });
    }

    /**
     * Attach table event listeners
     */
    attachTableEventListeners() {
        // Row click to view details
        const rows = this.container.querySelectorAll('.work-order-row');
        rows.forEach(row => {
            row.addEventListener('click', (e) => {
                if (!e.target.closest('.actions')) {
                    const workOrderId = row.dataset.id;
                    this.viewWorkOrder(workOrderId);
                }
            });
        });
    }

    /**
     * Show add work order modal
     */
    showAddWorkOrderModal() {
        // سنضيف هذه الوظيفة لاحقاً
        window.uiManager?.showToast('سيتم إضافة نموذج إنشاء أمر شغل جديد قريباً', 'info');
    }

    /**
     * View work order details
     */
    viewWorkOrder(workOrderId) {
        const workOrder = this.workOrders.find(wo => wo.id === workOrderId);
        if (!workOrder) return;

        // سنضيف عرض التفاصيل لاحقاً
        window.uiManager?.showToast(`عرض تفاصيل أمر الشغل: ${workOrderId}`, 'info');
    }

    /**
     * Edit work order
     */
    editWorkOrder(workOrderId) {
        const workOrder = this.workOrders.find(wo => wo.id === workOrderId);
        if (!workOrder) return;

        // سنضيف نموذج التعديل لاحقاً
        window.uiManager?.showToast(`تعديل أمر الشغل: ${workOrderId}`, 'info');
    }

    /**
     * Show status change menu
     */
    showStatusMenu(workOrderId, event) {
        event.stopPropagation();

        const workOrder = this.workOrders.find(wo => wo.id === workOrderId);
        if (!workOrder) return;

        // سنضيف قائمة تغيير الحالة لاحقاً
        window.uiManager?.showToast(`تغيير حالة أمر الشغل: ${workOrderId}`, 'info');
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Get work order by ID
     */
    getWorkOrder(id) {
        return this.workOrders.find(wo => wo.id === id);
    }

    /**
     * Add new work order
     */
    addWorkOrder(workOrderData) {
        const newWorkOrder = {
            id: this.generateWorkOrderId(),
            createdDate: new Date().toISOString().split('T')[0],
            status: 'new',
            actualCost: 0,
            materials: [],
            ...workOrderData
        };

        this.workOrders.unshift(newWorkOrder);
        this.saveWorkOrders();
        this.applyFilters();

        return newWorkOrder;
    }

    /**
     * Update work order
     */
    updateWorkOrder(id, updates) {
        const index = this.workOrders.findIndex(wo => wo.id === id);
        if (index !== -1) {
            this.workOrders[index] = { ...this.workOrders[index], ...updates };
            this.saveWorkOrders();
            this.applyFilters();
            return this.workOrders[index];
        }
        return null;
    }

    /**
     * Delete work order
     */
    deleteWorkOrder(id) {
        const index = this.workOrders.findIndex(wo => wo.id === id);
        if (index !== -1) {
            this.workOrders.splice(index, 1);
            this.saveWorkOrders();
            this.applyFilters();
            return true;
        }
        return false;
    }

    /**
     * Generate unique work order ID
     */
    generateWorkOrderId() {
        const year = new Date().getFullYear();
        const existingIds = this.workOrders
            .map(wo => wo.id)
            .filter(id => id.startsWith(`WO-${year}-`))
            .map(id => parseInt(id.split('-')[2]))
            .filter(num => !isNaN(num));

        const nextNumber = existingIds.length > 0 ? Math.max(...existingIds) + 1 : 1;
        return `WO-${year}-${nextNumber.toString().padStart(3, '0')}`;
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.WorkOrderManagement = WorkOrderManagement;
console.log('🔧 WorkOrderManagement class loaded and made globally available');
