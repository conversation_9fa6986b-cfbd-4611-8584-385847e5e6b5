/**
 * Themes - نظام الماهر للصيانة والخدمات
 * أنماط الثيمات المختلفة
 */

/* ========== منتقي الثيم ========== */
.theme-selector {
  position: relative;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
}

.theme-toggle:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-2);
  min-width: 150px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.theme-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.theme-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.theme-option:hover {
  background-color: var(--bg-secondary);
}

.theme-option.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.theme-preview {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  margin-left: var(--spacing-2);
  border: 2px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.theme-preview.light {
  background: linear-gradient(45deg, #ffffff 50%, #f5f5f5 50%);
}

.theme-preview.dark {
  background: linear-gradient(45deg, #212121 50%, #424242 50%);
}

.theme-preview.blue {
  background: linear-gradient(45deg, #2196F3 50%, #1976D2 50%);
}

.theme-preview.green {
  background: linear-gradient(45deg, #4CAF50 50%, #388E3C 50%);
}

.theme-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* ========== الثيم الفاتح (افتراضي) ========== */
:root {
  /* تم تعريفه في variables.css */
}

/* ========== الثيم الداكن ========== */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --bg-card: #2d2d2d;
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-tertiary: #b0b0b0;
  --text-inverse: #1a1a1a;
  --text-muted: #808080;
  
  --border-color: #404040;
  --border-light: #353535;
  --border-dark: #555555;
  
  /* ظلال Neumorphic للثيم الداكن */
  --neumorphic-shadow-light: 8px 8px 16px rgba(0, 0, 0, 0.4);
  --neumorphic-shadow-dark: -8px -8px 16px rgba(255, 255, 255, 0.02);
  --neumorphic-shadow-inset-light: inset 8px 8px 16px rgba(0, 0, 0, 0.4);
  --neumorphic-shadow-inset-dark: inset -8px -8px 16px rgba(255, 255, 255, 0.02);
  --neumorphic-shadow-pressed: inset 4px 4px 8px rgba(0, 0, 0, 0.5), inset -4px -4px 8px rgba(255, 255, 255, 0.03);
}

/* ========== الثيم الأزرق ========== */
[data-theme="blue"] {
  --primary-color: #1E88E5;
  --primary-light: #42A5F5;
  --primary-dark: #1565C0;
  
  --bg-primary: #E3F2FD;
  --bg-secondary: #BBDEFB;
  --bg-tertiary: #90CAF9;
  --bg-card: #ffffff;
  
  --text-primary: #0D47A1;
  --text-secondary: #1565C0;
  --text-tertiary: #1976D2;
  
  --border-color: #90CAF9;
  --border-light: #BBDEFB;
  --border-dark: #64B5F6;
  
  /* ظلال مخصصة للثيم الأزرق */
  --neumorphic-shadow-light: 8px 8px 16px rgba(30, 136, 229, 0.15);
  --neumorphic-shadow-dark: -8px -8px 16px rgba(255, 255, 255, 0.9);
}

/* ========== الثيم الأخضر ========== */
[data-theme="green"] {
  --primary-color: #43A047;
  --primary-light: #66BB6A;
  --primary-dark: #2E7D32;
  
  --bg-primary: #E8F5E8;
  --bg-secondary: #C8E6C9;
  --bg-tertiary: #A5D6A7;
  --bg-card: #ffffff;
  
  --text-primary: #1B5E20;
  --text-secondary: #2E7D32;
  --text-tertiary: #388E3C;
  
  --border-color: #A5D6A7;
  --border-light: #C8E6C9;
  --border-dark: #81C784;
  
  /* ظلال مخصصة للثيم الأخضر */
  --neumorphic-shadow-light: 8px 8px 16px rgba(67, 160, 71, 0.15);
  --neumorphic-shadow-dark: -8px -8px 16px rgba(255, 255, 255, 0.9);
}

/* ========== انتقالات الثيم ========== */
* {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

/* ========== تخصيصات خاصة بكل ثيم ========== */

/* الثيم الداكن - تخصيصات إضافية */
[data-theme="dark"] .card {
  background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
}

[data-theme="dark"] .btn {
  box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.5), -6px -6px 12px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .btn:hover {
  box-shadow: 8px 8px 16px rgba(0, 0, 0, 0.6), -8px -8px 16px rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .form-input {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-color: #404040;
}

[data-theme="dark"] .table th {
  background: linear-gradient(145deg, #404040, #2d2d2d);
}

/* الثيم الأزرق - تخصيصات إضافية */
[data-theme="blue"] .stat-card {
  background: linear-gradient(145deg, #ffffff, #E3F2FD);
  border: 1px solid rgba(30, 136, 229, 0.1);
}

[data-theme="blue"] .nav-link.active {
  background: linear-gradient(145deg, var(--primary-color), var(--primary-dark));
}

/* الثيم الأخضر - تخصيصات إضافية */
[data-theme="green"] .stat-card {
  background: linear-gradient(145deg, #ffffff, #E8F5E8);
  border: 1px solid rgba(67, 160, 71, 0.1);
}

[data-theme="green"] .nav-link.active {
  background: linear-gradient(145deg, var(--primary-color), var(--primary-dark));
}

/* ========== تأثيرات الانتقال بين الثيمات ========== */
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========== تخصيصات للطباعة ========== */
@media print {
  [data-theme="dark"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-card: #ffffff;
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #cccccc;
  }
}

/* ========== تخصيصات للشاشات عالية التباين ========== */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* ========== تخصيصات لتقليل الحركة ========== */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: var(--primary-color);
  }
}
