/* ===== SCROLL FIX CSS ===== */
/* This file contains critical fixes for scroll functionality */

/* Global scroll reset */
html {
  height: 100% !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
  scroll-behavior: smooth !important;
}

body {
  height: 100% !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* App container positioning fix */
.app-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  grid-template-areas: "header header" "sidebar main" !important;
  grid-template-columns: var(--sidebar-width) 1fr !important;
}

/* Header positioning fix */
.app-header {
  position: relative !important;
  z-index: 1000 !important;
  height: var(--header-height) !important;
  min-height: var(--header-height) !important;
  max-height: var(--header-height) !important;
  overflow: hidden !important;
}

/* Main content scroll fix */
.main-content {
  position: relative !important;
  height: 100% !important;
  max-height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scroll-behavior: smooth !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* Sidebar scroll fix */
.sidebar {
  position: relative !important;
  height: 100% !important;
  max-height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scroll-behavior: smooth !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* Content wrapper fix */
.content-wrapper {
  position: relative !important;
  width: 100% !important;
  min-height: 100% !important;
  padding: var(--space-lg) !important;
  box-sizing: border-box !important;
}

/* Page content fix */
.page-content {
  position: relative !important;
  width: 100% !important;
  min-height: calc(100vh - var(--header-height) - 4rem) !important;
  padding-bottom: var(--space-xl) !important;
}

/* Custom scrollbar styles */
.main-content::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track,
.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.main-content::-webkit-scrollbar-thumb:hover,
.sidebar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox scrollbar */
.main-content,
.sidebar {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Mobile fixes */
@media (max-width: 768px) {
  .app-container {
    grid-template-rows: var(--header-height) calc(100vh - var(--header-height)) !important;
    grid-template-areas: "header" "main" !important;
    grid-template-columns: 1fr !important;
  }

  .sidebar {
    position: fixed !important;
    top: var(--header-height) !important;
    right: -100% !important;
    bottom: 0 !important;
    width: var(--sidebar-width) !important;
    height: calc(100vh - var(--header-height)) !important;
    max-height: calc(100vh - var(--header-height)) !important;
    z-index: 1050 !important;
    transition: right 0.3s ease !important;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1) !important;
  }

  .sidebar.open {
    right: 0 !important;
  }
  
  .main-content {
    height: calc(100vh - var(--header-height)) !important;
    max-height: calc(100vh - var(--header-height)) !important;
  }
  
  .content-wrapper {
    padding: var(--space-md) !important;
    min-height: calc(100vh - var(--header-height) - 2rem) !important;
  }
  
  .page-content {
    min-height: calc(100vh - var(--header-height) - 2rem) !important;
  }
}

/* Ensure scroll works on touch devices */
.main-content,
.sidebar,
.content-wrapper {
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
  scroll-behavior: smooth !important;
}

/* Force scroll wheel to work */
.main-content {
  pointer-events: auto !important;
  touch-action: pan-y !important;
}

.sidebar {
  pointer-events: auto !important;
  touch-action: pan-y !important;
}

/* Ensure mouse wheel events work */
.main-content,
.sidebar {
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  scrollbar-width: thin !important;
}

/* Fix for Chrome/Safari */
.main-content::-webkit-scrollbar {
  width: 8px !important;
  display: block !important;
}

.sidebar::-webkit-scrollbar {
  width: 8px !important;
  display: block !important;
}

/* Force hardware acceleration for smooth scrolling */
.main-content,
.sidebar {
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Prevent content from being hidden behind header */
.content-wrapper {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.page-content {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Breadcrumb positioning */
.breadcrumb {
  position: relative !important;
  z-index: 1 !important;
  margin-bottom: var(--space-lg) !important;
}

/* Fix for any potential z-index issues */
.app-header {
  z-index: 1000 !important;
}

.sidebar {
  z-index: 999 !important;
}

.main-content {
  z-index: 1 !important;
}

/* Ensure modal and toast containers don't interfere */
.modal-container,
.toast-container {
  position: fixed !important;
  z-index: 2000 !important;
  pointer-events: none !important;
}

.modal-container > *,
.toast-container > * {
  pointer-events: auto !important;
}

/* Additional mobile scroll fixes */
@media (max-width: 480px) {
  .content-wrapper {
    padding: var(--space-sm) !important;
  }
  
  .page-content {
    min-height: calc(100vh - var(--header-height) - 1rem) !important;
  }
}

/* Tablet specific fixes */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    padding: var(--space-md) !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1400px) {
  .content-wrapper {
    max-width: 1400px !important;
    margin: 0 auto !important;
  }
}

/* Print styles - hide scrollbars */
@media print {
  .main-content,
  .sidebar {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
  }
  
  .app-container {
    position: static !important;
    height: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .main-content::-webkit-scrollbar-thumb,
  .sidebar::-webkit-scrollbar-thumb {
    background: #000 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .main-content,
  .sidebar,
  .content-wrapper {
    scroll-behavior: auto !important;
  }
}

/* ===== RTL FIXES ===== */
/* Ensure RTL layout is properly applied */
html[dir="rtl"] .app-container,
.app-container {
  grid-template-areas: "header header" "sidebar main" !important;
  grid-template-columns: var(--sidebar-width) 1fr !important;
}

html[dir="rtl"] .sidebar,
.sidebar {
  border-left: 1px solid #e9ecef !important;
  border-right: none !important;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1) !important;
}

html[dir="rtl"] .nav-link,
.nav-link {
  border-left: 3px solid transparent !important;
  border-right: none !important;
  direction: rtl !important;
  text-align: right !important;
}

html[dir="rtl"] .nav-item.active > .nav-link,
.nav-item.active > .nav-link {
  border-left-color: #007bff !important;
  border-right-color: transparent !important;
}

html[dir="rtl"] .nav-submenu .nav-link,
.nav-submenu .nav-link {
  padding-left: calc(var(--space-lg) + var(--space-xl)) !important;
  padding-right: var(--space-lg) !important;
}

html[dir="rtl"] .sidebar-toggle,
.sidebar-toggle {
  margin-right: auto !important;
  margin-left: 0 !important;
}

/* Mobile RTL fixes */
@media (max-width: 768px) {
  html[dir="rtl"] .app-container,
  .app-container {
    grid-template-areas: "header" "main" !important;
    grid-template-columns: 1fr !important;
  }

  html[dir="rtl"] .sidebar,
  .sidebar {
    right: -100% !important;
    left: auto !important;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1) !important;
  }

  html[dir="rtl"] .sidebar.open,
  .sidebar.open {
    right: 0 !important;
    left: auto !important;
  }
}
