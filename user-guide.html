<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المستخدم - نظام الماهر للصيانة والخدمات ERP</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.8;
            color: #333;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            border-bottom: 3px solid #2196F3;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 3em;
            color: #2196F3;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 2.5em;
            color: #1976D2;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .version {
            background: #E3F2FD;
            color: #1976D2;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: 600;
        }
        
        .toc {
            background: #f5f5f5;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .toc h2 {
            color: #1976D2;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-right: 4px solid #2196F3;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .toc a:hover {
            color: #2196F3;
        }
        
        .section {
            margin-bottom: 50px;
            page-break-inside: avoid;
        }
        
        .section h1 {
            color: #1976D2;
            font-size: 2.2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #E3F2FD;
        }
        
        .section h2 {
            color: #2196F3;
            font-size: 1.8em;
            margin: 30px 0 15px 0;
        }
        
        .section h3 {
            color: #1976D2;
            font-size: 1.4em;
            margin: 20px 0 10px 0;
        }
        
        .section p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid #2196F3;
        }
        
        .feature-card h4 {
            color: #1976D2;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .feature-card .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        
        .screenshot {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .screenshot img {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .screenshot-caption {
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
        
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
        }
        
        .warning {
            background: #FFF3CD;
            border: 1px solid #FFEAA7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-right: 4px solid #FFC107;
        }
        
        .warning .icon {
            color: #FF9800;
            font-size: 1.2em;
            margin-left: 10px;
        }
        
        .info {
            background: #E3F2FD;
            border: 1px solid #BBDEFB;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-right: 4px solid #2196F3;
        }
        
        .info .icon {
            color: #2196F3;
            font-size: 1.2em;
            margin-left: 10px;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .step {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #4CAF50;
            position: relative;
        }
        
        .step::before {
            content: counter(step-counter);
            position: absolute;
            right: -15px;
            top: 15px;
            background: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .step h4 {
            color: #2E7D32;
            margin-bottom: 10px;
            margin-right: 20px;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #2196F3;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f5f5f5;
        }
        
        .footer {
            text-align: center;
            padding: 40px 0;
            border-top: 2px solid #E3F2FD;
            margin-top: 50px;
            color: #666;
        }
        
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                max-width: none;
            }
            .section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🔧</div>
            <h1 class="title">دليل المستخدم</h1>
            <h2 class="subtitle">نظام الماهر للصيانة والخدمات ERP</h2>
            <div class="version">الإصدار 1.0 - يناير 2025</div>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h2>📋 فهرس المحتويات</h2>
            <ul>
                <li><a href="#overview">🏠 نظرة عامة على النظام</a></li>
                <li><a href="#installation">⚙️ متطلبات التشغيل والتثبيت</a></li>
                <li><a href="#getting-started">🚀 البدء مع النظام</a></li>
                <li><a href="#modules">📦 دليل الوحدات</a></li>
                <li><a href="#usage">📖 دليل الاستخدام</a></li>
                <li><a href="#technical">🔧 المعلومات التقنية</a></li>
                <li><a href="#troubleshooting">🛠️ استكشاف الأخطاء</a></li>
                <li><a href="#support">📞 الدعم والمساعدة</a></li>
            </ul>
        </div>

        <!-- Section 1: Overview -->
        <div class="section" id="overview">
            <h1>🏠 نظرة عامة على النظام</h1>
            
            <h2>ما هو نظام الماهر للصيانة والخدمات ERP؟</h2>
            <p>
                نظام الماهر للصيانة والخدمات ERP هو نظام إدارة موارد المؤسسة شامل ومتكامل، مصمم خصيصاً لشركات الصيانة والخدمات. 
                يوفر النظام حلولاً متقدمة لإدارة جميع جوانب العمل من العملاء والموردين إلى الفواتير والتقارير.
            </p>

            <h2>🎯 أهداف النظام</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">👥</span>
                    <h4>إدارة العملاء</h4>
                    <p>تتبع شامل لبيانات العملاء وتاريخ التعاملات والطلبات</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🏢</span>
                    <h4>إدارة الموردين</h4>
                    <p>متابعة الموردين والمشتريات وإدارة المخزون</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📄</span>
                    <h4>إدارة الفواتير</h4>
                    <p>إنشاء وتتبع الفواتير والمدفوعات بسهولة</p>
                </div>
                <div class="feature-card">
                    <span class="icon">🔧</span>
                    <h4>أوامر الشغل</h4>
                    <p>تنظيم وتتبع أوامر الصيانة والخدمات</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📊</span>
                    <h4>التقارير والإحصائيات</h4>
                    <p>تقارير شاملة لاتخاذ القرارات الصحيحة</p>
                </div>
                <div class="feature-card">
                    <span class="icon">⚙️</span>
                    <h4>إعدادات مرنة</h4>
                    <p>تخصيص النظام حسب احتياجات الشركة</p>
                </div>
            </div>

            <h2>✨ المميزات الرئيسية</h2>
            <ul>
                <li><strong>واجهة عربية كاملة:</strong> تصميم يدعم اللغة العربية بالكامل مع اتجاه RTL</li>
                <li><strong>تصميم neumorphic حديث:</strong> واجهة عصرية وسهلة الاستخدام</li>
                <li><strong>نظام تنقل سلس:</strong> تنقل سريع بين الوحدات المختلفة</li>
                <li><strong>بيانات محلية آمنة:</strong> حفظ البيانات محلياً في المتصفح</li>
                <li><strong>تقارير تفاعلية:</strong> إحصائيات ورسوم بيانية مفصلة</li>
                <li><strong>دعم متعدد الثيمات:</strong> اختيار من بين عدة ثيمات ملونة</li>
            </ul>

            <div class="info">
                <span class="icon">💡</span>
                <strong>ملاحظة:</strong> هذا النظام مصمم للعمل محلياً في المتصفح ولا يتطلب خادم قاعدة بيانات خارجي.
            </div>
        </div>

        <!-- Section 2: Installation -->
        <div class="section" id="installation">
            <h1>⚙️ متطلبات التشغيل والتثبيت</h1>
            
            <h2>📋 متطلبات النظام</h2>
            
            <h3>متطلبات الأجهزة:</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>المكون</th>
                            <th>الحد الأدنى</th>
                            <th>المُوصى به</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>المعالج</td>
                            <td>Intel Core i3 أو AMD Ryzen 3</td>
                            <td>Intel Core i5 أو AMD Ryzen 5</td>
                        </tr>
                        <tr>
                            <td>الذاكرة</td>
                            <td>4 جيجابايت RAM</td>
                            <td>8 جيجابايت RAM أو أكثر</td>
                        </tr>
                        <tr>
                            <td>مساحة التخزين</td>
                            <td>100 ميجابايت</td>
                            <td>500 ميجابايت</td>
                        </tr>
                        <tr>
                            <td>الشاشة</td>
                            <td>1366x768</td>
                            <td>1920x1080 أو أعلى</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>متطلبات البرمجيات:</h3>
            <ul>
                <li><strong>نظام التشغيل:</strong> Windows 10/11، macOS 10.14+، أو Linux Ubuntu 18.04+</li>
                <li><strong>Node.js:</strong> الإصدار 14.0 أو أحدث</li>
                <li><strong>متصفح الويب:</strong> Chrome 90+، Firefox 88+، Safari 14+، أو Edge 90+</li>
                <li><strong>اتصال الإنترنت:</strong> مطلوب للتحميل الأولي فقط</li>
            </ul>

            <h2>🔧 خطوات التثبيت</h2>
            
            <div class="steps">
                <div class="step">
                    <h4>تحميل الملفات</h4>
                    <p>قم بتحميل ملفات النظام من المصدر المحدد وفك الضغط في مجلد منفصل.</p>
                </div>
                
                <div class="step">
                    <h4>تثبيت Node.js</h4>
                    <p>تأكد من تثبيت Node.js على جهازك. يمكنك تحميله من الموقع الرسمي:</p>
                    <div class="code-block">https://nodejs.org</div>
                </div>
                
                <div class="step">
                    <h4>فتح Terminal/Command Prompt</h4>
                    <p>افتح نافذة الأوامر وانتقل إلى مجلد النظام:</p>
                    <div class="code-block">cd "مسار مجلد النظام"</div>
                </div>
                
                <div class="step">
                    <h4>تشغيل الخادم المحلي</h4>
                    <p>استخدم أحد الأوامر التالية لتشغيل الخادم:</p>
                    <div class="code-block">
npx serve . -p 3000
# أو
npx http-server -p 3000
# أو
python -m http.server 3000
                    </div>
                </div>
                
                <div class="step">
                    <h4>فتح النظام في المتصفح</h4>
                    <p>افتح المتصفح وانتقل إلى العنوان:</p>
                    <div class="code-block">http://localhost:3000</div>
                </div>
            </div>

            <div class="warning">
                <span class="icon">⚠️</span>
                <strong>تنبيه:</strong> تأكد من عدم إغلاق نافذة الأوامر أثناء استخدام النظام، حيث أنها تشغل الخادم المحلي.
            </div>
        </div>

        <!-- Section 3: Getting Started -->
        <div class="section" id="getting-started">
            <h1>🚀 البدء مع النظام</h1>

            <h2>🏠 الواجهة الرئيسية</h2>
            <p>
                عند فتح النظام لأول مرة، ستظهر لك لوحة التحكم الرئيسية التي تحتوي على نظرة عامة شاملة
                على حالة النشاط التجاري وإحصائيات سريعة لجميع الوحدات.
            </p>

            <div class="screenshot">
                <div style="background: #f0f0f0; padding: 40px; border-radius: 8px; text-align: center;">
                    <h3 style="color: #666;">📊 لوحة التحكم الرئيسية</h3>
                    <p style="color: #999;">هنا ستظهر لقطة شاشة للوحة التحكم</p>
                </div>
                <div class="screenshot-caption">الشكل 1: لوحة التحكم الرئيسية لنظام الماهر</div>
            </div>

            <h2>🧭 التنقل في النظام</h2>

            <h3>القائمة الجانبية:</h3>
            <p>تحتوي القائمة الجانبية على جميع وحدات النظام المنظمة في مجموعات:</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">👥</span>
                    <h4>إدارة العلاقات</h4>
                    <ul>
                        <li>العملاء</li>
                        <li>الموردين</li>
                        <li>جهات الاتصال</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <span class="icon">💼</span>
                    <h4>إدارة الأعمال</h4>
                    <ul>
                        <li>المنتجات</li>
                        <li>أوامر الشغل</li>
                        <li>المشتريات</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <span class="icon">💰</span>
                    <h4>إدارة المالية</h4>
                    <ul>
                        <li>الفواتير</li>
                        <li>عروض الأسعار</li>
                        <li>المدفوعات</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <span class="icon">📊</span>
                    <h4>التقارير والإعدادات</h4>
                    <ul>
                        <li>التقارير</li>
                        <li>الإعدادات</li>
                    </ul>
                </div>
            </div>

            <h3>شريط العلوي:</h3>
            <ul>
                <li><strong>اختيار الثيم:</strong> تغيير ألوان النظام</li>
                <li><strong>الإشعارات:</strong> تنبيهات النظام</li>
                <li><strong>الملف الشخصي:</strong> إعدادات المستخدم</li>
            </ul>

            <h2>📱 استخدام النظام على الأجهزة المختلفة</h2>

            <h3>على أجهزة الكمبيوتر:</h3>
            <ul>
                <li>استخدم الماوس للنقر والتنقل</li>
                <li>استخدم لوحة المفاتيح للبحث والإدخال</li>
                <li>استخدم عجلة الماوس للتمرير</li>
            </ul>

            <h3>على الأجهزة اللوحية والهواتف:</h3>
            <ul>
                <li>استخدم اللمس للتنقل</li>
                <li>اسحب للتمرير</li>
                <li>اضغط مطولاً لعرض القوائم السياقية</li>
            </ul>

            <div class="info">
                <span class="icon">💡</span>
                <strong>نصيحة:</strong> النظام مُحسن للعمل على جميع أحجام الشاشات ويتكيف تلقائياً مع حجم الجهاز.
            </div>

            <h2>🎨 تخصيص المظهر</h2>

            <h3>اختيار الثيم:</h3>
            <p>يمكنك اختيار من بين عدة ثيمات ملونة:</p>
            <ul>
                <li><strong>الأزرق الكلاسيكي:</strong> الثيم الافتراضي</li>
                <li><strong>الأخضر الطبيعي:</strong> مناسب للعمل الطويل</li>
                <li><strong>البنفسجي الأنيق:</strong> مظهر عصري</li>
                <li><strong>البرتقالي النشط:</strong> مظهر حيوي</li>
                <li><strong>الوردي الناعم:</strong> مظهر هادئ</li>
            </ul>

            <div class="steps">
                <div class="step">
                    <h4>فتح اختيار الثيم</h4>
                    <p>انقر على أيقونة الثيم في الشريط العلوي</p>
                </div>

                <div class="step">
                    <h4>اختيار اللون المفضل</h4>
                    <p>انقر على اللون الذي تريده من القائمة</p>
                </div>

                <div class="step">
                    <h4>حفظ الاختيار</h4>
                    <p>سيتم حفظ اختيارك تلقائياً وتطبيقه على النظام</p>
                </div>
            </div>
        </div>

        <!-- Section 4: Modules Guide -->
        <div class="section" id="modules">
            <h1>📦 دليل الوحدات</h1>

            <h2>👥 وحدة إدارة العملاء</h2>
            <p>
                وحدة إدارة العملاء هي قلب النظام، حيث يمكنك تتبع جميع بيانات العملاء،
                تاريخ التعاملات، والطلبات السابقة.
            </p>

            <h3>الميزات الرئيسية:</h3>
            <ul>
                <li><strong>قاعدة بيانات شاملة:</strong> حفظ جميع بيانات العملاء</li>
                <li><strong>تصنيف العملاء:</strong> فرد، شركة، مؤسسة، عميل مميز</li>
                <li><strong>تتبع الحالة:</strong> نشط، غير نشط، مميز</li>
                <li><strong>إحصائيات مفصلة:</strong> إجمالي الطلبات والمبالغ المنفقة</li>
                <li><strong>بحث متقدم:</strong> البحث بالاسم، الهاتف، أو المدينة</li>
            </ul>

            <div class="screenshot">
                <div style="background: #f0f0f0; padding: 40px; border-radius: 8px; text-align: center;">
                    <h3 style="color: #666;">👥 واجهة إدارة العملاء</h3>
                    <p style="color: #999;">هنا ستظهر لقطة شاشة لواجهة العملاء</p>
                </div>
                <div class="screenshot-caption">الشكل 2: واجهة إدارة العملاء مع البطاقات التفاعلية</div>
            </div>

            <h3>البيانات المحفوظة لكل عميل:</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الوصف</th>
                            <th>مطلوب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>رقم العميل</td>
                            <td>رقم تعريف فريد للعميل</td>
                            <td>نعم</td>
                        </tr>
                        <tr>
                            <td>الاسم</td>
                            <td>اسم العميل أو الشركة</td>
                            <td>نعم</td>
                        </tr>
                        <tr>
                            <td>رقم الهاتف</td>
                            <td>رقم الاتصال الأساسي</td>
                            <td>نعم</td>
                        </tr>
                        <tr>
                            <td>البريد الإلكتروني</td>
                            <td>عنوان البريد الإلكتروني</td>
                            <td>لا</td>
                        </tr>
                        <tr>
                            <td>العنوان</td>
                            <td>العنوان الكامل</td>
                            <td>لا</td>
                        </tr>
                        <tr>
                            <td>المدينة</td>
                            <td>مدينة العميل</td>
                            <td>لا</td>
                        </tr>
                        <tr>
                            <td>نوع العميل</td>
                            <td>فرد، شركة، مؤسسة، عميل مميز</td>
                            <td>نعم</td>
                        </tr>
                        <tr>
                            <td>الحالة</td>
                            <td>نشط، غير نشط، مميز</td>
                            <td>نعم</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h2>🏢 وحدة إدارة الموردين</h2>
            <p>
                تساعدك هذه الوحدة في إدارة جميع الموردين والشركات التي تتعامل معها،
                مع تتبع الأرصدة والمشتريات.
            </p>

            <h3>الميزات الرئيسية:</h3>
            <ul>
                <li><strong>إدارة بيانات الموردين:</strong> معلومات كاملة عن كل مورد</li>
                <li><strong>تتبع الأرصدة:</strong> متابعة المبالغ المستحقة</li>
                <li><strong>تاريخ التعاملات:</strong> سجل كامل للمشتريات</li>
                <li><strong>تقييم الموردين:</strong> نظام تقييم الأداء</li>
                <li><strong>إدارة العقود:</strong> تتبع العقود والاتفاقيات</li>
            </ul>

            <h2>📞 وحدة إدارة جهات الاتصال</h2>
            <p>
                دليل شامل لجميع جهات الاتصال المهمة في عملك، من العملاء إلى الموردين
                والشركاء التجاريين.
            </p>

            <h3>أنواع جهات الاتصال:</h3>
            <ul>
                <li><strong>عملاء:</strong> جهات اتصال العملاء الرئيسيين</li>
                <li><strong>موردين:</strong> ممثلي الشركات الموردة</li>
                <li><strong>شركاء:</strong> الشركاء التجاريين</li>
                <li><strong>موظفين:</strong> فريق العمل الداخلي</li>
                <li><strong>أخرى:</strong> جهات اتصال متنوعة</li>
            </ul>

            <h2>📦 وحدة إدارة المنتجات</h2>
            <p>
                إدارة شاملة لجميع المنتجات والخدمات التي تقدمها الشركة، مع تتبع المخزون والأسعار.
            </p>

            <h3>الميزات الرئيسية:</h3>
            <ul>
                <li><strong>كتالوج المنتجات:</strong> قاعدة بيانات شاملة للمنتجات</li>
                <li><strong>إدارة المخزون:</strong> تتبع الكميات المتاحة</li>
                <li><strong>إدارة الأسعار:</strong> أسعار البيع والشراء</li>
                <li><strong>تصنيف المنتجات:</strong> تنظيم المنتجات في فئات</li>
                <li><strong>تتبع الحركة:</strong> سجل دخول وخروج المنتجات</li>
            </ul>

            <h2>📄 وحدة إدارة الفواتير</h2>
            <p>
                نظام متكامل لإنشاء وإدارة الفواتير مع تتبع حالات الدفع والمتابعة.
            </p>

            <h3>أنواع الفواتير:</h3>
            <ul>
                <li><strong>فواتير البيع:</strong> فواتير للعملاء</li>
                <li><strong>فواتير الشراء:</strong> فواتير من الموردين</li>
                <li><strong>فواتير الخدمات:</strong> فواتير أعمال الصيانة</li>
            </ul>

            <h3>حالات الفواتير:</h3>
            <ul>
                <li><strong>مسودة:</strong> فاتورة قيد الإعداد</li>
                <li><strong>مرسلة:</strong> تم إرسالها للعميل</li>
                <li><strong>مدفوعة:</strong> تم استلام الدفعة</li>
                <li><strong>متأخرة:</strong> تجاوزت تاريخ الاستحقاق</li>
                <li><strong>ملغية:</strong> فاتورة ملغاة</li>
            </ul>

            <h2>💰 وحدة إدارة المدفوعات</h2>
            <p>
                تتبع جميع المدفوعات الواردة والصادرة مع ربطها بالفواتير المقابلة.
            </p>

            <h3>طرق الدفع المدعومة:</h3>
            <ul>
                <li><strong>نقداً:</strong> الدفع النقدي</li>
                <li><strong>تحويل بنكي:</strong> التحويلات البنكية</li>
                <li><strong>شيك:</strong> الدفع بالشيكات</li>
                <li><strong>بطاقة ائتمان:</strong> الدفع الإلكتروني</li>
            </ul>

            <h2>🔧 وحدة أوامر الشغل</h2>
            <p>
                إدارة متكاملة لجميع أوامر الصيانة والخدمات من الإنشاء حتى الإنجاز.
            </p>

            <h3>مراحل أمر الشغل:</h3>
            <div class="steps">
                <div class="step">
                    <h4>طلب جديد</h4>
                    <p>استلام طلب الخدمة من العميل</p>
                </div>

                <div class="step">
                    <h4>قيد التنفيذ</h4>
                    <p>بدء العمل على الطلب</p>
                </div>

                <div class="step">
                    <h4>في الانتظار</h4>
                    <p>انتظار قطع غيار أو موافقة</p>
                </div>

                <div class="step">
                    <h4>مكتمل</h4>
                    <p>انتهاء العمل وتسليم الخدمة</p>
                </div>
            </div>

            <h2>📊 وحدة التقارير</h2>
            <p>
                تقارير شاملة ومفصلة لجميع جوانب العمل مع رسوم بيانية تفاعلية.
            </p>

            <h3>أنواع التقارير:</h3>
            <ul>
                <li><strong>تقارير المبيعات:</strong> تحليل الإيرادات والمبيعات</li>
                <li><strong>تقارير العملاء:</strong> إحصائيات العملاء والتعاملات</li>
                <li><strong>تقارير المخزون:</strong> حالة المخزون والحركة</li>
                <li><strong>تقارير مالية:</strong> الأرباح والخسائر والتدفق النقدي</li>
                <li><strong>تقارير الأداء:</strong> مؤشرات الأداء الرئيسية</li>
            </ul>

            <h2>⚙️ وحدة الإعدادات</h2>
            <p>
                تخصيص النظام وإعداداته ليناسب احتياجات شركتك الخاصة.
            </p>

            <h3>أقسام الإعدادات:</h3>
            <ul>
                <li><strong>معلومات الشركة:</strong> البيانات الأساسية للشركة</li>
                <li><strong>إعدادات النظام:</strong> تخصيص سلوك النظام</li>
                <li><strong>إعدادات الفواتير:</strong> تنسيق وترقيم الفواتير</li>
                <li><strong>إعدادات التقارير:</strong> تخصيص التقارير</li>
                <li><strong>النسخ الاحتياطي:</strong> حفظ واستعادة البيانات</li>
            </ul>
        </div>

        <!-- Section 5: Usage Guide -->
        <div class="section" id="usage">
            <h1>📖 دليل الاستخدام</h1>

            <h2>👥 كيفية إضافة عميل جديد</h2>

            <div class="steps">
                <div class="step">
                    <h4>الانتقال لوحدة العملاء</h4>
                    <p>انقر على "العملاء" في القائمة الجانبية</p>
                </div>

                <div class="step">
                    <h4>النقر على "عميل جديد"</h4>
                    <p>انقر على الزر الأزرق "عميل جديد" في أعلى الصفحة</p>
                </div>

                <div class="step">
                    <h4>ملء البيانات المطلوبة</h4>
                    <p>أدخل اسم العميل، رقم الهاتف، ونوع العميل (مطلوب)</p>
                </div>

                <div class="step">
                    <h4>إضافة البيانات الاختيارية</h4>
                    <p>أضف البريد الإلكتروني، العنوان، والملاحظات حسب الحاجة</p>
                </div>

                <div class="step">
                    <h4>حفظ العميل</h4>
                    <p>انقر على "حفظ" لإضافة العميل إلى النظام</p>
                </div>
            </div>

            <div class="info">
                <span class="icon">💡</span>
                <strong>نصيحة:</strong> استخدم رقم هاتف صحيح لأنه سيستخدم في التواصل مع العميل.
            </div>

            <h2>📄 كيفية إنشاء فاتورة جديدة</h2>

            <div class="steps">
                <div class="step">
                    <h4>اختيار العميل</h4>
                    <p>ابدأ بالانتقال لوحدة الفواتير واختر العميل من القائمة</p>
                </div>

                <div class="step">
                    <h4>إضافة المنتجات/الخدمات</h4>
                    <p>أضف المنتجات أو الخدمات مع الكميات والأسعار</p>
                </div>

                <div class="step">
                    <h4>مراجعة الإجماليات</h4>
                    <p>تأكد من صحة المبالغ والضرائب المحسوبة</p>
                </div>

                <div class="step">
                    <h4>حفظ الفاتورة</h4>
                    <p>احفظ الفاتورة كمسودة أو أرسلها مباشرة للعميل</p>
                </div>
            </div>

            <h2>🔧 كيفية إنشاء أمر شغل</h2>

            <div class="steps">
                <div class="step">
                    <h4>تحديد العميل والخدمة</h4>
                    <p>اختر العميل ونوع الخدمة المطلوبة</p>
                </div>

                <div class="step">
                    <h4>وصف المشكلة</h4>
                    <p>أدخل وصفاً مفصلاً للمشكلة أو الخدمة المطلوبة</p>
                </div>

                <div class="step">
                    <h4>تحديد الأولوية والموعد</h4>
                    <p>حدد أولوية العمل والموعد المتوقع للإنجاز</p>
                </div>

                <div class="step">
                    <h4>تعيين الفني</h4>
                    <p>اختر الفني المسؤول عن تنفيذ العمل</p>
                </div>

                <div class="step">
                    <h4>حفظ أمر الشغل</h4>
                    <p>احفظ أمر الشغل وابدأ تتبع التقدم</p>
                </div>
            </div>

            <h2>📊 كيفية عرض التقارير</h2>

            <div class="steps">
                <div class="step">
                    <h4>الانتقال لوحدة التقارير</h4>
                    <p>انقر على "التقارير" في القائمة الجانبية</p>
                </div>

                <div class="step">
                    <h4>اختيار نوع التقرير</h4>
                    <p>اختر نوع التقرير المطلوب من القائمة المتاحة</p>
                </div>

                <div class="step">
                    <h4>تحديد الفترة الزمنية</h4>
                    <p>حدد الفترة الزمنية للتقرير (يومي، أسبوعي، شهري)</p>
                </div>

                <div class="step">
                    <h4>عرض النتائج</h4>
                    <p>اعرض التقرير مع الرسوم البيانية والإحصائيات</p>
                </div>

                <div class="step">
                    <h4>تصدير التقرير</h4>
                    <p>صدّر التقرير بصيغة PDF أو Excel حسب الحاجة</p>
                </div>
            </div>

            <h2>💡 نصائح للاستخدام الأمثل</h2>

            <h3>إدارة البيانات:</h3>
            <ul>
                <li><strong>النسخ الاحتياطي المنتظم:</strong> احفظ نسخة احتياطية من البيانات أسبوعياً</li>
                <li><strong>تحديث البيانات:</strong> حدّث بيانات العملاء والموردين بانتظام</li>
                <li><strong>مراجعة الفواتير:</strong> راجع الفواتير قبل الإرسال للعملاء</li>
                <li><strong>متابعة أوامر الشغل:</strong> تابع حالة أوامر الشغل يومياً</li>
            </ul>

            <h3>تحسين الأداء:</h3>
            <ul>
                <li><strong>استخدام البحث:</strong> استخدم خاصية البحث للوصول السريع للبيانات</li>
                <li><strong>الفلترة:</strong> استخدم الفلاتر لتنظيم عرض البيانات</li>
                <li><strong>الاختصارات:</strong> تعلم اختصارات لوحة المفاتيح للعمل السريع</li>
                <li><strong>التقارير الدورية:</strong> اعرض التقارير بانتظام لمتابعة الأداء</li>
            </ul>

            <div class="warning">
                <span class="icon">⚠️</span>
                <strong>تنبيه مهم:</strong> تأكد من حفظ عملك بانتظام، حيث أن البيانات محفوظة محلياً في المتصفح.
            </div>
        </div>

        <!-- Section 6: Technical Information -->
        <div class="section" id="technical">
            <h1>🔧 المعلومات التقنية</h1>

            <h2>🏗️ هيكل المشروع</h2>
            <p>
                نظام الماهر مبني بتقنيات الويب الحديثة ومنظم في هيكل واضح ومرتب:
            </p>

            <div class="code-block">
almaher-erp/
├── index.html                 # الملف الرئيسي
├── manifest.json             # إعدادات التطبيق
├── assets/                   # الموارد الثابتة
│   ├── css/                 # ملفات التنسيق
│   │   ├── variables.css    # متغيرات CSS
│   │   ├── base.css         # الأساسيات
│   │   ├── components.css   # المكونات
│   │   ├── layout.css       # التخطيط
│   │   ├── themes.css       # الثيمات
│   │   └── rtl.css          # دعم العربية
│   └── images/              # الصور والأيقونات
├── js/                      # ملفات JavaScript
│   ├── core/               # الملفات الأساسية
│   │   ├── Router.js       # نظام التنقل
│   │   ├── DataManager.js  # إدارة البيانات
│   │   ├── EventBus.js     # نظام الأحداث
│   │   └── ThemeManager.js # إدارة الثيمات
│   └── components/         # مكونات النظام
│       ├── DashboardManagement.js
│       ├── CustomerManagement.js
│       ├── SupplierManagement.js
│       └── ...
└── user-guide.html         # دليل المستخدم
            </div>

            <h2>💻 التقنيات المستخدمة</h2>

            <h3>Frontend Technologies:</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>التقنية</th>
                            <th>الإصدار</th>
                            <th>الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>HTML5</td>
                            <td>أحدث إصدار</td>
                            <td>هيكل الصفحات</td>
                        </tr>
                        <tr>
                            <td>CSS3</td>
                            <td>أحدث إصدار</td>
                            <td>التنسيق والتصميم</td>
                        </tr>
                        <tr>
                            <td>JavaScript ES6+</td>
                            <td>ES2020</td>
                            <td>المنطق والتفاعل</td>
                        </tr>
                        <tr>
                            <td>CSS Grid & Flexbox</td>
                            <td>-</td>
                            <td>التخطيط المرن</td>
                        </tr>
                        <tr>
                            <td>CSS Custom Properties</td>
                            <td>-</td>
                            <td>نظام الثيمات</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>المكتبات والأدوات:</h3>
            <ul>
                <li><strong>Vanilla JavaScript:</strong> بدون مكتبات خارجية للأداء الأمثل</li>
                <li><strong>CSS Modules:</strong> تنظيم ملفات CSS</li>
                <li><strong>ES6 Modules:</strong> تنظيم كود JavaScript</li>
                <li><strong>LocalStorage API:</strong> حفظ البيانات محلياً</li>
                <li><strong>History API:</strong> نظام التنقل</li>
            </ul>

            <h2>💾 قاعدة البيانات المحلية</h2>
            <p>
                النظام يستخدم LocalStorage لحفظ البيانات محلياً في المتصفح،
                مما يضمن الخصوصية والأمان دون الحاجة لخادم خارجي.
            </p>

            <h3>مفاتيح التخزين:</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>المفتاح</th>
                            <th>البيانات المحفوظة</th>
                            <th>التنسيق</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>almaher_customers</td>
                            <td>بيانات العملاء</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_suppliers</td>
                            <td>بيانات الموردين</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_contacts</td>
                            <td>جهات الاتصال</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_products</td>
                            <td>المنتجات</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_invoices</td>
                            <td>الفواتير</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_payments</td>
                            <td>المدفوعات</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_workorders</td>
                            <td>أوامر الشغل</td>
                            <td>JSON Array</td>
                        </tr>
                        <tr>
                            <td>almaher_settings</td>
                            <td>إعدادات النظام</td>
                            <td>JSON Object</td>
                        </tr>
                        <tr>
                            <td>almaher_theme</td>
                            <td>الثيم المختار</td>
                            <td>String</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>مميزات التخزين المحلي:</h3>
            <ul>
                <li><strong>الخصوصية:</strong> البيانات لا تغادر جهازك</li>
                <li><strong>السرعة:</strong> وصول فوري للبيانات</li>
                <li><strong>عدم الحاجة للإنترنت:</strong> يعمل بدون اتصال</li>
                <li><strong>الأمان:</strong> لا توجد مخاطر تسريب البيانات</li>
            </ul>

            <div class="warning">
                <span class="icon">⚠️</span>
                <strong>تنبيه:</strong> البيانات محفوظة في المتصفح فقط. تأكد من عمل نسخ احتياطية منتظمة.
            </div>

            <h2>🔄 نظام التنقل (Router)</h2>
            <p>
                النظام يستخدم Hash-based routing للتنقل السلس بين الصفحات:
            </p>

            <div class="code-block">
// أمثلة على المسارات
http://localhost:3000/#/              // الصفحة الرئيسية
http://localhost:3000/#/customers     // صفحة العملاء
http://localhost:3000/#/invoices      // صفحة الفواتير
http://localhost:3000/#/work-orders   // صفحة أوامر الشغل
            </div>

            <h3>مميزات نظام التنقل:</h3>
            <ul>
                <li><strong>SPA Experience:</strong> تحميل سريع بدون إعادة تحميل الصفحة</li>
                <li><strong>Browser History:</strong> دعم أزرار الرجوع والتقدم</li>
                <li><strong>Deep Linking:</strong> إمكانية الوصول المباشر لأي صفحة</li>
                <li><strong>SEO Friendly:</strong> مسارات واضحة ومفهومة</li>
            </ul>
        </div>

        <!-- Section 7: Troubleshooting -->
        <div class="section" id="troubleshooting">
            <h1>🛠️ استكشاف الأخطاء</h1>

            <h2>❌ المشاكل الشائعة وحلولها</h2>

            <h3>🚫 النظام لا يعمل أو لا يظهر</h3>
            <div class="steps">
                <div class="step">
                    <h4>تحقق من الخادم المحلي</h4>
                    <p>تأكد من أن الخادم المحلي يعمل في نافذة الأوامر</p>
                </div>

                <div class="step">
                    <h4>تحقق من العنوان</h4>
                    <p>تأكد من أن العنوان صحيح: http://localhost:3000</p>
                </div>

                <div class="step">
                    <h4>تحقق من المتصفح</h4>
                    <p>استخدم متصفح حديث (Chrome, Firefox, Safari, Edge)</p>
                </div>

                <div class="step">
                    <h4>مسح الكاش</h4>
                    <p>امسح كاش المتصفح وأعد تحميل الصفحة (Ctrl+F5)</p>
                </div>
            </div>

            <h3>💾 فقدان البيانات</h3>
            <div class="steps">
                <div class="step">
                    <h4>تحقق من LocalStorage</h4>
                    <p>افتح Developer Tools واذهب إلى Application > Local Storage</p>
                </div>

                <div class="step">
                    <h4>استعادة النسخة الاحتياطية</h4>
                    <p>استخدم النسخة الاحتياطية المحفوظة مسبقاً</p>
                </div>

                <div class="step">
                    <h4>إعادة إنشاء البيانات</h4>
                    <p>أعد إدخال البيانات المهمة يدوياً</p>
                </div>
            </div>

            <h3>🐌 بطء في الأداء</h3>
            <ul>
                <li><strong>أغلق التبويبات الأخرى:</strong> قلل استهلاك ذاكرة المتصفح</li>
                <li><strong>أعد تشغيل المتصفح:</strong> لتحرير الذاكرة</li>
                <li><strong>تحقق من الذاكرة:</strong> تأكد من توفر ذاكرة كافية</li>
                <li><strong>حدّث المتصفح:</strong> استخدم أحدث إصدار</li>
            </ul>

            <h3>🔧 مشاكل في الوظائف</h3>
            <ul>
                <li><strong>تحقق من Console:</strong> افتح Developer Tools وتحقق من الأخطاء</li>
                <li><strong>أعد تحميل الصفحة:</strong> اضغط F5 أو Ctrl+R</li>
                <li><strong>امسح البيانات المؤقتة:</strong> امسح LocalStorage وأعد البدء</li>
                <li><strong>جرب متصفح آخر:</strong> للتأكد من أن المشكلة ليست في المتصفح</li>
            </ul>

            <h2>🔍 كيفية فتح Developer Tools</h2>

            <h3>في Chrome/Edge:</h3>
            <ul>
                <li>اضغط F12 أو Ctrl+Shift+I</li>
                <li>أو انقر بالزر الأيمن واختر "Inspect"</li>
            </ul>

            <h3>في Firefox:</h3>
            <ul>
                <li>اضغط F12 أو Ctrl+Shift+I</li>
                <li>أو انقر بالزر الأيمن واختر "Inspect Element"</li>
            </ul>

            <h3>في Safari:</h3>
            <ul>
                <li>فعّل Developer Menu من Preferences</li>
                <li>اضغط Cmd+Option+I</li>
            </ul>

            <h2>📋 معلومات مفيدة للدعم</h2>
            <p>عند طلب الدعم، يرجى تقديم المعلومات التالية:</p>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>المعلومة</th>
                            <th>كيفية الحصول عليها</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>نوع المتصفح والإصدار</td>
                            <td>Settings > About أو Help > About</td>
                        </tr>
                        <tr>
                            <td>نظام التشغيل</td>
                            <td>Windows: Settings > System<br>Mac: Apple Menu > About</td>
                        </tr>
                        <tr>
                            <td>رسالة الخطأ</td>
                            <td>انسخ النص الكامل للخطأ</td>
                        </tr>
                        <tr>
                            <td>خطوات إعادة المشكلة</td>
                            <td>اكتب الخطوات بالتفصيل</td>
                        </tr>
                        <tr>
                            <td>لقطة شاشة</td>
                            <td>Print Screen أو أدوات لقطة الشاشة</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Section 8: Support -->
        <div class="section" id="support">
            <h1>📞 الدعم والمساعدة</h1>

            <h2>🏢 معلومات الشركة</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">🏢</span>
                    <h4>الماهر للصيانة والخدمات</h4>
                    <p>شركة متخصصة في خدمات الصيانة والإصلاح</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📞</span>
                    <h4>أرقام الاتصال</h4>
                    <p>الهاتف: 0112345678<br>الجوال: 0501234567</p>
                </div>
                <div class="feature-card">
                    <span class="icon">📧</span>
                    <h4>البريد الإلكتروني</h4>
                    <p><EMAIL><br><EMAIL></p>
                </div>
                <div class="feature-card">
                    <span class="icon">📍</span>
                    <h4>العنوان</h4>
                    <p>الرياض، المملكة العربية السعودية<br>الرمز البريدي: 12345</p>
                </div>
            </div>

            <h2>🆘 طلب الدعم التقني</h2>
            <p>
                إذا واجهت أي مشكلة في استخدام النظام، يمكنك التواصل معنا عبر:
            </p>

            <ul>
                <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                <li><strong>الهاتف:</strong> 0112345678 (أوقات العمل: 8 ص - 5 م)</li>
                <li><strong>الواتساب:</strong> 0501234567</li>
            </ul>

            <h2>📚 موارد إضافية</h2>
            <ul>
                <li><strong>دليل المستخدم المفصل:</strong> هذا الدليل</li>
                <li><strong>فيديوهات تعليمية:</strong> قريباً على موقعنا</li>
                <li><strong>الأسئلة الشائعة:</strong> قسم FAQ على الموقع</li>
                <li><strong>تحديثات النظام:</strong> إشعارات عبر البريد الإلكتروني</li>
            </ul>

            <h2>🔄 التحديثات والتطوير</h2>
            <p>
                نحن نعمل باستمرار على تطوير النظام وإضافة ميزات جديدة.
                ستحصل على إشعارات بالتحديثات الجديدة عبر البريد الإلكتروني.
            </p>

            <h3>التحديثات المخططة:</h3>
            <ul>
                <li><strong>نظام التقارير المتقدم:</strong> رسوم بيانية تفاعلية</li>
                <li><strong>تطبيق الجوال:</strong> تطبيق مخصص للهواتف الذكية</li>
                <li><strong>التكامل مع الأنظمة الخارجية:</strong> ربط مع أنظمة المحاسبة</li>
                <li><strong>نظام الصلاحيات:</strong> إدارة المستخدمين والصلاحيات</li>
            </ul>

            <div class="info">
                <span class="icon">💡</span>
                <strong>اقتراحاتك مهمة:</strong> نرحب بجميع اقتراحاتك لتطوير النظام وجعله أفضل.
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 الماهر للصيانة والخدمات - جميع الحقوق محفوظة</p>
            <p>دليل المستخدم - الإصدار 1.0 - يناير 2025</p>
            <p>تم إنشاء هذا الدليل بواسطة فريق التطوير في الماهر للصيانة والخدمات</p>
        </div>
    </div>
</body>
</html>
