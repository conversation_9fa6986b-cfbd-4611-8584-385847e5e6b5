<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل النظام - الماهر</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f8f9fa;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            color: #007bff;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 تشخيص مشاكل نظام الماهر</h1>
        <p>هذه الصفحة لتشخيص المشاكل الفعلية في النظام</p>

        <!-- Console Output -->
        <div class="test-section">
            <h2 class="test-title">📋 رسائل وحدة التحكم</h2>
            <button class="test-button" onclick="clearConsole()">مسح الرسائل</button>
            <button class="test-button" onclick="captureConsole()">التقاط الرسائل</button>
            <div id="console-output" class="console-output"></div>
        </div>

        <!-- File Loading Test -->
        <div class="test-section">
            <h2 class="test-title">📁 اختبار تحميل الملفات</h2>
            <button class="test-button" onclick="testFileLoading()">اختبار تحميل الملفات</button>
            <div id="file-loading-result" class="test-result"></div>
        </div>

        <!-- Theme Selector Test -->
        <div class="test-section">
            <h2 class="test-title">🎨 اختبار اختيار الثيمات</h2>
            <button class="test-button" onclick="testThemeSelector()">اختبار ThemeSelector</button>
            <button class="test-button" onclick="testThemeButton()">اختبار زر الثيم</button>
            <button class="test-button" onclick="manualThemeTest()">اختبار يدوي للثيم</button>
            <div id="theme-test-result" class="test-result"></div>
        </div>

        <!-- Chart.js Test -->
        <div class="test-section">
            <h2 class="test-title">📊 اختبار Chart.js</h2>
            <button class="test-button" onclick="testChartJS()">اختبار تحميل Chart.js</button>
            <button class="test-button" onclick="createSimpleChart()">إنشاء رسم بسيط</button>
            <div id="chart-test-result" class="test-result"></div>
            <canvas id="test-chart" width="400" height="200" style="margin-top: 10px; display: none;"></canvas>
        </div>

        <!-- Export Libraries Test -->
        <div class="test-section">
            <h2 class="test-title">📤 اختبار مكتبات التصدير</h2>
            <button class="test-button" onclick="testExportLibraries()">اختبار XLSX و jsPDF</button>
            <button class="test-button" onclick="testSimpleExport()">اختبار تصدير بسيط</button>
            <div id="export-test-result" class="test-result"></div>
        </div>

        <!-- DOM Elements Test -->
        <div class="test-section">
            <h2 class="test-title">🏗️ اختبار عناصر DOM</h2>
            <button class="test-button" onclick="testDOMElements()">فحص العناصر المطلوبة</button>
            <div id="dom-test-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // Console capture
        let consoleMessages = [];
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        function captureConsole() {
            console.log = function(...args) {
                consoleMessages.push(`[LOG] ${args.join(' ')}`);
                originalConsole.log.apply(console, args);
                updateConsoleOutput();
            };
            
            console.error = function(...args) {
                consoleMessages.push(`[ERROR] ${args.join(' ')}`);
                originalConsole.error.apply(console, args);
                updateConsoleOutput();
            };
            
            console.warn = function(...args) {
                consoleMessages.push(`[WARN] ${args.join(' ')}`);
                originalConsole.warn.apply(console, args);
                updateConsoleOutput();
            };
            
            console.info = function(...args) {
                consoleMessages.push(`[INFO] ${args.join(' ')}`);
                originalConsole.info.apply(console, args);
                updateConsoleOutput();
            };
            
            updateResult('console-output', 'تم تفعيل التقاط رسائل وحدة التحكم', 'success');
        }

        function clearConsole() {
            consoleMessages = [];
            updateConsoleOutput();
        }

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            output.innerHTML = consoleMessages.slice(-50).join('\n');
            output.scrollTop = output.scrollHeight;
        }

        function updateResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        // Test file loading
        function testFileLoading() {
            const result = document.getElementById('file-loading-result');
            let report = 'تقرير تحميل الملفات:\n\n';

            // Test CSS files
            const cssFiles = [
                'assets/css/variables.css',
                'assets/css/themes.css',
                'assets/css/theme-selector.css'
            ];

            // Test JS files
            const jsFiles = [
                'js/core/ThemeSelector.js',
                'js/core/ChartManager.js',
                'js/core/ExportManager.js'
            ];

            // Check CSS files
            cssFiles.forEach(file => {
                const link = document.querySelector(`link[href="${file}"]`);
                report += `${link ? '✅' : '❌'} ${file}\n`;
            });

            // Check JS files
            jsFiles.forEach(file => {
                const script = document.querySelector(`script[src="${file}"]`);
                report += `${script ? '✅' : '❌'} ${file}\n`;
            });

            // Check global objects
            report += '\nالكائنات العامة:\n';
            report += `${window.ThemeSelector ? '✅' : '❌'} window.ThemeSelector\n`;
            report += `${window.Chart ? '✅' : '❌'} window.Chart\n`;
            report += `${window.XLSX ? '✅' : '❌'} window.XLSX\n`;
            report += `${window.jsPDF ? '✅' : '❌'} window.jsPDF\n`;

            result.textContent = report;
            result.className = 'test-result warning';
        }

        // Test theme selector
        function testThemeSelector() {
            const result = document.getElementById('theme-test-result');
            let report = 'اختبار ThemeSelector:\n\n';

            try {
                // Check if ThemeSelector class exists
                if (typeof ThemeSelector === 'undefined') {
                    report += '❌ ThemeSelector class غير موجود\n';
                    result.textContent = report;
                    result.className = 'test-result error';
                    return;
                }

                report += '✅ ThemeSelector class موجود\n';

                // Try to create instance
                const themeSelector = new ThemeSelector();
                report += '✅ تم إنشاء instance بنجاح\n';

                // Check methods
                const methods = ['init', 'selectTheme', 'applyTheme', 'setupElements'];
                methods.forEach(method => {
                    report += `${typeof themeSelector[method] === 'function' ? '✅' : '❌'} Method: ${method}\n`;
                });

                // Try to initialize
                themeSelector.init();
                report += '✅ تم استدعاء init()\n';

                result.textContent = report;
                result.className = 'test-result success';

            } catch (error) {
                report += `❌ خطأ: ${error.message}\n`;
                result.textContent = report;
                result.className = 'test-result error';
            }
        }

        // Test theme button
        function testThemeButton() {
            const result = document.getElementById('theme-test-result');
            let report = 'اختبار زر الثيم:\n\n';

            // Check if button exists
            const themeBtn = document.getElementById('theme-selector-btn');
            if (!themeBtn) {
                report += '❌ زر الثيم غير موجود في الصفحة\n';
                report += 'محاولة البحث عن الزر...\n';
                
                const allButtons = document.querySelectorAll('button');
                report += `عدد الأزرار الموجودة: ${allButtons.length}\n`;
                
                allButtons.forEach((btn, index) => {
                    report += `زر ${index + 1}: ${btn.id || 'بدون ID'} - ${btn.className}\n`;
                });
                
                result.textContent = report;
                result.className = 'test-result error';
                return;
            }

            report += '✅ زر الثيم موجود\n';
            report += `ID: ${themeBtn.id}\n`;
            report += `Classes: ${themeBtn.className}\n`;

            // Check dropdown
            const dropdown = document.getElementById('theme-dropdown');
            report += `${dropdown ? '✅' : '❌'} القائمة المنسدلة موجودة\n`;

            // Test click event
            try {
                themeBtn.click();
                report += '✅ تم النقر على الزر بنجاح\n';
            } catch (error) {
                report += `❌ خطأ في النقر: ${error.message}\n`;
            }

            result.textContent = report;
            result.className = 'test-result warning';
        }

        // Manual theme test
        function manualThemeTest() {
            const result = document.getElementById('theme-test-result');
            let report = 'اختبار يدوي للثيم:\n\n';

            try {
                // Apply dark theme manually
                document.documentElement.setAttribute('data-theme', 'dark');
                document.documentElement.classList.add('theme-dark');
                report += '✅ تم تطبيق الثيم الداكن يدوياً\n';

                setTimeout(() => {
                    // Apply light theme
                    document.documentElement.setAttribute('data-theme', 'light');
                    document.documentElement.classList.remove('theme-dark');
                    document.documentElement.classList.add('theme-light');
                    report += '✅ تم تطبيق الثيم الفاتح يدوياً\n';
                    
                    result.textContent = report;
                    result.className = 'test-result success';
                }, 2000);

                result.textContent = report + '⏳ جاري التبديل للثيم الفاتح...\n';
                result.className = 'test-result warning';

            } catch (error) {
                report += `❌ خطأ: ${error.message}\n`;
                result.textContent = report;
                result.className = 'test-result error';
            }
        }

        // Test Chart.js
        function testChartJS() {
            const result = document.getElementById('chart-test-result');
            let report = 'اختبار Chart.js:\n\n';

            if (typeof Chart === 'undefined') {
                report += '❌ Chart.js غير محمل\n';
                report += 'محاولة تحميل Chart.js...\n';
                
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
                script.onload = () => {
                    report += '✅ تم تحميل Chart.js بنجاح\n';
                    result.textContent = report;
                    result.className = 'test-result success';
                };
                script.onerror = () => {
                    report += '❌ فشل في تحميل Chart.js\n';
                    result.textContent = report;
                    result.className = 'test-result error';
                };
                document.head.appendChild(script);
                
                result.textContent = report;
                result.className = 'test-result warning';
                return;
            }

            report += '✅ Chart.js محمل ومتوفر\n';
            report += `إصدار Chart.js: ${Chart.version || 'غير معروف'}\n`;

            result.textContent = report;
            result.className = 'test-result success';
        }

        // Create simple chart
        function createSimpleChart() {
            const canvas = document.getElementById('test-chart');
            const result = document.getElementById('chart-test-result');

            if (typeof Chart === 'undefined') {
                result.textContent = '❌ Chart.js غير متوفر';
                result.className = 'test-result error';
                return;
            }

            try {
                canvas.style.display = 'block';
                
                new Chart(canvas, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس'],
                        datasets: [{
                            label: 'اختبار',
                            data: [10, 20, 15],
                            backgroundColor: ['#007bff', '#28a745', '#ffc107']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                rtl: true
                            }
                        }
                    }
                });

                result.textContent = '✅ تم إنشاء الرسم البياني بنجاح';
                result.className = 'test-result success';

            } catch (error) {
                result.textContent = `❌ خطأ في إنشاء الرسم البياني: ${error.message}`;
                result.className = 'test-result error';
            }
        }

        // Test export libraries
        function testExportLibraries() {
            const result = document.getElementById('export-test-result');
            let report = 'اختبار مكتبات التصدير:\n\n';

            // Test XLSX
            if (typeof XLSX === 'undefined') {
                report += '❌ مكتبة XLSX غير محملة\n';
            } else {
                report += '✅ مكتبة XLSX محملة\n';
                report += `إصدار XLSX: ${XLSX.version || 'غير معروف'}\n`;
            }

            // Test jsPDF
            if (typeof jsPDF === 'undefined') {
                report += '❌ مكتبة jsPDF غير محملة\n';
            } else {
                report += '✅ مكتبة jsPDF محملة\n';
            }

            result.textContent = report;
            result.className = report.includes('❌') ? 'test-result error' : 'test-result success';
        }

        // Test simple export
        function testSimpleExport() {
            const result = document.getElementById('export-test-result');

            if (typeof XLSX === 'undefined') {
                result.textContent = '❌ مكتبة XLSX غير متوفرة للاختبار';
                result.className = 'test-result error';
                return;
            }

            try {
                const data = [
                    { 'الاسم': 'أحمد', 'العمر': 30 },
                    { 'الاسم': 'فاطمة', 'العمر': 25 }
                ];

                const ws = XLSX.utils.json_to_sheet(data);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');
                XLSX.writeFile(wb, 'اختبار.xlsx');

                result.textContent = '✅ تم تصدير ملف Excel تجريبي بنجاح';
                result.className = 'test-result success';

            } catch (error) {
                result.textContent = `❌ خطأ في التصدير: ${error.message}`;
                result.className = 'test-result error';
            }
        }

        // Test DOM elements
        function testDOMElements() {
            const result = document.getElementById('dom-test-result');
            let report = 'فحص عناصر DOM:\n\n';

            const requiredElements = [
                'theme-selector-btn',
                'theme-dropdown',
                'sidebar-toggle',
                'notification-btn'
            ];

            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                report += `${element ? '✅' : '❌'} #${id}\n`;
                if (element) {
                    report += `   Classes: ${element.className}\n`;
                    report += `   Visible: ${element.offsetParent !== null}\n`;
                }
            });

            // Check theme selector structure
            const themeSelector = document.querySelector('.theme-selector');
            report += `\n${themeSelector ? '✅' : '❌'} .theme-selector container\n`;

            if (themeSelector) {
                const options = themeSelector.querySelectorAll('.theme-option');
                report += `   عدد خيارات الثيم: ${options.length}\n`;
            }

            result.textContent = report;
            result.className = 'test-result warning';
        }

        // Auto-start console capture
        window.addEventListener('load', () => {
            captureConsole();
            console.log('🔍 بدء تشخيص النظام...');
        });
    </script>
</body>
</html>
