/**
 * Export Manager
 * إدارة تصدير البيانات والتقارير
 */
class ExportManager {
    constructor() {
        this.supportedFormats = ['excel', 'pdf', 'csv', 'json'];
        this.libraries = {
            excel: null,
            pdf: null
        };
    }

    /**
     * Initialize export manager
     */
    init() {
        console.log('📤 Initializing Export Manager...');
        this.loadLibraries();
        console.log('✅ Export Manager initialized');
    }

    /**
     * Load required libraries
     */
    async loadLibraries() {
        try {
            // Load SheetJS for Excel export
            if (!window.XLSX) {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js');
            }

            // Load jsPDF for PDF export
            if (!window.jsPDF) {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
            }

            // Wait a bit for libraries to be available
            await new Promise(resolve => setTimeout(resolve, 100));

            this.libraries.excel = window.XLSX;
            this.libraries.pdf = window.jsPDF?.jsPDF;

            console.log('✅ Export libraries loaded:', {
                excel: !!this.libraries.excel,
                pdf: !!this.libraries.pdf
            });
        } catch (error) {
            console.error('❌ Failed to load export libraries:', error);
        }
    }

    /**
     * Load external script
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // Check if script already exists
            const existingScript = document.querySelector(`script[src="${src}"]`);
            if (existingScript) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                console.log(`✅ Loaded: ${src}`);
                resolve();
            };
            script.onerror = () => {
                console.error(`❌ Failed to load: ${src}`);
                reject(new Error(`Failed to load ${src}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Export data to Excel
     */
    exportToExcel(data, filename = 'تقرير', sheetName = 'البيانات') {
        if (!this.libraries.excel) {
            window.uiManager?.showToast('مكتبة Excel غير متوفرة', 'error');
            return;
        }

        try {
            // Create workbook
            const wb = this.libraries.excel.utils.book_new();
            
            // Convert data to worksheet
            const ws = this.libraries.excel.utils.json_to_sheet(data);
            
            // Set column widths
            const colWidths = this.calculateColumnWidths(data);
            ws['!cols'] = colWidths;
            
            // Add worksheet to workbook
            this.libraries.excel.utils.book_append_sheet(wb, ws, sheetName);
            
            // Write file
            this.libraries.excel.writeFile(wb, `${filename}.xlsx`);
            
            window.uiManager?.showToast('تم تصدير الملف بنجاح', 'success');
        } catch (error) {
            console.error('Excel export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الملف', 'error');
        }
    }

    /**
     * Export data to CSV
     */
    exportToCSV(data, filename = 'تقرير') {
        try {
            if (!Array.isArray(data) || data.length === 0) {
                window.uiManager?.showToast('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            // Get headers
            const headers = Object.keys(data[0]);
            
            // Create CSV content
            let csvContent = '\uFEFF'; // BOM for UTF-8
            csvContent += headers.join(',') + '\n';
            
            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header] || '';
                    // Escape commas and quotes
                    return `"${String(value).replace(/"/g, '""')}"`;
                });
                csvContent += values.join(',') + '\n';
            });
            
            // Download file
            this.downloadFile(csvContent, `${filename}.csv`, 'text/csv;charset=utf-8;');
            
            window.uiManager?.showToast('تم تصدير الملف بنجاح', 'success');
        } catch (error) {
            console.error('CSV export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الملف', 'error');
        }
    }

    /**
     * Export data to PDF
     */
    exportToPDF(data, filename = 'تقرير', title = 'تقرير البيانات') {
        if (!this.libraries.pdf) {
            window.uiManager?.showToast('مكتبة PDF غير متوفرة', 'error');
            return;
        }

        try {
            // Create PDF document
            const doc = new this.libraries.pdf({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4'
            });

            // Add Arabic font support (if available)
            this.setupPDFFont(doc);

            // Add title
            doc.setFontSize(16);
            doc.text(title, 20, 20);

            // Add company info
            doc.setFontSize(10);
            doc.text('الماهر للصيانة والخدمات', 20, 30);
            doc.text('تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA'), 20, 35);

            // Add table
            this.addTableToPDF(doc, data, 20, 45);

            // Save PDF
            doc.save(`${filename}.pdf`);
            
            window.uiManager?.showToast('تم تصدير الملف بنجاح', 'success');
        } catch (error) {
            console.error('PDF export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الملف', 'error');
        }
    }

    /**
     * Export data to JSON
     */
    exportToJSON(data, filename = 'تقرير') {
        try {
            const jsonContent = JSON.stringify(data, null, 2);
            this.downloadFile(jsonContent, `${filename}.json`, 'application/json');
            
            window.uiManager?.showToast('تم تصدير الملف بنجاح', 'success');
        } catch (error) {
            console.error('JSON export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الملف', 'error');
        }
    }

    /**
     * Export chart as image
     */
    exportChartAsImage(chartId, filename = 'رسم-بياني', format = 'png') {
        try {
            const chart = window.chartManager?.getChart(chartId);
            if (!chart) {
                window.uiManager?.showToast('الرسم البياني غير موجود', 'error');
                return;
            }

            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = `${filename}.${format}`;
            link.href = url;
            link.click();
            
            window.uiManager?.showToast('تم تصدير الرسم البياني بنجاح', 'success');
        } catch (error) {
            console.error('Chart export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الرسم البياني', 'error');
        }
    }

    /**
     * Setup PDF font for Arabic support
     */
    setupPDFFont(doc) {
        try {
            // Try to set a font that supports Arabic
            doc.setFont('helvetica');
        } catch (error) {
            console.warn('Arabic font not available, using default font');
        }
    }

    /**
     * Add table to PDF
     */
    addTableToPDF(doc, data, x, y) {
        if (!Array.isArray(data) || data.length === 0) return;

        const headers = Object.keys(data[0]);
        const cellWidth = 40;
        const cellHeight = 8;
        let currentY = y;

        // Draw headers
        doc.setFillColor(240, 240, 240);
        doc.rect(x, currentY, cellWidth * headers.length, cellHeight, 'F');
        
        doc.setFontSize(10);
        headers.forEach((header, index) => {
            doc.text(String(header), x + (index * cellWidth) + 2, currentY + 5);
        });

        currentY += cellHeight;

        // Draw data rows
        data.forEach((row, rowIndex) => {
            if (rowIndex % 2 === 0) {
                doc.setFillColor(250, 250, 250);
                doc.rect(x, currentY, cellWidth * headers.length, cellHeight, 'F');
            }

            headers.forEach((header, colIndex) => {
                const value = String(row[header] || '');
                doc.text(value.substring(0, 15), x + (colIndex * cellWidth) + 2, currentY + 5);
            });

            currentY += cellHeight;

            // Add new page if needed
            if (currentY > 180) {
                doc.addPage();
                currentY = 20;
            }
        });
    }

    /**
     * Calculate column widths for Excel
     */
    calculateColumnWidths(data) {
        if (!Array.isArray(data) || data.length === 0) return [];

        const headers = Object.keys(data[0]);
        return headers.map(header => {
            let maxLength = header.length;
            data.forEach(row => {
                const value = String(row[header] || '');
                maxLength = Math.max(maxLength, value.length);
            });
            return { wch: Math.min(maxLength + 2, 50) };
        });
    }

    /**
     * Download file
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    /**
     * Export invoice to PDF
     */
    exportInvoiceToPDF(invoice, filename = 'فاتورة') {
        if (!this.libraries.pdf) {
            window.uiManager?.showToast('مكتبة PDF غير متوفرة', 'error');
            return;
        }

        try {
            const doc = new this.libraries.pdf();
            
            // Company header
            doc.setFontSize(18);
            doc.text('الماهر للصيانة والخدمات', 20, 20);
            doc.setFontSize(12);
            doc.text('فرع الشرائع، 24263، حي الخضراء، مكة المكرمة', 20, 30);
            doc.text('هاتف: 0509888210', 20, 35);
            doc.text('الرقم الضريبي: 311055560500003', 20, 40);

            // Invoice details
            doc.setFontSize(16);
            doc.text(`فاتورة رقم: ${invoice.number}`, 20, 55);
            doc.setFontSize(12);
            doc.text(`التاريخ: ${invoice.date}`, 20, 65);
            doc.text(`العميل: ${invoice.customerName}`, 20, 70);

            // Items table
            let y = 85;
            doc.text('الخدمة', 20, y);
            doc.text('الكمية', 80, y);
            doc.text('السعر', 120, y);
            doc.text('المجموع', 160, y);
            
            y += 10;
            invoice.items.forEach(item => {
                doc.text(item.name, 20, y);
                doc.text(String(item.quantity), 80, y);
                doc.text(`${item.price} ر.س`, 120, y);
                doc.text(`${item.total} ر.س`, 160, y);
                y += 8;
            });

            // Total
            y += 10;
            doc.text(`المجموع: ${invoice.total} ر.س`, 160, y);

            doc.save(`${filename}.pdf`);
            window.uiManager?.showToast('تم تصدير الفاتورة بنجاح', 'success');
        } catch (error) {
            console.error('Invoice PDF export error:', error);
            window.uiManager?.showToast('خطأ في تصدير الفاتورة', 'error');
        }
    }

    /**
     * Get supported formats
     */
    getSupportedFormats() {
        return this.supportedFormats;
    }

    /**
     * Check if format is supported
     */
    isFormatSupported(format) {
        return this.supportedFormats.includes(format.toLowerCase());
    }
}

// Export for use in other modules
window.ExportManager = ExportManager;
