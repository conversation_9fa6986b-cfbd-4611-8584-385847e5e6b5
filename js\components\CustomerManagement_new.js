/**
 * Customer Management Component
 * إدارة العملاء
 */
export default class CustomerManagement {
    constructor() {
        console.log('👤 CustomerManagement constructor called');
        this.container = null;
        this.customers = [];
        this.filteredCustomers = [];
        this.currentFilter = 'all';
        this.currentSort = 'name';
        this.searchTerm = '';
        
        // أنواع العملاء
        this.customerTypes = [
            'فرد',
            'شركة',
            'مؤسسة',
            'عميل مميز'
        ];
        
        // حالات العملاء
        this.statuses = {
            'active': { name: 'نشط', color: '#28a745', icon: '✅' },
            'inactive': { name: 'غير نشط', color: '#6c757d', icon: '⏸️' },
            'vip': { name: 'مميز', color: '#ffc107', icon: '⭐' }
        };
        
        this.loadCustomers();
    }

    /**
     * Load customers from storage
     */
    loadCustomers() {
        const saved = localStorage.getItem('almaher_customers');
        if (saved) {
            this.customers = JSON.parse(saved);
        } else {
            this.createSampleData();
        }
        this.filteredCustomers = [...this.customers];
    }

    /**
     * Save customers to storage
     */
    saveCustomers() {
        localStorage.setItem('almaher_customers', JSON.stringify(this.customers));
    }

    /**
     * Create sample customers
     */
    createSampleData() {
        const sampleCustomers = [
            {
                id: 'CUST-001',
                name: 'أحمد محمد العلي',
                phone: '0501234567',
                email: '<EMAIL>',
                address: 'حي النزهة، شارع الملك فهد، الرياض',
                city: 'الرياض',
                customerType: 'فرد',
                status: 'active',
                notes: 'عميل منتظم، يطلب خدمات صيانة دورية',
                createdDate: '2024-01-01',
                lastOrderDate: '2024-01-10',
                totalOrders: 5,
                totalSpent: 2500
            },
            {
                id: 'CUST-002',
                name: 'شركة التقنية المتقدمة',
                contactPerson: 'سارة أحمد الغامدي',
                phone: '0112345678',
                email: '<EMAIL>',
                website: 'www.tech-advanced.com',
                address: 'طريق الملك عبدالعزيز، برج الأعمال، الدور 15',
                city: 'الرياض',
                customerType: 'شركة',
                status: 'vip',
                taxNumber: '300123456789003',
                notes: 'شركة تقنية كبيرة، عقد صيانة سنوي',
                createdDate: '2023-12-01',
                lastOrderDate: '2024-01-12',
                totalOrders: 12,
                totalSpent: 15000
            },
            {
                id: 'CUST-003',
                name: 'فاطمة سعد الزهراني',
                phone: '0509876543',
                email: '<EMAIL>',
                address: 'حي الملك فهد، شارع التحلية، جدة',
                city: 'جدة',
                customerType: 'فرد',
                status: 'active',
                notes: 'عميلة جديدة، مهتمة بخدمات التكييف',
                createdDate: '2024-01-05',
                lastOrderDate: '2024-01-08',
                totalOrders: 2,
                totalSpent: 800
            },
            {
                id: 'CUST-004',
                name: 'مؤسسة الخليج للمقاولات',
                contactPerson: 'محمد علي القحطاني',
                phone: '0138901234',
                email: '<EMAIL>',
                website: 'www.gulf-contracting.com',
                address: 'المنطقة الصناعية الثانية، الدمام',
                city: 'الدمام',
                customerType: 'مؤسسة',
                status: 'active',
                taxNumber: '300987654321003',
                notes: 'مؤسسة مقاولات، طلبات كبيرة للمشاريع',
                createdDate: '2023-11-15',
                lastOrderDate: '2024-01-06',
                totalOrders: 8,
                totalSpent: 25000
            }
        ];
        
        this.customers = sampleCustomers;
        this.saveCustomers();
    }

    /**
     * Render the customer management interface
     */
    render(container) {
        console.log('👤 CustomerManagement render called', container);
        this.container = container;
        this.renderContent();
        this.attachEventListeners();
        this.applyFilters();
        console.log('✅ CustomerManagement render completed');
    }

    /**
     * Render the main content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="customer-management">
                <!-- Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <span class="page-icon">👤</span>
                            إدارة العملاء
                        </h1>
                        <p class="page-description">إدارة وتتبع جميع العملاء والزبائن</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-customer-btn">
                            <span class="btn-icon">➕</span>
                            عميل جديد
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card active">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-active">0</div>
                            <div class="stat-label">عملاء نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card vip">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-vip">0</div>
                            <div class="stat-label">عملاء مميزين</div>
                        </div>
                    </div>
                    <div class="stat-card companies">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-companies">0</div>
                            <div class="stat-label">شركات</div>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label">إجمالي العملاء</div>
                        </div>
                    </div>
                </div>

                <!-- Customers List -->
                <div class="customers-section">
                    <div class="section-header">
                        <h2>قائمة العملاء</h2>
                    </div>
                    
                    <div id="customers-container" class="customers-container">
                        <div class="customers-grid">
                            ${this.customers.map(customer => this.renderCustomerCard(customer)).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render single customer card
     */
    renderCustomerCard(customer) {
        const status = this.statuses[customer.status];
        
        return `
            <div class="customer-card" data-id="${customer.id}">
                <div class="card-header">
                    <div class="card-id">${customer.id}</div>
                    <div class="card-badges">
                        <span class="status-badge ${customer.status}" title="${status.name}">
                            ${status.icon} ${status.name}
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <h3 class="customer-name">${customer.name}</h3>
                    <div class="customer-type">${customer.customerType}</div>
                    ${customer.contactPerson ? `<div class="contact-person">👤 ${customer.contactPerson}</div>` : ''}
                    
                    <div class="card-details">
                        <div class="detail-item">
                            <span class="detail-icon">📞</span>
                            <span class="detail-text">${customer.phone}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📧</span>
                            <span class="detail-text">${customer.email}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📍</span>
                            <span class="detail-text">${customer.city}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">💰</span>
                            <span class="detail-text">أنفق: ${customer.totalSpent.toLocaleString()} ر.س</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📋</span>
                            <span class="detail-text">الطلبات: ${customer.totalOrders}</span>
                        </div>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="customerManagement.viewCustomer('${customer.id}')">
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="customerManagement.editCustomer('${customer.id}')">
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="customerManagement.createOrder('${customer.id}')">
                        أمر شغل
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Update statistics
     */
    updateStatistics() {
        const stats = {
            active: this.customers.filter(c => c.status === 'active').length,
            vip: this.customers.filter(c => c.status === 'vip').length,
            companies: this.customers.filter(c => c.customerType === 'شركة' || c.customerType === 'مؤسسة').length,
            total: this.customers.length
        };

        document.getElementById('stat-active').textContent = stats.active;
        document.getElementById('stat-vip').textContent = stats.vip;
        document.getElementById('stat-companies').textContent = stats.companies;
        document.getElementById('stat-total').textContent = stats.total;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Add new customer button
        const addBtn = this.container.querySelector('#add-customer-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddCustomerModal());
        }
    }

    /**
     * Apply filters and search
     */
    applyFilters() {
        this.filteredCustomers = [...this.customers];
        this.updateStatistics();
    }

    /**
     * Show add customer modal
     */
    showAddCustomerModal() {
        window.uiManager?.showToast('سيتم إضافة نموذج إنشاء عميل جديد قريباً', 'info');
    }

    /**
     * View customer details
     */
    viewCustomer(customerId) {
        window.uiManager?.showToast(`عرض تفاصيل العميل: ${customerId}`, 'info');
    }

    /**
     * Edit customer
     */
    editCustomer(customerId) {
        window.uiManager?.showToast(`تعديل العميل: ${customerId}`, 'info');
    }

    /**
     * Create order for customer
     */
    createOrder(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (customer) {
            window.uiManager?.showToast(`إنشاء أمر شغل للعميل: ${customer.name}`, 'info');
        }
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.CustomerManagement = CustomerManagement;
console.log('👤 CustomerManagement class loaded and made globally available');
