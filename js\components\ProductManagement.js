/**
 * Product Management Component
 * إدارة المنتجات والخدمات
 */
class ProductManagement {
    constructor() {
        this.container = null;
        this.products = [];
        this.filteredProducts = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.selectedCategory = 'all';
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.loadProducts();
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Load products data
     */
    loadProducts() {
        // Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.products = [
            {
                id: 1,
                name: 'صيانة مكيف سبليت',
                category: 'maintenance',
                price: 150,
                cost: 100,
                stock: 0,
                type: 'service',
                description: 'خدمة صيانة شاملة للمكيفات السبليت',
                status: 'active'
            },
            {
                id: 2,
                name: 'فلتر مكيف',
                category: 'parts',
                price: 25,
                cost: 15,
                stock: 50,
                type: 'product',
                description: 'فلتر هواء للمكيفات',
                status: 'active'
            },
            {
                id: 3,
                name: 'تنظيف مكيف شباك',
                category: 'maintenance',
                price: 80,
                cost: 50,
                stock: 0,
                type: 'service',
                description: 'خدمة تنظيف المكيفات الشباك',
                status: 'active'
            },
            {
                id: 4,
                name: 'ريموت كنترول',
                category: 'parts',
                price: 45,
                cost: 25,
                stock: 20,
                type: 'product',
                description: 'ريموت كنترول للمكيفات',
                status: 'active'
            },
            {
                id: 5,
                name: 'تركيب مكيف جديد',
                category: 'installation',
                price: 200,
                cost: 120,
                stock: 0,
                type: 'service',
                description: 'خدمة تركيب المكيفات الجديدة',
                status: 'active'
            }
        ];
        
        this.filteredProducts = [...this.products];
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">إدارة المنتجات والخدمات</h1>
                        <p class="page-subtitle">إدارة المنتجات والخدمات المتاحة</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-product-btn">
                            <span class="btn-icon">➕</span>
                            إضافة منتج/خدمة
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="search-box">
                        <input type="text" id="product-search" placeholder="البحث في المنتجات..." value="${this.searchTerm}">
                        <span class="search-icon">🔍</span>
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab ${this.selectedCategory === 'all' ? 'active' : ''}" data-category="all">
                            الكل (${this.products.length})
                        </button>
                        <button class="filter-tab ${this.selectedCategory === 'maintenance' ? 'active' : ''}" data-category="maintenance">
                            خدمات الصيانة (${this.products.filter(p => p.category === 'maintenance').length})
                        </button>
                        <button class="filter-tab ${this.selectedCategory === 'parts' ? 'active' : ''}" data-category="parts">
                            قطع الغيار (${this.products.filter(p => p.category === 'parts').length})
                        </button>
                        <button class="filter-tab ${this.selectedCategory === 'installation' ? 'active' : ''}" data-category="installation">
                            خدمات التركيب (${this.products.filter(p => p.category === 'installation').length})
                        </button>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="products-grid">
                    ${this.renderProductCards()}
                </div>

                <!-- Pagination -->
                ${this.renderPagination()}
            </div>
        `;
    }

    /**
     * Render product cards
     */
    renderProductCards() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedProducts = this.filteredProducts.slice(startIndex, endIndex);

        if (paginatedProducts.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-icon">📦</div>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق البحث</p>
                </div>
            `;
        }

        return paginatedProducts.map(product => `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-header">
                    <div class="product-type ${product.type}">
                        ${product.type === 'service' ? '🔧' : '📦'} ${product.type === 'service' ? 'خدمة' : 'منتج'}
                    </div>
                    <div class="product-actions">
                        <button class="action-btn edit-btn" data-action="edit" data-id="${product.id}" title="تعديل">
                            ✏️
                        </button>
                        <button class="action-btn delete-btn" data-action="delete" data-id="${product.id}" title="حذف">
                            🗑️
                        </button>
                    </div>
                </div>
                
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-description">${product.description}</p>
                    
                    <div class="product-details">
                        <div class="detail-item">
                            <span class="detail-label">السعر:</span>
                            <span class="detail-value price">${product.price} ر.س</span>
                        </div>
                        ${product.type === 'product' ? `
                            <div class="detail-item">
                                <span class="detail-label">المخزون:</span>
                                <span class="detail-value stock ${product.stock < 10 ? 'low' : ''}">${product.stock}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <span class="detail-label">الحالة:</span>
                            <span class="status-badge ${product.status}">${product.status === 'active' ? 'نشط' : 'غير نشط'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="pagination-btn active">${i}</button>`;
            } else {
                paginationHTML += `<button class="pagination-btn" data-page="${i}">${i}</button>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }
        
        paginationHTML += '</div>';
        return paginationHTML;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Search functionality
        const searchInput = this.container.querySelector('#product-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.filterProducts();
            });
        }

        // Category filters
        const filterTabs = this.container.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.selectedCategory = e.target.dataset.category;
                this.filterProducts();
            });
        });

        // Product actions
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const productId = parseInt(e.target.dataset.id);

            if (action === 'edit') {
                this.editProduct(productId);
            } else if (action === 'delete') {
                this.deleteProduct(productId);
            }
        });

        // Add product button
        const addBtn = this.container.querySelector('#add-product-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addProduct());
        }

        // Pagination
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn') && e.target.dataset.page) {
                this.currentPage = parseInt(e.target.dataset.page);
                this.renderContent();
                this.attachEventListeners();
            }
        });
    }

    /**
     * Filter products
     */
    filterProducts() {
        this.filteredProducts = this.products.filter(product => {
            const matchesSearch = product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                product.description.toLowerCase().includes(this.searchTerm.toLowerCase());
            const matchesCategory = this.selectedCategory === 'all' || product.category === this.selectedCategory;
            
            return matchesSearch && matchesCategory;
        });

        this.currentPage = 1;
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Add new product
     */
    addProduct() {
        window.uiManager?.showModal({
            title: 'إضافة منتج/خدمة جديد',
            content: this.getProductFormHTML(),
            size: 'large',
            actions: [
                {
                    text: 'إلغاء',
                    class: 'btn-secondary',
                    action: 'cancel'
                },
                {
                    text: 'حفظ',
                    class: 'btn-primary',
                    action: 'save',
                    handler: () => this.saveProduct()
                }
            ]
        });
    }

    /**
     * Edit product
     */
    editProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        window.uiManager?.showModal({
            title: 'تعديل المنتج/الخدمة',
            content: this.getProductFormHTML(product),
            size: 'large',
            actions: [
                {
                    text: 'إلغاء',
                    class: 'btn-secondary',
                    action: 'cancel'
                },
                {
                    text: 'حفظ التغييرات',
                    class: 'btn-primary',
                    action: 'save',
                    handler: () => this.saveProduct(productId)
                }
            ]
        });
    }

    /**
     * Get product form HTML
     */
    getProductFormHTML(product = null) {
        return `
            <form class="product-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="product-name">اسم المنتج/الخدمة *</label>
                        <input type="text" id="product-name" name="name" value="${product?.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="product-type">النوع *</label>
                        <select id="product-type" name="type" required>
                            <option value="product" ${product?.type === 'product' ? 'selected' : ''}>منتج</option>
                            <option value="service" ${product?.type === 'service' ? 'selected' : ''}>خدمة</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="product-category">الفئة *</label>
                        <select id="product-category" name="category" required>
                            <option value="maintenance" ${product?.category === 'maintenance' ? 'selected' : ''}>خدمات الصيانة</option>
                            <option value="parts" ${product?.category === 'parts' ? 'selected' : ''}>قطع الغيار</option>
                            <option value="installation" ${product?.category === 'installation' ? 'selected' : ''}>خدمات التركيب</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="product-status">الحالة *</label>
                        <select id="product-status" name="status" required>
                            <option value="active" ${product?.status === 'active' ? 'selected' : ''}>نشط</option>
                            <option value="inactive" ${product?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="product-description">الوصف</label>
                    <textarea id="product-description" name="description" rows="3">${product?.description || ''}</textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="product-price">سعر البيع (ر.س) *</label>
                        <input type="number" id="product-price" name="price" value="${product?.price || ''}" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="product-cost">التكلفة (ر.س)</label>
                        <input type="number" id="product-cost" name="cost" value="${product?.cost || ''}" step="0.01" min="0">
                    </div>
                </div>
                
                <div class="form-group stock-group" style="display: ${product?.type === 'service' ? 'none' : 'block'}">
                    <label for="product-stock">الكمية في المخزون</label>
                    <input type="number" id="product-stock" name="stock" value="${product?.stock || 0}" min="0">
                </div>
            </form>
        `;
    }

    /**
     * Save product
     */
    saveProduct(productId = null) {
        const form = document.querySelector('.product-form');
        const formData = new FormData(form);
        
        const productData = {
            name: formData.get('name'),
            type: formData.get('type'),
            category: formData.get('category'),
            description: formData.get('description'),
            price: parseFloat(formData.get('price')),
            cost: parseFloat(formData.get('cost')) || 0,
            stock: formData.get('type') === 'service' ? 0 : parseInt(formData.get('stock')) || 0,
            status: formData.get('status')
        };

        if (productId) {
            // Update existing product
            const index = this.products.findIndex(p => p.id === productId);
            if (index !== -1) {
                this.products[index] = { ...this.products[index], ...productData };
                window.uiManager?.showToast('تم تحديث المنتج بنجاح', 'success');
            }
        } else {
            // Add new product
            const newProduct = {
                id: Date.now(),
                ...productData
            };
            this.products.push(newProduct);
            window.uiManager?.showToast('تم إضافة المنتج بنجاح', 'success');
        }

        this.filterProducts();
    }

    /**
     * Delete product
     */
    deleteProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        window.uiManager?.showConfirmation({
            title: 'حذف المنتج',
            message: `هل أنت متأكد من حذف "${product.name}"؟`,
            confirmText: 'حذف',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.products = this.products.filter(p => p.id !== productId);
                this.filterProducts();
                window.uiManager?.showToast('تم حذف المنتج بنجاح', 'success');
            }
        });
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.ProductManagement = ProductManagement;
console.log('📦 ProductManagement class loaded and made globally available');

// Export for use in other modules
export default ProductManagement;
