/**
 * Settings Management Component
 * إدارة الإعدادات
 */
class SettingsManagement {
    constructor() {
        this.container = null;
        this.settings = {};
        this.activeTab = 'company';
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.loadSettings();
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Load settings data
     */
    loadSettings() {
        // Sample settings - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.settings = {
            company: {
                name: 'الماهر للصيانة والخدمات',
                nameEn: 'Almaher Maintenance & Services',
                address: 'فرع الشرائع، 24263، حي الخضراء، مكة المكرمة',
                phone: '0509888210',
                email: '<EMAIL>',
                website: 'almaher-est.com',
                taxId: '311055560500003',
                commercialRecord: '4030123456',
                logo: ''
            },
            invoice: {
                prefix: 'INV',
                startNumber: 1,
                showTax: true,
                taxRate: 15,
                showDiscount: true,
                showNotes: true,
                defaultTerms: 'شكراً لتعاملكم معنا',
                footerText: 'الماهر للصيانة والخدمات - جودة في الخدمة'
            },
            system: {
                language: 'ar',
                timezone: 'Asia/Riyadh',
                dateFormat: 'dd/mm/yyyy',
                currency: 'SAR',
                backupFrequency: 'daily',
                autoSave: true,
                notifications: true
            },
            users: [
                {
                    id: 1,
                    name: 'المدير العام',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'active',
                    lastLogin: '2025-01-15 10:30:00'
                },
                {
                    id: 2,
                    name: 'محاسب',
                    email: '<EMAIL>',
                    role: 'accountant',
                    status: 'active',
                    lastLogin: '2025-01-15 09:15:00'
                }
            ]
        };
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">إعدادات النظام</h1>
                        <p class="page-subtitle">إدارة إعدادات النظام والشركة</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-success" id="save-settings-btn">
                            <span class="btn-icon">💾</span>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>

                <!-- Settings Tabs -->
                <div class="settings-container">
                    <div class="settings-sidebar">
                        <div class="settings-tabs">
                            <button class="settings-tab ${this.activeTab === 'company' ? 'active' : ''}" data-tab="company">
                                <span class="tab-icon">🏢</span>
                                <span class="tab-text">بيانات الشركة</span>
                            </button>
                            <button class="settings-tab ${this.activeTab === 'invoice' ? 'active' : ''}" data-tab="invoice">
                                <span class="tab-icon">📄</span>
                                <span class="tab-text">إعدادات الفواتير</span>
                            </button>
                            <button class="settings-tab ${this.activeTab === 'system' ? 'active' : ''}" data-tab="system">
                                <span class="tab-icon">⚙️</span>
                                <span class="tab-text">إعدادات النظام</span>
                            </button>
                            <button class="settings-tab ${this.activeTab === 'users' ? 'active' : ''}" data-tab="users">
                                <span class="tab-icon">👥</span>
                                <span class="tab-text">إدارة المستخدمين</span>
                            </button>
                            <button class="settings-tab ${this.activeTab === 'backup' ? 'active' : ''}" data-tab="backup">
                                <span class="tab-icon">💾</span>
                                <span class="tab-text">النسخ الاحتياطي</span>
                            </button>
                        </div>
                    </div>

                    <div class="settings-content">
                        ${this.renderTabContent()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render tab content based on active tab
     */
    renderTabContent() {
        switch (this.activeTab) {
            case 'company':
                return this.renderCompanySettings();
            case 'invoice':
                return this.renderInvoiceSettings();
            case 'system':
                return this.renderSystemSettings();
            case 'users':
                return this.renderUsersSettings();
            case 'backup':
                return this.renderBackupSettings();
            default:
                return '';
        }
    }

    /**
     * Render company settings
     */
    renderCompanySettings() {
        const company = this.settings.company;
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h2>بيانات الشركة</h2>
                    <p>إعدادات المعلومات الأساسية للشركة</p>
                </div>

                <form class="settings-form" id="company-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="company-name">اسم الشركة *</label>
                            <input type="text" id="company-name" name="name" value="${company.name}" required>
                        </div>
                        <div class="form-group">
                            <label for="company-name-en">الاسم بالإنجليزية</label>
                            <input type="text" id="company-name-en" name="nameEn" value="${company.nameEn}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="company-address">العنوان *</label>
                        <textarea id="company-address" name="address" rows="3" required>${company.address}</textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="company-phone">رقم الهاتف *</label>
                            <input type="tel" id="company-phone" name="phone" value="${company.phone}" required>
                        </div>
                        <div class="form-group">
                            <label for="company-email">البريد الإلكتروني</label>
                            <input type="email" id="company-email" name="email" value="${company.email}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="company-website">الموقع الإلكتروني</label>
                            <input type="url" id="company-website" name="website" value="${company.website}">
                        </div>
                        <div class="form-group">
                            <label for="company-tax-id">الرقم الضريبي *</label>
                            <input type="text" id="company-tax-id" name="taxId" value="${company.taxId}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="company-commercial-record">رقم السجل التجاري</label>
                        <input type="text" id="company-commercial-record" name="commercialRecord" value="${company.commercialRecord}">
                    </div>

                    <div class="form-group">
                        <label for="company-logo">شعار الشركة</label>
                        <div class="logo-upload-section">
                            <div class="current-logo">
                                <img id="current-logo-img" src="${company.logo || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMDA3YmZmIiByeD0iMTAiLz4KPHR0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+2YU8L3RleHQ+Cjwvc3ZnPg=='}" alt="شعار الشركة" class="logo-preview">
                                <div class="logo-overlay">
                                    <button type="button" class="change-logo-btn" onclick="document.getElementById('company-logo').click()">
                                        تغيير الشعار
                                    </button>
                                </div>
                            </div>
                            <input type="file" id="company-logo" name="logo" accept="image/*" style="display: none;">
                            <div class="logo-actions">
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('company-logo').click()">
                                    <span class="btn-icon">📁</span>
                                    اختيار شعار
                                </button>
                                <button type="button" class="btn btn-secondary" id="remove-logo-btn">
                                    <span class="btn-icon">🗑️</span>
                                    إزالة الشعار
                                </button>
                            </div>
                        </div>
                        <small class="form-help">يفضل أن يكون الشعار بصيغة PNG أو JPG وبحجم 200x200 بكسل</small>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Render invoice settings
     */
    renderInvoiceSettings() {
        const invoice = this.settings.invoice;
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h2>إعدادات الفواتير</h2>
                    <p>تخصيص شكل ومحتوى الفواتير</p>
                </div>

                <form class="settings-form" id="invoice-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-prefix">بادئة رقم الفاتورة</label>
                            <input type="text" id="invoice-prefix" name="prefix" value="${invoice.prefix}">
                        </div>
                        <div class="form-group">
                            <label for="invoice-start-number">رقم البداية</label>
                            <input type="number" id="invoice-start-number" name="startNumber" value="${invoice.startNumber}" min="1">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="tax-rate">معدل الضريبة (%)</label>
                            <input type="number" id="tax-rate" name="taxRate" value="${invoice.taxRate}" min="0" max="100" step="0.01">
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="showTax" ${invoice.showTax ? 'checked' : ''}>
                                    <span class="checkbox-text">إظهار الضريبة في الفاتورة</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="showDiscount" ${invoice.showDiscount ? 'checked' : ''}>
                                    <span class="checkbox-text">إظهار حقل الخصم</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="showNotes" ${invoice.showNotes ? 'checked' : ''}>
                                    <span class="checkbox-text">إظهار حقل الملاحظات</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="default-terms">الشروط والأحكام الافتراضية</label>
                        <textarea id="default-terms" name="defaultTerms" rows="3">${invoice.defaultTerms}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="footer-text">نص التذييل</label>
                        <input type="text" id="footer-text" name="footerText" value="${invoice.footerText}">
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Render system settings
     */
    renderSystemSettings() {
        const system = this.settings.system;
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h2>إعدادات النظام</h2>
                    <p>إعدادات عامة للنظام والواجهة</p>
                </div>

                <form class="settings-form" id="system-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="system-language">لغة النظام</label>
                            <select id="system-language" name="language">
                                <option value="ar" ${system.language === 'ar' ? 'selected' : ''}>العربية</option>
                                <option value="en" ${system.language === 'en' ? 'selected' : ''}>English</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="system-timezone">المنطقة الزمنية</label>
                            <select id="system-timezone" name="timezone">
                                <option value="Asia/Riyadh" ${system.timezone === 'Asia/Riyadh' ? 'selected' : ''}>الرياض (GMT+3)</option>
                                <option value="Asia/Dubai" ${system.timezone === 'Asia/Dubai' ? 'selected' : ''}>دبي (GMT+4)</option>
                                <option value="UTC" ${system.timezone === 'UTC' ? 'selected' : ''}>UTC (GMT+0)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date-format">تنسيق التاريخ</label>
                            <select id="date-format" name="dateFormat">
                                <option value="dd/mm/yyyy" ${system.dateFormat === 'dd/mm/yyyy' ? 'selected' : ''}>يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy" ${system.dateFormat === 'mm/dd/yyyy' ? 'selected' : ''}>شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd" ${system.dateFormat === 'yyyy-mm-dd' ? 'selected' : ''}>سنة-شهر-يوم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="system-currency">العملة</label>
                            <select id="system-currency" name="currency">
                                <option value="SAR" ${system.currency === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                <option value="AED" ${system.currency === 'AED' ? 'selected' : ''}>درهم إماراتي (AED)</option>
                                <option value="USD" ${system.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="backup-frequency">تكرار النسخ الاحتياطي</label>
                            <select id="backup-frequency" name="backupFrequency">
                                <option value="daily" ${system.backupFrequency === 'daily' ? 'selected' : ''}>يومياً</option>
                                <option value="weekly" ${system.backupFrequency === 'weekly' ? 'selected' : ''}>أسبوعياً</option>
                                <option value="monthly" ${system.backupFrequency === 'monthly' ? 'selected' : ''}>شهرياً</option>
                                <option value="manual" ${system.backupFrequency === 'manual' ? 'selected' : ''}>يدوياً فقط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="autoSave" ${system.autoSave ? 'checked' : ''}>
                                    <span class="checkbox-text">الحفظ التلقائي</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="notifications" ${system.notifications ? 'checked' : ''}>
                                <span class="checkbox-text">تفعيل الإشعارات</span>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Render users settings
     */
    renderUsersSettings() {
        const users = this.settings.users;
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h2>إدارة المستخدمين</h2>
                    <p>إدارة حسابات المستخدمين وصلاحياتهم</p>
                    <button class="btn btn-primary" id="add-user-btn">
                        <span class="btn-icon">➕</span>
                        إضافة مستخدم
                    </button>
                </div>

                <div class="users-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td>${user.name}</td>
                                    <td>${user.email}</td>
                                    <td>
                                        <span class="role-badge ${user.role}">
                                            ${user.role === 'admin' ? 'مدير' : user.role === 'accountant' ? 'محاسب' : 'مستخدم'}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge ${user.status}">
                                            ${user.status === 'active' ? 'نشط' : 'غير نشط'}
                                        </span>
                                    </td>
                                    <td>${this.formatDate(user.lastLogin)}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="action-btn edit-btn" data-action="edit-user" data-id="${user.id}" title="تعديل">
                                                ✏️
                                            </button>
                                            <button class="action-btn delete-btn" data-action="delete-user" data-id="${user.id}" title="حذف">
                                                🗑️
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Render backup settings
     */
    renderBackupSettings() {
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h2>النسخ الاحتياطي والاستعادة</h2>
                    <p>إدارة النسخ الاحتياطية للبيانات</p>
                </div>

                <div class="backup-actions">
                    <div class="backup-card">
                        <div class="backup-icon">💾</div>
                        <h3>إنشاء نسخة احتياطية</h3>
                        <p>إنشاء نسخة احتياطية من جميع البيانات</p>
                        <button class="btn btn-primary" id="create-backup-btn">إنشاء نسخة احتياطية</button>
                    </div>

                    <div class="backup-card">
                        <div class="backup-icon">📥</div>
                        <h3>استعادة البيانات</h3>
                        <p>استعادة البيانات من نسخة احتياطية</p>
                        <button class="btn btn-warning" id="restore-backup-btn">استعادة البيانات</button>
                    </div>

                    <div class="backup-card">
                        <div class="backup-icon">🗂️</div>
                        <h3>تصدير البيانات</h3>
                        <p>تصدير البيانات بصيغة Excel أو CSV</p>
                        <button class="btn btn-success" id="export-data-btn">تصدير البيانات</button>
                    </div>
                </div>

                <div class="backup-history">
                    <h3>سجل النسخ الاحتياطية</h3>
                    <div class="backup-list">
                        <div class="backup-item">
                            <div class="backup-info">
                                <strong>نسخة احتياطية تلقائية</strong>
                                <span class="backup-date">15 يناير 2025 - 10:30 ص</span>
                            </div>
                            <div class="backup-size">2.5 MB</div>
                            <div class="backup-actions-small">
                                <button class="btn btn-sm btn-primary">تحميل</button>
                                <button class="btn btn-sm btn-danger">حذف</button>
                            </div>
                        </div>
                        <div class="backup-item">
                            <div class="backup-info">
                                <strong>نسخة احتياطية يدوية</strong>
                                <span class="backup-date">14 يناير 2025 - 3:45 م</span>
                            </div>
                            <div class="backup-size">2.3 MB</div>
                            <div class="backup-actions-small">
                                <button class="btn btn-sm btn-primary">تحميل</button>
                                <button class="btn btn-sm btn-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Settings tabs
        const settingsTabs = this.container.querySelectorAll('.settings-tab');
        settingsTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.activeTab = e.currentTarget.dataset.tab;
                this.renderContent();
                this.attachEventListeners();
            });
        });

        // Save settings button
        const saveBtn = this.container.querySelector('#save-settings-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // Add user button
        const addUserBtn = this.container.querySelector('#add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.addUser();
            });
        }

        // User actions
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const userId = e.target.dataset.id;

            if (action === 'edit-user') {
                this.editUser(parseInt(userId));
            } else if (action === 'delete-user') {
                this.deleteUser(parseInt(userId));
            }
        });

        // Backup actions
        const createBackupBtn = this.container.querySelector('#create-backup-btn');
        const restoreBackupBtn = this.container.querySelector('#restore-backup-btn');
        const exportDataBtn = this.container.querySelector('#export-data-btn');

        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => this.createBackup());
        }

        if (restoreBackupBtn) {
            restoreBackupBtn.addEventListener('click', () => this.restoreBackup());
        }

        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => this.exportData());
        }

        // Logo upload functionality
        this.setupLogoUpload();
    }

    /**
     * Save settings
     */
    saveSettings() {
        const activeForm = this.container.querySelector(`#${this.activeTab}-form`);
        if (!activeForm) return;

        const formData = new FormData(activeForm);
        const settings = {};

        for (const [key, value] of formData.entries()) {
            if (activeForm.querySelector(`[name="${key}"]`).type === 'checkbox') {
                settings[key] = activeForm.querySelector(`[name="${key}"]`).checked;
            } else {
                settings[key] = value;
            }
        }

        // Update settings
        this.settings[this.activeTab] = { ...this.settings[this.activeTab], ...settings };

        window.uiManager?.showToast('تم حفظ الإعدادات بنجاح', 'success');
    }

    /**
     * Add user
     */
    addUser() {
        window.uiManager?.showToast('سيتم إضافة صفحة إضافة المستخدمين قريباً', 'info');
    }

    /**
     * Edit user
     */
    editUser(userId) {
        window.uiManager?.showToast('سيتم إضافة صفحة تعديل المستخدمين قريباً', 'info');
    }

    /**
     * Delete user
     */
    deleteUser(userId) {
        const user = this.settings.users.find(u => u.id === userId);
        if (!user) return;

        window.uiManager?.showConfirmation({
            title: 'حذف المستخدم',
            message: `هل أنت متأكد من حذف المستخدم "${user.name}"؟`,
            confirmText: 'حذف',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.settings.users = this.settings.users.filter(u => u.id !== userId);
                this.renderContent();
                this.attachEventListeners();
                window.uiManager?.showToast('تم حذف المستخدم بنجاح', 'success');
            }
        });
    }

    /**
     * Create backup
     */
    createBackup() {
        window.uiManager?.showToast('جاري إنشاء النسخة الاحتياطية...', 'info');
        
        // Simulate backup creation
        setTimeout(() => {
            window.uiManager?.showToast('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }, 2000);
    }

    /**
     * Restore backup
     */
    restoreBackup() {
        window.uiManager?.showConfirmation({
            title: 'استعادة البيانات',
            message: 'هل أنت متأكد من استعادة البيانات؟ سيتم استبدال البيانات الحالية.',
            confirmText: 'استعادة',
            confirmClass: 'btn-warning',
            onConfirm: () => {
                window.uiManager?.showToast('سيتم إضافة وظيفة الاستعادة قريباً', 'info');
            }
        });
    }

    /**
     * Export data
     */
    exportData() {
        window.uiManager?.showToast('سيتم إضافة وظيفة التصدير قريباً', 'info');
    }

    /**
     * Setup logo upload functionality
     */
    setupLogoUpload() {
        const logoInput = this.container.querySelector('#company-logo');
        const logoPreview = this.container.querySelector('#current-logo-img');
        const removeLogoBtn = this.container.querySelector('#remove-logo-btn');

        if (logoInput) {
            logoInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleLogoUpload(file, logoPreview);
                }
            });
        }

        if (removeLogoBtn) {
            removeLogoBtn.addEventListener('click', () => {
                this.removeLogo(logoPreview);
            });
        }
    }

    /**
     * Handle logo upload
     */
    handleLogoUpload(file, previewElement) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            window.uiManager?.showToast('يرجى اختيار ملف صورة صالح (JPG, PNG, GIF)', 'error');
            return;
        }

        // Validate file size (max 2MB)
        const maxSize = 2 * 1024 * 1024; // 2MB
        if (file.size > maxSize) {
            window.uiManager?.showToast('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت', 'error');
            return;
        }

        // Read and preview file
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageData = e.target.result;
            if (previewElement) {
                previewElement.src = imageData;
            }

            // Save to settings
            this.settings.company.logo = imageData;

            // Update header logo if exists
            this.updateHeaderLogo(imageData);

            window.uiManager?.showToast('تم تحديث الشعار بنجاح', 'success');
        };

        reader.onerror = () => {
            window.uiManager?.showToast('خطأ في قراءة الملف', 'error');
        };

        reader.readAsDataURL(file);
    }

    /**
     * Remove logo
     */
    removeLogo(previewElement) {
        window.uiManager?.showConfirmation({
            title: 'إزالة الشعار',
            message: 'هل أنت متأكد من إزالة شعار الشركة؟',
            confirmText: 'إزالة',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                // Reset to default logo
                const defaultLogo = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMDA3YmZmIiByeD0iMTAiLz4KPHR0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+2YU8L3RleHQ+Cjwvc3ZnPg==';

                if (previewElement) {
                    previewElement.src = defaultLogo;
                }

                // Update settings
                this.settings.company.logo = '';

                // Update header logo
                this.updateHeaderLogo(defaultLogo);

                window.uiManager?.showToast('تم إزالة الشعار', 'success');
            }
        });
    }

    /**
     * Update header logo
     */
    updateHeaderLogo(logoData) {
        const headerLogo = document.querySelector('.company-brand img');
        if (headerLogo) {
            headerLogo.src = logoData;
        }
    }

    /**
     * Format date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' - ' + date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Export for use in other modules
window.SettingsManagement = SettingsManagement;
