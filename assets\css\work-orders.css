/* ===== WORK ORDER MANAGEMENT STYLES ===== */

.work-order-management {
  padding: var(--space-lg);
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== STATISTICS CARDS ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.stat-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-neumorphic-outset);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: var(--transition-normal);
  border: 1px solid var(--border-light);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  background: var(--bg-tertiary);
}

.stat-card.new .stat-icon {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.stat-card.in-progress .stat-icon {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: white;
}

.stat-card.completed .stat-icon {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* ===== FILTERS SECTION ===== */
.filters-section {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-neumorphic-outset);
  border: 1px solid var(--border-light);
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-lg);
  align-items: end;
}

.search-box {
  position: relative;
}

.search-input {
  width: 100%;
  padding: var(--space-md) var(--space-md) var(--space-md) 40px;
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-md);
  background: var(--bg-input);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  transition: var(--transition-colors);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.2rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.filter-group label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.filter-select {
  padding: var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-md);
  background: var(--bg-input);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  transition: var(--transition-colors);
}

.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== WORK ORDERS SECTION ===== */
.work-orders-section {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-neumorphic-outset);
  border: 1px solid var(--border-light);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-light);
}

.section-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.view-toggle {
  display: flex;
  gap: var(--space-xs);
}

.view-btn {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-md);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-colors);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.view-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.view-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* ===== WORK ORDERS GRID (CARDS VIEW) ===== */
.work-orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-lg);
}

.work-order-card {
  background: var(--bg-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  cursor: pointer;
  overflow: hidden;
}

.work-order-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.card-id {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-size: var(--font-size-sm);
}

.card-badges {
  display: flex;
  gap: var(--space-xs);
}

.priority-badge, .status-badge {
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 11px;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: 4px;
}

.priority-badge.urgent {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.priority-badge.normal {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.priority-badge.low {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.status-badge.new {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.status-badge.in_progress {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-badge.completed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-badge.cancelled {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.card-content {
  padding: var(--space-md);
}

.customer-name {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.service-type {
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-sm);
}

.description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  margin-bottom: var(--space-md);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
}

.detail-icon {
  width: 16px;
  text-align: center;
}

.detail-text {
  color: var(--text-secondary);
}

.card-actions {
  display: flex;
  gap: var(--space-xs);
  padding: var(--space-md);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.card-actions .btn {
  flex: 1;
}

.status-dropdown {
  position: relative;
}

/* ===== WORK ORDERS TABLE ===== */
.work-orders-table-container {
  overflow-x: auto;
}

.work-orders-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.work-orders-table th {
  background: var(--bg-tertiary);
  padding: var(--space-md);
  text-align: right;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  font-size: var(--font-size-sm);
}

.work-orders-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.work-order-row {
  transition: var(--transition-colors);
  cursor: pointer;
}

.work-order-row:hover {
  background: var(--bg-hover);
}

.wo-id {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
}

.customer-info .customer-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.customer-info .customer-phone {
  font-size: 11px;
  color: var(--text-muted);
}

.action-buttons {
  display: flex;
  gap: var(--space-xs);
}

.action-buttons .btn {
  min-width: 32px;
  padding: 4px 8px;
}

/* ===== EMPTY STATE ===== */
.empty-state {
  text-align: center;
  padding: var(--space-xl) var(--space-lg);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 var(--space-md) 0;
  color: var(--text-secondary);
}

.empty-state p {
  margin: 0 0 var(--space-lg) 0;
  color: var(--text-muted);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .work-orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .work-order-management {
    padding: var(--space-md);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .work-orders-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--space-md);
    align-items: stretch;
  }
  
  .view-toggle {
    justify-content: center;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
