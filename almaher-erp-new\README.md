# نظام الماهر للصيانة والخدمات ERP

نظام شامل لإدارة موارد المؤسسة مصمم خصيصاً للشركات العربية

## 🚀 طرق تشغيل النظام

### الطريقة الأولى: تشغيل تلقائي
```bash
# انقر نقراً مزدوجاً على الملف
تشغيل_النظام.bat
```

### الطريقة الثانية: خادم Node.js
```bash
node simple-server.js
# ثم افتح: http://localhost:8080
```

### الطريقة الثالثة: خادم Python
```bash
python -m http.server 8080
# ثم افتح: http://localhost:8080
```

### الطريقة الرابعة: فتح مباشر
```bash
# افتح الملف في المتصفح مباشرة
index.html
```

## 📋 الوحدات المتوفرة

### الوحدات الأساسية (13 وحدة)
1. **🏠 الرئيسية** - لوحة التحكم والإحصائيات
2. **👥 العملاء** - إدارة العملاء والزبائن  
3. **🏢 الموردين** - إدارة الموردين والشركاء
4. **📞 جهات الاتصال** - دليل جهات الاتصال
5. **📦 المنتجات** - كتالوج المنتجات والخدمات
6. **🧾 الفواتير** - إنشاء وإدارة الفواتير
7. **💰 عروض الأسعار** - إدارة العروض والتسعير
8. **🛒 المشتريات** - إدارة طلبات الشراء
9. **💳 المدفوعات** - تتبع المدفوعات والمالية
10. **🔧 أوامر الشغل** - إدارة أوامر الصيانة
11. **📊 التقارير** - التقارير والإحصائيات
12. **⚙️ الإعدادات** - إعدادات النظام والشركة

## 🎨 الثيمات المتوفرة

- **🌞 الثيم الفاتح** (افتراضي)
- **🌙 الثيم الداكن**
- **🔵 الثيم الأزرق**
- **🟢 الثيم الأخضر**

## 📱 التوافق

- ✅ **الشاشات الكبيرة** (Desktop)
- ✅ **الأجهزة اللوحية** (Tablet)  
- ✅ **الهواتف الذكية** (Mobile)
- ✅ **دعم RTL كامل** للعربية

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (Vanilla)** - البرمجة والتفاعل
- **LocalStorage** - تخزين البيانات محلياً
- **Hash Routing** - نظام التنقل
- **Responsive Design** - التصميم المتجاوب

## 📁 هيكل المشروع

```
almaher-erp-new/
├── index.html              # الصفحة الرئيسية
├── package.json           # إعدادات المشروع
├── server.js              # خادم Node.js
├── simple-server.js       # خادم مبسط
├── تشغيل_النظام.bat        # ملف التشغيل التلقائي
├── assets/                # الملفات الثابتة
│   └── css/              # ملفات التنسيق
│       ├── variables.css  # المتغيرات والألوان
│       ├── base.css      # الأنماط الأساسية
│       ├── layout.css    # تخطيط الصفحة
│       ├── components.css # مكونات التصميم
│       ├── themes.css    # الثيمات المتعددة
│       └── rtl.css       # دعم اللغة العربية
└── js/                   # ملفات JavaScript
    └── app.js            # الملف الرئيسي للتطبيق
```

## 🔧 المتطلبات

### الحد الأدنى
- **متصفح حديث** يدعم ES6+
- **JavaScript مفعل**

### للتطوير
- **Node.js** v14+ (اختياري)
- **Python** 3.x (اختياري)

## 📞 معلومات الشركة

**الماهر للصيانة والخدمات**
- 📱 الجوال: **********
- ☎️ الهاتف: **********  
- 📧 البريد: <EMAIL>
- 🌐 الموقع: www.almaher-services.com
- 📍 العنوان: الرياض، المملكة العربية السعودية
- 🆔 الرقم الضريبي: 300123456789003

## 📄 الترخيص

MIT License - مفتوح المصدر

## 🤝 المساهمة

نرحب بالمساهمات والتحسينات على النظام

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات الشركات العربية** 🇸🇦
