/* ===== NEUMORPHIC COMPONENTS ===== */

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-lg);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-all);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  box-shadow: var(--shadow-neumorphic-outset);
  min-height: 2.5rem;
}

.btn:hover {
  box-shadow: var(--shadow-neumorphic-hover);
  transform: translateY(-1px);
}

.btn:active, .btn.pressed {
  box-shadow: var(--shadow-neumorphic-pressed);
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: var(--shadow-neumorphic-outset);
  transform: none;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--text-inverse);
}

.btn-warning {
  background-color: var(--color-warning);
  color: var(--text-inverse);
}

.btn-error {
  background-color: var(--color-error);
  color: var(--text-inverse);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-size-sm);
  min-height: 2rem;
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-lg);
  min-height: 3rem;
}

.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: var(--border-radius-full);
}

/* ===== FORM CONTROLS ===== */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  transition: var(--transition-colors);
  box-shadow: var(--shadow-neumorphic-inset);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--shadow-neumorphic-inset), 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-control:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form Control Variants */
.form-control-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-sm);
}

.form-control-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
}

/* ===== CARDS ===== */
.card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-neumorphic-card);
  overflow: hidden;
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-neumorphic-hover);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--bg-tertiary);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-tertiary);
}

/* ===== MODALS ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-dialog {
  position: relative;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  transform: scale(0.9) translateY(-20px);
  transition: var(--transition-normal);
}

.modal.show .modal-dialog {
  transform: scale(1) translateY(0);
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-tertiary);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-full);
  transition: var(--transition-colors);
}

.modal-close:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-lg);
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
  background-color: var(--bg-tertiary);
}

/* Modal Sizes */
.modal-small .modal-dialog {
  max-width: 400px;
}

.modal-large .modal-dialog {
  max-width: 800px;
}

.modal-extra-large .modal-dialog {
  max-width: 1200px;
}

/* ===== TOASTS ===== */
.toast-container {
  position: fixed;
  top: var(--space-lg);
  left: var(--space-lg);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  pointer-events: none;
}

.toast {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  padding: var(--space-md);
  min-width: 300px;
  max-width: 500px;
  transform: translateX(-100%);
  opacity: 0;
  transition: var(--transition-normal);
  pointer-events: auto;
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-full);
  transition: var(--transition-colors);
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

/* Toast Types */
.toast-success {
  border-right: 4px solid var(--color-success);
}

.toast-error {
  border-right: 4px solid var(--color-error);
}

.toast-warning {
  border-right: 4px solid var(--color-warning);
}

.toast-info {
  border-right: 4px solid var(--color-info);
}

/* ===== DROPDOWNS ===== */
.dropdown {
  position: absolute;
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  min-width: 200px;
  transform: scale(0.95) translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-fast);
  z-index: var(--z-dropdown);
}

.dropdown.show {
  transform: scale(1) translateY(0);
  opacity: 1;
  visibility: visible;
}

.dropdown-menu {
  list-style: none;
  padding: var(--space-xs) 0;
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-colors);
}

.dropdown-item:hover {
  background-color: var(--bg-hover);
}

.dropdown-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropdown-item.disabled:hover {
  background-color: transparent;
}

.dropdown-icon {
  font-size: var(--font-size-sm);
  width: 1rem;
  text-align: center;
}

.dropdown-text {
  flex: 1;
  font-size: var(--font-size-sm);
}

/* ===== TABLES ===== */
.table-container {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-neumorphic-card);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.table th,
.table td {
  padding: var(--space-md);
  text-align: right;
  border-bottom: 1px solid var(--border-light);
}

.table th {
  background-color: var(--bg-tertiary);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tbody tr:hover {
  background-color: var(--bg-hover);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.badge-success {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.badge-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.badge-error {
  background-color: var(--color-error-light);
  color: var(--color-error);
}

.badge-info {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

/* ===== PROGRESS BARS ===== */
.progress {
  width: 100%;
  height: 0.5rem;
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  box-shadow: var(--shadow-neumorphic-inset);
}

.progress-bar {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== LOADING SPINNERS ===== */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner-sm {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

.loading-spinner-lg {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* ===== PAGE LOADER ===== */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.page-loader.show {
  opacity: 1;
  visibility: visible;
}

.page-loader-content {
  background-color: var(--bg-card);
  padding: var(--space-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-neumorphic-modal);
  text-align: center;
  min-width: 200px;
}

.page-loader-content .loading-spinner {
  margin: 0 auto var(--space-md);
}

.page-loader-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* ===== ELEMENT LOADER ===== */
.element-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

.element-loader-content {
  text-align: center;
}

.element-loader-content .loading-spinner {
  margin: 0 auto var(--space-sm);
}

.element-loader-content span {
  display: block;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Dark theme adjustments for element loader */
[data-theme="dark"] .element-loader {
  background-color: rgba(0, 0, 0, 0.8);
}

/* ===== DASHBOARD COMPONENTS ===== */

/* Dashboard Container */
.dashboard-container {
  padding: var(--space-lg);
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-2xl);
  gap: var(--space-lg);
}

.welcome-section h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-sm);
}

.welcome-section p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: var(--space-md);
  flex-shrink: 0;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.metric-card {
  padding: var(--space-lg);
  transition: var(--transition-all);
}

.metric-card:hover {
  transform: translateY(-4px);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.metric-icon {
  width: 4rem;
  height: 4rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.metric-icon-primary {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.metric-icon-success {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.metric-icon-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.metric-icon-info {
  background-color: var(--color-info-light);
  color: var(--color-info);
}

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
}

.metric-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-xs) 0;
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-full);
}

.metric-change-positive {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.metric-change-negative {
  background-color: var(--color-error-light);
  color: var(--color-error);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.dashboard-section {
  min-height: 300px;
}

/* Activities List */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  transition: var(--transition-colors);
}

.activity-item:hover {
  background-color: var(--bg-hover);
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--border-radius-full);
  background-color: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
}

.activity-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-xs) 0;
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* Quick Stats */
.quick-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius-md);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* Tasks List */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.task-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  transition: var(--transition-colors);
}

.task-item:hover {
  background-color: var(--bg-hover);
}

.task-priority {
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-full);
  flex-shrink: 0;
}

.task-priority-high {
  background-color: var(--color-error);
}

.task-priority-medium {
  background-color: var(--color-warning);
}

.task-priority-low {
  background-color: var(--color-success);
}

.task-title {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.task-action {
  flex-shrink: 0;
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-md);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  border: none;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  text-align: center;
  min-height: 100px;
}

.quick-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-hover);
}

.quick-action-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphic-pressed);
}

.quick-action-icon {
  font-size: var(--font-size-2xl);
}

.quick-action-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
}

/* Responsive Dashboard */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--space-md);
  }

  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }

  .welcome-section h1 {
    font-size: var(--font-size-2xl);
  }

  .dashboard-actions {
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .metric-content {
    gap: var(--space-md);
  }

  .metric-icon {
    width: 3rem;
    height: 3rem;
    font-size: var(--font-size-xl);
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== NOT FOUND PAGE ===== */
.not-found-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: var(--space-2xl);
}

.not-found-content {
  text-align: center;
  max-width: 600px;
}

.not-found-illustration {
  margin-bottom: var(--space-2xl);
  position: relative;
}

.error-code {
  font-size: 8rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-md);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.7;
}

.not-found-text {
  margin-bottom: var(--space-2xl);
}

.not-found-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.not-found-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.not-found-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.helpful-links {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-neumorphic-card);
}

.helpful-links h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.links-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-sm);
}

.links-list li {
  margin: 0;
}

.helpful-link {
  display: block;
  padding: var(--space-sm) var(--space-md);
  color: var(--color-primary);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  transition: var(--transition-colors);
  font-size: var(--font-size-sm);
}

.helpful-link:hover {
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
  text-decoration: none;
}

/* Responsive Not Found */
@media (max-width: 768px) {
  .not-found-container {
    padding: var(--space-lg);
    min-height: 50vh;
  }

  .error-code {
    font-size: 6rem;
  }

  .not-found-title {
    font-size: var(--font-size-2xl);
  }

  .not-found-description {
    font-size: var(--font-size-base);
  }

  .not-found-actions {
    flex-direction: column;
    align-items: center;
  }

  .not-found-actions .btn {
    width: 100%;
    max-width: 250px;
  }

  .links-list {
    grid-template-columns: 1fr;
  }
}
