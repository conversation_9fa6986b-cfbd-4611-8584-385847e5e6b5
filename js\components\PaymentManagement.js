/**
 * Payment Management Component
 * إدارة المدفوعات
 */
class PaymentManagement {
    constructor() {
        this.container = null;
        this.payments = [];
        this.filteredPayments = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.selectedType = 'all';
        this.selectedMethod = 'all';
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.loadPayments();
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Load payments data
     */
    loadPayments() {
        // Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.payments = [
            {
                id: 'PAY-001',
                type: 'income',
                invoiceId: 'INV-001',
                customerName: 'أحمد محمد',
                amount: 450.00,
                method: 'cash',
                date: '2025-01-15',
                status: 'completed',
                notes: 'دفعة كاملة للفاتورة'
            },
            {
                id: 'PAY-002',
                type: 'income',
                invoiceId: 'INV-002',
                customerName: 'فاطمة أحمد',
                amount: 100.00,
                method: 'card',
                date: '2025-01-14',
                status: 'completed',
                notes: 'دفعة جزئية'
            },
            {
                id: 'PAY-003',
                type: 'expense',
                invoiceId: 'PR-001',
                customerName: 'شركة قطع الغيار المتقدمة',
                amount: 1250.00,
                method: 'transfer',
                date: '2025-01-13',
                status: 'pending',
                notes: 'دفع لطلب الشراء'
            },
            {
                id: 'PAY-004',
                type: 'income',
                invoiceId: 'INV-005',
                customerName: 'عبدالله سعد',
                amount: 320.00,
                method: 'cash',
                date: '2025-01-12',
                status: 'completed',
                notes: 'دفعة كاملة نقداً'
            },
            {
                id: 'PAY-005',
                type: 'expense',
                invoiceId: 'EXP-001',
                customerName: 'مصاريف التشغيل',
                amount: 500.00,
                method: 'cash',
                date: '2025-01-11',
                status: 'completed',
                notes: 'مصاريف شهرية'
            },
            {
                id: 'PAY-006',
                type: 'income',
                invoiceId: 'INV-003',
                customerName: 'محمد علي',
                amount: 200.00,
                method: 'check',
                date: '2025-01-10',
                status: 'pending',
                notes: 'شيك مؤجل'
            }
        ];
        
        this.filteredPayments = [...this.payments];
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">إدارة المدفوعات</h1>
                        <p class="page-subtitle">متابعة المدفوعات الواردة والصادرة</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-success" id="add-income-btn">
                            <span class="btn-icon">💰</span>
                            إضافة إيراد
                        </button>
                        <button class="btn btn-danger" id="add-expense-btn">
                            <span class="btn-icon">💸</span>
                            إضافة مصروف
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    ${this.renderStatsCards()}
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="search-box">
                        <input type="text" id="payment-search" placeholder="البحث في المدفوعات..." value="${this.searchTerm}">
                        <span class="search-icon">🔍</span>
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab ${this.selectedType === 'all' ? 'active' : ''}" data-type="all">
                            الكل (${this.payments.length})
                        </button>
                        <button class="filter-tab ${this.selectedType === 'income' ? 'active' : ''}" data-type="income">
                            إيرادات (${this.payments.filter(p => p.type === 'income').length})
                        </button>
                        <button class="filter-tab ${this.selectedType === 'expense' ? 'active' : ''}" data-type="expense">
                            مصروفات (${this.payments.filter(p => p.type === 'expense').length})
                        </button>
                    </div>
                    <div class="filter-row">
                        <select id="method-filter" class="filter-select">
                            <option value="all">جميع طرق الدفع</option>
                            <option value="cash" ${this.selectedMethod === 'cash' ? 'selected' : ''}>نقداً</option>
                            <option value="card" ${this.selectedMethod === 'card' ? 'selected' : ''}>بطاقة ائتمان</option>
                            <option value="transfer" ${this.selectedMethod === 'transfer' ? 'selected' : ''}>تحويل بنكي</option>
                            <option value="check" ${this.selectedMethod === 'check' ? 'selected' : ''}>شيك</option>
                        </select>
                    </div>
                </div>

                <!-- Payments Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم المدفوعة</th>
                                <th>النوع</th>
                                <th>المرجع</th>
                                <th>العميل/المورد</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.renderPaymentRows()}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                ${this.renderPagination()}
            </div>
        `;
    }

    /**
     * Render stats cards
     */
    renderStatsCards() {
        const totalPayments = this.payments.length;
        const totalIncome = this.payments.filter(p => p.type === 'income' && p.status === 'completed')
                                        .reduce((sum, p) => sum + p.amount, 0);
        const totalExpense = this.payments.filter(p => p.type === 'expense' && p.status === 'completed')
                                         .reduce((sum, p) => sum + p.amount, 0);
        const netIncome = totalIncome - totalExpense;
        const pendingPayments = this.payments.filter(p => p.status === 'pending').length;

        return `
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <div class="stat-value">${totalPayments}</div>
                    <div class="stat-label">إجمالي المدفوعات</div>
                </div>
            </div>
            <div class="stat-card income">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-value">${totalIncome.toFixed(2)} ر.س</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                </div>
            </div>
            <div class="stat-card expense">
                <div class="stat-icon">💸</div>
                <div class="stat-content">
                    <div class="stat-value">${totalExpense.toFixed(2)} ر.س</div>
                    <div class="stat-label">إجمالي المصروفات</div>
                </div>
            </div>
            <div class="stat-card ${netIncome >= 0 ? 'profit' : 'loss'}">
                <div class="stat-icon">${netIncome >= 0 ? '📈' : '📉'}</div>
                <div class="stat-content">
                    <div class="stat-value">${netIncome.toFixed(2)} ر.س</div>
                    <div class="stat-label">صافي الربح</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <div class="stat-value">${pendingPayments}</div>
                    <div class="stat-label">مدفوعات معلقة</div>
                </div>
            </div>
        `;
    }

    /**
     * Render payment rows
     */
    renderPaymentRows() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedPayments = this.filteredPayments.slice(startIndex, endIndex);

        if (paginatedPayments.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="empty-state">
                        <div class="empty-icon">💳</div>
                        <h3>لا توجد مدفوعات</h3>
                        <p>لم يتم العثور على مدفوعات تطابق البحث</p>
                    </td>
                </tr>
            `;
        }

        return paginatedPayments.map(payment => {
            const typeText = payment.type === 'income' ? 'إيراد' : 'مصروف';
            const methodText = {
                'cash': 'نقداً',
                'card': 'بطاقة ائتمان',
                'transfer': 'تحويل بنكي',
                'check': 'شيك'
            };
            const statusText = {
                'completed': 'مكتملة',
                'pending': 'معلقة',
                'cancelled': 'ملغية'
            };

            return `
                <tr data-payment-id="${payment.id}">
                    <td>
                        <div class="payment-id">
                            <strong>${payment.id}</strong>
                        </div>
                    </td>
                    <td>
                        <span class="type-badge ${payment.type}">${typeText}</span>
                    </td>
                    <td>
                        <div class="reference-info">
                            <strong>${payment.invoiceId}</strong>
                        </div>
                    </td>
                    <td>
                        <div class="customer-name">${payment.customerName}</div>
                    </td>
                    <td class="amount ${payment.type}">
                        ${payment.type === 'income' ? '+' : '-'}${payment.amount.toFixed(2)} ر.س
                    </td>
                    <td>
                        <span class="method-badge">${methodText[payment.method]}</span>
                    </td>
                    <td>${this.formatDate(payment.date)}</td>
                    <td>
                        <span class="status-badge ${payment.status}">${statusText[payment.status]}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" data-action="view" data-id="${payment.id}" title="عرض">
                                👁️
                            </button>
                            <button class="action-btn edit-btn" data-action="edit" data-id="${payment.id}" title="تعديل">
                                ✏️
                            </button>
                            <button class="action-btn print-btn" data-action="print" data-id="${payment.id}" title="طباعة">
                                🖨️
                            </button>
                            ${payment.status === 'pending' ? `
                                <button class="action-btn confirm-btn" data-action="confirm" data-id="${payment.id}" title="تأكيد">
                                    ✅
                                </button>
                            ` : ''}
                            <button class="action-btn delete-btn" data-action="delete" data-id="${payment.id}" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const totalPages = Math.ceil(this.filteredPayments.length / this.itemsPerPage);
        
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="pagination-btn active">${i}</button>`;
            } else {
                paginationHTML += `<button class="pagination-btn" data-page="${i}">${i}</button>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }
        
        paginationHTML += '</div>';
        return paginationHTML;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Search functionality
        const searchInput = this.container.querySelector('#payment-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.filterPayments();
            });
        }

        // Type filters
        const filterTabs = this.container.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.selectedType = e.target.dataset.type;
                this.filterPayments();
            });
        });

        // Method filter
        const methodFilter = this.container.querySelector('#method-filter');
        if (methodFilter) {
            methodFilter.addEventListener('change', (e) => {
                this.selectedMethod = e.target.value;
                this.filterPayments();
            });
        }

        // Payment actions
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const paymentId = e.target.dataset.id;

            switch (action) {
                case 'view':
                    this.viewPayment(paymentId);
                    break;
                case 'edit':
                    this.editPayment(paymentId);
                    break;
                case 'print':
                    this.printPayment(paymentId);
                    break;
                case 'confirm':
                    this.confirmPayment(paymentId);
                    break;
                case 'delete':
                    this.deletePayment(paymentId);
                    break;
            }
        });

        // Add payment buttons
        const addIncomeBtn = this.container.querySelector('#add-income-btn');
        const addExpenseBtn = this.container.querySelector('#add-expense-btn');
        
        if (addIncomeBtn) {
            addIncomeBtn.addEventListener('click', () => this.addPayment('income'));
        }
        
        if (addExpenseBtn) {
            addExpenseBtn.addEventListener('click', () => this.addPayment('expense'));
        }

        // Pagination
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn') && e.target.dataset.page) {
                this.currentPage = parseInt(e.target.dataset.page);
                this.renderContent();
                this.attachEventListeners();
            }
        });
    }

    /**
     * Filter payments
     */
    filterPayments() {
        this.filteredPayments = this.payments.filter(payment => {
            const matchesSearch = payment.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                payment.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                payment.invoiceId.toLowerCase().includes(this.searchTerm.toLowerCase());
            const matchesType = this.selectedType === 'all' || payment.type === this.selectedType;
            const matchesMethod = this.selectedMethod === 'all' || payment.method === this.selectedMethod;
            
            return matchesSearch && matchesType && matchesMethod;
        });

        this.currentPage = 1;
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * View payment details
     */
    viewPayment(paymentId) {
        const payment = this.payments.find(p => p.id === paymentId);
        if (!payment) return;

        const typeText = payment.type === 'income' ? 'إيراد' : 'مصروف';
        const methodText = {
            'cash': 'نقداً',
            'card': 'بطاقة ائتمان',
            'transfer': 'تحويل بنكي',
            'check': 'شيك'
        };
        const statusText = {
            'completed': 'مكتملة',
            'pending': 'معلقة',
            'cancelled': 'ملغية'
        };

        window.uiManager?.showModal({
            title: `تفاصيل المدفوعة ${payment.id}`,
            content: `
                <div class="payment-details">
                    <div class="payment-header">
                        <div class="payment-info">
                            <h4>بيانات المدفوعة</h4>
                            <p><strong>النوع:</strong> <span class="type-badge ${payment.type}">${typeText}</span></p>
                            <p><strong>المرجع:</strong> ${payment.invoiceId}</p>
                            <p><strong>التاريخ:</strong> ${this.formatDate(payment.date)}</p>
                            <p><strong>الحالة:</strong> <span class="status-badge ${payment.status}">${statusText[payment.status]}</span></p>
                        </div>
                        <div class="customer-info">
                            <h4>${payment.type === 'income' ? 'بيانات العميل' : 'بيانات المورد'}</h4>
                            <p><strong>الاسم:</strong> ${payment.customerName}</p>
                        </div>
                    </div>
                    
                    <div class="payment-amount">
                        <h4>تفاصيل المبلغ</h4>
                        <div class="amount-display ${payment.type}">
                            <span class="amount-label">${typeText}:</span>
                            <span class="amount-value">${payment.amount.toFixed(2)} ر.س</span>
                        </div>
                        <p><strong>طريقة الدفع:</strong> ${methodText[payment.method]}</p>
                    </div>
                    
                    ${payment.notes ? `
                        <div class="payment-notes">
                            <h4>ملاحظات</h4>
                            <p>${payment.notes}</p>
                        </div>
                    ` : ''}
                </div>
            `,
            size: 'medium',
            actions: [
                {
                    text: 'إغلاق',
                    class: 'btn-secondary',
                    action: 'close'
                },
                {
                    text: 'طباعة',
                    class: 'btn-primary',
                    action: 'print',
                    handler: () => this.printPayment(paymentId)
                }
            ]
        });
    }

    /**
     * Add new payment
     */
    addPayment(type) {
        const typeText = type === 'income' ? 'إيراد' : 'مصروف';
        
        window.uiManager?.showModal({
            title: `إضافة ${typeText} جديد`,
            content: `
                <form class="payment-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="payment-reference">المرجع *</label>
                            <input type="text" id="payment-reference" name="reference" placeholder="رقم الفاتورة أو المرجع" required>
                        </div>
                        <div class="form-group">
                            <label for="payment-customer">${type === 'income' ? 'العميل' : 'المورد'} *</label>
                            <input type="text" id="payment-customer" name="customer" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="payment-amount">المبلغ (ر.س) *</label>
                            <input type="number" id="payment-amount" name="amount" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="payment-method">طريقة الدفع *</label>
                            <select id="payment-method" name="method" required>
                                <option value="cash">نقداً</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment-date">تاريخ الدفع *</label>
                        <input type="date" id="payment-date" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment-notes">ملاحظات</label>
                        <textarea id="payment-notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            `,
            size: 'medium',
            actions: [
                {
                    text: 'إلغاء',
                    class: 'btn-secondary',
                    action: 'cancel'
                },
                {
                    text: 'حفظ',
                    class: 'btn-primary',
                    action: 'save',
                    handler: () => this.savePayment(type)
                }
            ]
        });
    }

    /**
     * Save payment
     */
    savePayment(type) {
        const form = document.querySelector('.payment-form');
        const formData = new FormData(form);
        
        const newPayment = {
            id: `PAY-${String(this.payments.length + 1).padStart(3, '0')}`,
            type: type,
            invoiceId: formData.get('reference'),
            customerName: formData.get('customer'),
            amount: parseFloat(formData.get('amount')),
            method: formData.get('method'),
            date: formData.get('date'),
            status: 'completed',
            notes: formData.get('notes') || ''
        };

        this.payments.unshift(newPayment);
        this.filterPayments();
        window.uiManager?.showToast(`تم إضافة ${type === 'income' ? 'الإيراد' : 'المصروف'} بنجاح`, 'success');
    }

    /**
     * Edit payment
     */
    editPayment(paymentId) {
        window.uiManager?.showToast('سيتم إضافة صفحة تعديل المدفوعات قريباً', 'info');
    }

    /**
     * Print payment
     */
    printPayment(paymentId) {
        window.uiManager?.showToast('سيتم إضافة وظيفة الطباعة قريباً', 'info');
    }

    /**
     * Confirm payment
     */
    confirmPayment(paymentId) {
        const payment = this.payments.find(p => p.id === paymentId);
        if (!payment) return;

        window.uiManager?.showConfirmation({
            title: 'تأكيد المدفوعة',
            message: `هل تريد تأكيد المدفوعة "${payment.id}"؟`,
            confirmText: 'تأكيد',
            confirmClass: 'btn-success',
            onConfirm: () => {
                payment.status = 'completed';
                this.filterPayments();
                window.uiManager?.showToast('تم تأكيد المدفوعة', 'success');
            }
        });
    }

    /**
     * Delete payment
     */
    deletePayment(paymentId) {
        const payment = this.payments.find(p => p.id === paymentId);
        if (!payment) return;

        window.uiManager?.showConfirmation({
            title: 'حذف المدفوعة',
            message: `هل أنت متأكد من حذف المدفوعة "${payment.id}"؟`,
            confirmText: 'حذف',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.payments = this.payments.filter(p => p.id !== paymentId);
                this.filterPayments();
                window.uiManager?.showToast('تم حذف المدفوعة بنجاح', 'success');
            }
        });
    }

    /**
     * Format date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.PaymentManagement = PaymentManagement;
console.log('💳 PaymentManagement class loaded and made globally available');

// Export for use in other modules
export default PaymentManagement;
