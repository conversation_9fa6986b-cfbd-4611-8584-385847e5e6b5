<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الماهر - إدارة العملاء</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <style>
        /* Quick test styles */
        .test-container {
            padding: var(--space-lg);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: var(--space-2xl);
            padding: var(--space-xl);
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-neumorphic-card);
        }
        
        .test-header h1 {
            color: var(--color-primary);
            margin-bottom: var(--space-md);
        }
        
        .test-actions {
            display: flex;
            gap: var(--space-md);
            justify-content: center;
            margin-bottom: var(--space-xl);
        }
        
        .test-content {
            min-height: 600px;
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-neumorphic-card);
            padding: var(--space-lg);
        }
    </style>
</head>
<body class="theme-light">
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار نظام الماهر للصيانة والخدمات</h1>
            <p>اختبار صفحة إدارة العملاء</p>
        </div>
        
        <div class="test-actions">
            <button class="btn btn-primary" id="load-customers">
                👥 تحميل إدارة العملاء
            </button>
            <button class="btn btn-secondary" id="toggle-theme">
                🌙 تبديل المظهر
            </button>
            <button class="btn btn-secondary" id="clear-data">
                🗑️ مسح البيانات
            </button>
        </div>
        
        <div class="test-content" id="test-content">
            <div style="text-align: center; padding: var(--space-3xl);">
                <h3>مرحباً بك في نظام الماهر</h3>
                <p>اضغط على "تحميل إدارة العملاء" لبدء الاختبار</p>
            </div>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script type="module">
        import { EventBus } from './js/core/EventBus.js';
        import { DataManager } from './js/core/DataManager.js';
        import { ThemeManager } from './js/core/ThemeManager.js';
        import { UIManager } from './js/core/UIManager.js';
        import CustomerManagement from './js/components/CustomerManagement.js';

        // Initialize core systems
        const eventBus = new EventBus();
        const dataManager = new DataManager();
        const themeManager = new ThemeManager();
        const uiManager = new UIManager();

        // Make globally available
        window.dataManager = dataManager;
        window.themeManager = themeManager;
        window.uiManager = uiManager;

        // Initialize sample data
        async function initSampleData() {
            const customers = await dataManager.query('customers');
            
            if (customers.length === 0) {
                console.log('📝 Initializing sample data...');
                
                const sampleCustomers = [
                    {
                        name: 'أحمد محمد العلي',
                        type: 'individual',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        city: 'الرياض',
                        district: 'الملز',
                        address: 'شارع الملك فهد، حي الملز',
                        status: 'active',
                        creditLimit: 5000,
                        notes: 'عميل مميز، يفضل التواصل عبر الواتساب'
                    },
                    {
                        name: 'فاطمة سعد الغامدي',
                        type: 'individual',
                        phone: '0509876543',
                        phone2: '0112345678',
                        email: '<EMAIL>',
                        city: 'جدة',
                        district: 'الروضة',
                        address: 'طريق الملك عبدالعزيز، حي الروضة',
                        status: 'active',
                        creditLimit: 3000
                    },
                    {
                        name: 'شركة التقنية المتقدمة',
                        type: 'company',
                        company: 'شركة التقنية المتقدمة للحلول الرقمية',
                        taxId: '300123456700003',
                        phone: '0114567890',
                        email: '<EMAIL>',
                        website: 'https://advanced-tech.com',
                        city: 'الرياض',
                        district: 'العليا',
                        address: 'برج الفيصلية، الدور 15',
                        status: 'active',
                        creditLimit: 50000,
                        notes: 'شركة كبيرة، تحتاج فواتير ضريبية'
                    },
                    {
                        name: 'خالد عبدالله النجار',
                        type: 'vip',
                        phone: '0505555555',
                        email: '<EMAIL>',
                        city: 'مكة المكرمة',
                        district: 'العزيزية',
                        address: 'شارع إبراهيم الخليل، العزيزية',
                        status: 'active',
                        creditLimit: 10000,
                        notes: 'عميل VIP، أولوية في الخدمة'
                    },
                    {
                        name: 'مؤسسة البناء الحديث',
                        type: 'company',
                        company: 'مؤسسة البناء الحديث للمقاولات',
                        taxId: '300987654300003',
                        phone: '0126789012',
                        email: '<EMAIL>',
                        city: 'الدمام',
                        district: 'الفيصلية',
                        address: 'شارع الأمير محمد بن فهد',
                        status: 'active',
                        creditLimit: 100000
                    }
                ];

                for (const customer of sampleCustomers) {
                    await dataManager.create('customers', customer);
                }
                
                console.log('✅ Sample data initialized');
                uiManager.showToast('تم تحميل البيانات التجريبية', 'success');
            }
        }

        // Event listeners
        document.getElementById('load-customers').addEventListener('click', async () => {
            try {
                await initSampleData();
                
                const customerManagement = new CustomerManagement();
                const content = await customerManagement.render();
                await customerManagement.init();
                
                const testContent = document.getElementById('test-content');
                testContent.innerHTML = '';
                testContent.appendChild(content);
                
                uiManager.showToast('تم تحميل صفحة إدارة العملاء', 'success');
            } catch (error) {
                console.error('Error loading customer management:', error);
                uiManager.showToast('خطأ في تحميل صفحة إدارة العملاء', 'error');
            }
        });

        document.getElementById('toggle-theme').addEventListener('click', () => {
            themeManager.toggleTheme();
        });

        document.getElementById('clear-data').addEventListener('click', async () => {
            const confirmed = await uiManager.showConfirmation({
                title: 'تأكيد مسح البيانات',
                message: 'هل أنت متأكد من مسح جميع البيانات؟',
                confirmText: 'مسح',
                cancelText: 'إلغاء',
                confirmClass: 'btn-error'
            });

            if (confirmed) {
                localStorage.clear();
                location.reload();
            }
        });

        console.log('🚀 Test environment ready!');
    </script>
</body>
</html>
