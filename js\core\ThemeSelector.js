/**
 * Theme Selector Manager
 * إدارة اختيار الثيمات المتعددة
 */
class ThemeSelector {
    constructor() {
        this.currentTheme = 'light';
        this.themes = {
            light: {
                name: 'فاتح',
                icon: '☀️',
                description: 'المظهر الفاتح الكلاسيكي'
            },
            dark: {
                name: 'داكن',
                icon: '🌙',
                description: 'المظهر الداكن المريح للعين'
            },
            blue: {
                name: 'أزرق',
                icon: '💙',
                description: 'مظهر أزرق احترافي'
            },
            green: {
                name: 'أخضر',
                icon: '💚',
                description: 'مظهر أخضر منعش'
            },
            purple: {
                name: 'بنفسجي',
                icon: '💜',
                description: 'مظهر بنفسجي أنيق'
            },
            orange: {
                name: 'برتقالي',
                icon: '🧡',
                description: 'مظهر برتقالي دافئ'
            },
            teal: {
                name: 'تركوازي',
                icon: '💎',
                description: 'مظهر تركوازي هادئ'
            }
        };
        
        this.selectorBtn = null;
        this.dropdown = null;
        this.isOpen = false;
    }

    /**
     * Initialize theme selector
     */
    init() {
        console.log('🎨 Initializing Theme Selector...');
        
        this.loadSavedTheme();
        this.setupElements();
        this.attachEventListeners();
        this.updateActiveTheme();
        
        console.log('✅ Theme Selector initialized');
    }

    /**
     * Load saved theme from localStorage
     */
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('selectedTheme');
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
            this.applyTheme(savedTheme);
        }
    }

    /**
     * Setup DOM elements
     */
    setupElements() {
        this.selectorBtn = document.getElementById('theme-selector-btn');
        this.dropdown = document.getElementById('theme-dropdown');

        if (!this.selectorBtn || !this.dropdown) {
            console.warn('Theme selector elements not found');
            // Try to create them if they don't exist
            this.createThemeSelector();
            return;
        }
    }

    /**
     * Create theme selector if it doesn't exist
     */
    createThemeSelector() {
        const headerActions = document.querySelector('.header-actions');
        if (!headerActions) return;

        // Create theme selector HTML
        const themeSelectorHTML = `
            <div class="theme-selector">
                <button id="theme-selector-btn" class="theme-selector-btn" aria-label="اختيار الثيم">
                    <span class="theme-icon">🎨</span>
                </button>
                <div id="theme-dropdown" class="theme-dropdown">
                    <div class="theme-option" data-theme="light">
                        <div class="theme-preview light"></div>
                        <span>فاتح</span>
                    </div>
                    <div class="theme-option" data-theme="dark">
                        <div class="theme-preview dark"></div>
                        <span>داكن</span>
                    </div>
                    <div class="theme-option" data-theme="blue">
                        <div class="theme-preview blue"></div>
                        <span>أزرق</span>
                    </div>
                    <div class="theme-option" data-theme="green">
                        <div class="theme-preview green"></div>
                        <span>أخضر</span>
                    </div>
                    <div class="theme-option" data-theme="purple">
                        <div class="theme-preview purple"></div>
                        <span>بنفسجي</span>
                    </div>
                    <div class="theme-option" data-theme="orange">
                        <div class="theme-preview orange"></div>
                        <span>برتقالي</span>
                    </div>
                    <div class="theme-option" data-theme="teal">
                        <div class="theme-preview teal"></div>
                        <span>تركوازي</span>
                    </div>
                </div>
            </div>
        `;

        // Insert before sidebar toggle
        const sidebarToggle = headerActions.querySelector('#sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.insertAdjacentHTML('beforebegin', themeSelectorHTML);
        } else {
            headerActions.insertAdjacentHTML('beforeend', themeSelectorHTML);
        }

        // Re-setup elements
        this.selectorBtn = document.getElementById('theme-selector-btn');
        this.dropdown = document.getElementById('theme-dropdown');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        if (!this.selectorBtn || !this.dropdown) return;

        // Toggle dropdown
        this.selectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });

        // Theme option clicks
        this.dropdown.addEventListener('click', (e) => {
            const themeOption = e.target.closest('.theme-option');
            if (themeOption) {
                const theme = themeOption.dataset.theme;
                this.selectTheme(theme);
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.theme-selector')) {
                this.closeDropdown();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeDropdown();
            }
        });
    }

    /**
     * Toggle dropdown visibility
     */
    toggleDropdown() {
        if (this.isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    /**
     * Open dropdown
     */
    openDropdown() {
        if (!this.dropdown) return;
        
        this.dropdown.classList.add('show');
        this.selectorBtn.parentElement.classList.add('active');
        this.isOpen = true;
        
        // Focus first theme option
        const firstOption = this.dropdown.querySelector('.theme-option');
        if (firstOption) {
            firstOption.focus();
        }
    }

    /**
     * Close dropdown
     */
    closeDropdown() {
        if (!this.dropdown) return;
        
        this.dropdown.classList.remove('show');
        this.selectorBtn.parentElement.classList.remove('active');
        this.isOpen = false;
    }

    /**
     * Select a theme
     */
    selectTheme(theme) {
        if (!this.themes[theme]) {
            console.warn(`Theme "${theme}" not found`);
            return;
        }

        this.currentTheme = theme;
        this.applyTheme(theme);
        this.saveTheme(theme);
        this.updateActiveTheme();
        this.closeDropdown();
        this.showThemeChangeNotification(theme);
        
        // Emit theme change event
        if (window.eventBus) {
            window.eventBus.emit('theme:changed', { theme });
        }
    }

    /**
     * Apply theme to document
     */
    applyTheme(theme) {
        // Add transition class for smooth change
        document.body.classList.add('theme-transitioning');
        
        // Remove all theme classes
        Object.keys(this.themes).forEach(t => {
            document.documentElement.classList.remove(`theme-${t}`);
            document.documentElement.removeAttribute('data-theme');
        });
        
        // Add new theme
        if (theme !== 'light') {
            document.documentElement.setAttribute('data-theme', theme);
            document.documentElement.classList.add(`theme-${theme}`);
        }
        
        // Remove transition class after animation
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);
    }

    /**
     * Save theme to localStorage
     */
    saveTheme(theme) {
        localStorage.setItem('selectedTheme', theme);
    }

    /**
     * Update active theme in dropdown
     */
    updateActiveTheme() {
        if (!this.dropdown) return;
        
        const options = this.dropdown.querySelectorAll('.theme-option');
        options.forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === this.currentTheme) {
                option.classList.add('active');
            }
        });
        
        // Update selector button icon
        const themeData = this.themes[this.currentTheme];
        if (themeData && this.selectorBtn) {
            const icon = this.selectorBtn.querySelector('.theme-icon');
            if (icon) {
                icon.textContent = themeData.icon;
            }
        }
    }

    /**
     * Show theme change notification
     */
    showThemeChangeNotification(theme) {
        const themeData = this.themes[theme];
        if (!themeData) return;

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'theme-change-notification';
        notification.innerHTML = `
            <div class="theme-icon-large">${themeData.icon}</div>
            <h3>تم تغيير المظهر</h3>
            <p>${themeData.description}</p>
        `;

        // Add to document
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Hide and remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get available themes
     */
    getAvailableThemes() {
        return this.themes;
    }

    /**
     * Set theme programmatically
     */
    setTheme(theme) {
        this.selectTheme(theme);
    }

    /**
     * Get theme data
     */
    getThemeData(theme) {
        return this.themes[theme] || null;
    }

    /**
     * Check if theme exists
     */
    hasTheme(theme) {
        return !!this.themes[theme];
    }

    /**
     * Add custom theme
     */
    addTheme(key, themeData) {
        this.themes[key] = themeData;
    }

    /**
     * Remove theme
     */
    removeTheme(key) {
        if (key === 'light' || key === 'dark') {
            console.warn('Cannot remove default themes');
            return;
        }
        
        delete this.themes[key];
        
        // Switch to light theme if current theme was removed
        if (this.currentTheme === key) {
            this.selectTheme('light');
        }
    }

    /**
     * Reset to default theme
     */
    resetTheme() {
        this.selectTheme('light');
    }

    /**
     * Cleanup
     */
    destroy() {
        // Remove event listeners if needed
        this.closeDropdown();
    }
}

// Export for use in other modules
window.ThemeSelector = ThemeSelector;
