{"name": "almaher-erp-system", "version": "1.0.0", "description": "نظام الماهر للصيانة والخدمات ERP - نظام إدارة موارد المؤسسة", "main": "index.html", "scripts": {"start": "npx serve . -p 8080 -s", "dev": "npx serve . -p 3000 -s", "serve": "npx serve . -s", "build": "echo 'No build process needed for static files'", "test": "echo 'No tests specified'", "generate-pdf": "node generate-pdf.js"}, "keywords": ["ERP", "الماهر", "صيانة", "خدمات", "إدارة", "موا<PERSON>د", "مؤسسة", "JavaScript", "SPA"], "author": "الماهر للصيانة والخدمات", "license": "MIT", "devDependencies": {"puppeteer": "^21.0.0", "serve": "^14.2.1"}, "dependencies": {}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "local"}, "homepage": "http://localhost:8080", "browserslist": ["> 1%", "last 2 versions", "not dead"]}