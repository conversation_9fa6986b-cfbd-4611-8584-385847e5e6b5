@echo off
chcp 65001 >nul
title نظام الماهر للصيانة والخدمات ERP

echo.
echo ========================================
echo    نظام الماهر للصيانة والخدمات ERP
echo ========================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM محاولة تشغيل خادم Node.js
echo [1] محاولة تشغيل خادم Node.js...
node simple-server.js
if %errorlevel% equ 0 (
    echo ✅ تم تشغيل الخادم بنجاح!
    echo 🌐 افتح المتصفح على: http://localhost:8080
    goto :end
)

echo ❌ فشل تشغيل Node.js
echo.

REM محاولة تشغيل خادم Python
echo [2] محاولة تشغيل خادم Python...
python -m http.server 8080
if %errorlevel% equ 0 (
    echo ✅ تم تشغيل خادم Python بنجاح!
    echo 🌐 افتح المتصفح على: http://localhost:8080
    goto :end
)

echo ❌ فشل تشغيل Python
echo.

REM فتح الملف مباشرة
echo [3] فتح النظام مباشرة في المتصفح...
start "" "%~dp0index.html"
if %errorlevel% equ 0 (
    echo ✅ تم فتح النظام في المتصفح!
    goto :end
)

echo ❌ فشل فتح النظام
echo.
echo 💡 يمكنك فتح الملف يدوياً:
echo    %~dp0index.html
echo.

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
