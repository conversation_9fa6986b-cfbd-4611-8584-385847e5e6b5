<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الماهر للصيانة والخدمات - نظام إدارة الخدمات والمبيعات</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Meta tags for SEO and PWA -->
    <meta name="description" content="نظام إدارة شامل للخدمات والمبيعات - الماهر للصيانة والخدمات">
    <meta name="keywords" content="إدارة الخدمات, نقاط البيع, المخزون, الفواتير, العملاء">
    <meta name="author" content="الماهر للصيانة والخدمات">
    
    <!-- PWA Meta -->
    <meta name="theme-color" content="#2196F3">
    <link rel="manifest" href="manifest.json">
    
    <!-- iOS Meta -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="الماهر ERP">
</head>
<body class="theme-light" data-loading="true">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="company-logo">
                <h1>الماهر للصيانة والخدمات</h1>
            </div>
            <div class="loading-spinner"></div>
            <p>جاري تحميل النظام...</p>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <!-- Menu Toggle -->
                <button id="menu-toggle" class="menu-toggle" aria-label="فتح القائمة">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                
                <!-- Company Branding -->
                <div class="company-brand">
                    <h1>الماهر للصيانة والخدمات</h1>
                    <span class="company-subtitle">نظام إدارة الخدمات والمبيعات</span>
                </div>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="theme-toggle" aria-label="تبديل المظهر">
                        <span class="theme-icon light-icon">☀️</span>
                        <span class="theme-icon dark-icon">🌙</span>
                    </button>
                    
                    <!-- Notifications -->
                    <button class="notification-btn" aria-label="الإشعارات">
                        <span class="notification-icon">🔔</span>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="user-menu">
                        <button class="user-avatar" aria-label="قائمة المستخدم">
                            <span class="avatar-text">م</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-content">
                <!-- Navigation will be dynamically generated -->
            </div>
        </nav>

        <!-- Main Content Area -->
        <main id="main-content" class="main-content">
            <div class="content-wrapper">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <ol class="breadcrumb-list">
                        <li><a href="#home">الرئيسية</a></li>
                    </ol>
                </nav>
                
                <!-- Page Content -->
                <div id="page-content" class="page-content">
                    <!-- Content will be dynamically loaded here -->
                </div>
            </div>
        </main>

        <!-- Modal Container -->
        <div id="modal-container" class="modal-container"></div>
        
        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Core JavaScript Modules -->
    <script type="module" src="js/core/EventBus.js"></script>
    <script type="module" src="js/core/DataManager.js"></script>
    <script type="module" src="js/core/Router.js"></script>
    <script type="module" src="js/core/ThemeManager.js"></script>
    <script type="module" src="js/core/UIManager.js"></script>
    <script type="module" src="js/app.js"></script>
</body>
</html>
