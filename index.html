<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الماهر للصيانة والخدمات - نظام إدارة الخدمات والمبيعات</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/scroll-fix.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/theme-selector.css">
    <link rel="stylesheet" href="assets/css/custom-icons.css">
    <link rel="stylesheet" href="assets/css/work-orders.css">
    <link rel="stylesheet" href="assets/css/contacts.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    <link rel="stylesheet" href="assets/css/rtl-fix.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Meta tags for SEO and PWA -->
    <meta name="description" content="نظام إدارة شامل للخدمات والمبيعات - الماهر للصيانة والخدمات">
    <meta name="keywords" content="إدارة الخدمات, نقاط البيع, المخزون, الفواتير, العملاء">
    <meta name="author" content="الماهر للصيانة والخدمات">
    
    <!-- PWA Meta -->
    <meta name="theme-color" content="#2196F3">
    <link rel="manifest" href="manifest.json">
    
    <!-- iOS Meta -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="الماهر ERP">
</head>
<body class="theme-light" data-loading="true">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="company-logo">
                <h1>الماهر للصيانة والخدمات</h1>
            </div>
            <div class="loading-spinner"></div>
            <p>جاري تحميل النظام...</p>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <!-- Menu Toggle -->
                <button id="menu-toggle" class="menu-toggle" aria-label="فتح القائمة">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                
                <!-- Company Branding -->
                <div class="company-brand">
                    <h1>الماهر للصيانة والخدمات</h1>
                    <span class="company-subtitle">نظام إدارة الخدمات والمبيعات</span>
                </div>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button id="theme-selector-btn" class="theme-selector-btn" aria-label="اختيار الثيم">
                            <span class="theme-icon">🎨</span>
                        </button>
                        <div id="theme-dropdown" class="theme-dropdown">
                            <div class="theme-option" data-theme="light">
                                <div class="theme-preview light"></div>
                                <span>فاتح</span>
                            </div>
                            <div class="theme-option" data-theme="dark">
                                <div class="theme-preview dark"></div>
                                <span>داكن</span>
                            </div>
                            <div class="theme-option" data-theme="blue">
                                <div class="theme-preview blue"></div>
                                <span>أزرق</span>
                            </div>
                            <div class="theme-option" data-theme="green">
                                <div class="theme-preview green"></div>
                                <span>أخضر</span>
                            </div>
                            <div class="theme-option" data-theme="purple">
                                <div class="theme-preview purple"></div>
                                <span>بنفسجي</span>
                            </div>
                            <div class="theme-option" data-theme="orange">
                                <div class="theme-preview orange"></div>
                                <span>برتقالي</span>
                            </div>
                            <div class="theme-option" data-theme="teal">
                                <div class="theme-preview teal"></div>
                                <span>تركوازي</span>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <button class="notification-btn" aria-label="الإشعارات">
                        <span class="notification-icon">🔔</span>
                        <span class="notification-badge">3</span>
                    </button>

                    <!-- User Menu -->
                    <div class="user-menu">
                        <button class="user-avatar" aria-label="قائمة المستخدم">
                            <span class="avatar-text">م</span>
                        </button>
                    </div>

                    <!-- Sidebar Toggle -->
                    <button id="sidebar-toggle" class="sidebar-toggle" aria-label="طي القائمة">
                        <span>☰</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-content">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <div class="logo-icon">🔧</div>
                        <h1>الماهر</h1>
                    </div>
                </div>

                <!-- Navigation will be dynamically generated -->
            </div>
        </nav>

        <!-- Main Content Area -->
        <main id="main-content" class="main-content">
            <div class="content-wrapper">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <ol class="breadcrumb-list">
                        <li><a href="#home">الرئيسية</a></li>
                    </ol>
                </nav>
                
                <!-- Page Content -->
                <div id="page-content" class="page-content">
                    <!-- Content will be dynamically loaded here -->
                </div>
            </div>
        </main>

        <!-- Modal Container -->
        <div id="modal-container" class="modal-container"></div>
        
        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- JavaScript Components -->
    <script type="module" src="js/components/DashboardManagement.js"></script>
    <script type="module" src="js/components/WorkOrderManagement.js"></script>
    <script type="module" src="js/components/CustomerManagement.js"></script>
    <script type="module" src="js/components/SupplierManagement.js"></script>
    <script type="module" src="js/components/ContactManagement.js"></script>
    <script type="module" src="js/components/ProductManagement.js"></script>
    <script type="module" src="js/components/InvoiceManagement.js"></script>
    <script type="module" src="js/components/QuotationManagement.js"></script>
    <script type="module" src="js/components/PurchaseManagement.js"></script>
    <script type="module" src="js/components/PaymentManagement.js"></script>
    <script type="module" src="js/components/ReportManagement.js"></script>
    <script type="module" src="js/components/SettingsManagement.js"></script>

    <!-- Scroll Fix -->
    <script src="js/scroll-fix.js"></script>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Core JavaScript Modules -->
    <script type="module" src="js/core/EventBus.js"></script>
    <script type="module" src="js/core/DataManager.js"></script>
    <script type="module" src="js/core/Router.js"></script>
    <script type="module" src="js/core/ThemeManager.js"></script>
    <script type="module" src="js/core/ThemeSelector.js"></script>
    <script type="module" src="js/core/ChartManager.js"></script>
    <script type="module" src="js/core/ExportManager.js"></script>
    <script type="module" src="js/core/PrintManager.js"></script>
    <script type="module" src="js/core/NotificationManager.js"></script>
    <script type="module" src="js/core/UIManager.js"></script>
    <script type="module" src="js/app.js"></script>

    <!-- Theme initialization script -->
    <script>
        // Apply saved theme immediately to prevent flash
        (function() {
            const savedTheme = localStorage.getItem('almaher_theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            document.documentElement.classList.add(`theme-${savedTheme}`);
            console.log(`🎨 Initial theme applied: ${savedTheme}`);
        })();
    </script>
</body>
</html>
