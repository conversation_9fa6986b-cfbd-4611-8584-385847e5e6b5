<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الماهر - إدارة العملاء</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS Variables */
        :root {
            --color-primary: #2196F3;
            --color-primary-dark: #1976D2;
            --color-primary-light: #BBDEFB;
            --color-secondary: #FF9800;
            --color-success: #10B981;
            --color-warning: #F59E0B;
            --color-error: #EF4444;
            --color-info: #3B82F6;
            
            --bg-primary: #F5F7FA;
            --bg-secondary: #FFFFFF;
            --bg-tertiary: #FAFBFC;
            --bg-card: #FFFFFF;
            --bg-input: #FFFFFF;
            --bg-hover: #F8F9FA;
            
            --text-primary: #2C3E50;
            --text-secondary: #5A6C7D;
            --text-muted: #A0AEC0;
            --text-inverse: #FFFFFF;
            
            --border-light: #E2E8F0;
            --border-medium: #CBD5E0;
            
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --space-3xl: 4rem;
            
            --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            --border-radius-sm: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-full: 9999px;
            
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
        }

        /* Dark theme */
        .theme-dark {
            --bg-primary: #1A202C;
            --bg-secondary: #2D3748;
            --bg-tertiary: #4A5568;
            --bg-card: #2D3748;
            --bg-input: #4A5568;
            --bg-hover: #4A5568;
            
            --text-primary: #F7FAFC;
            --text-secondary: #E2E8F0;
            --text-muted: #A0AEC0;
            --text-inverse: #1A202C;
            
            --border-light: #4A5568;
            --border-medium: #718096;
        }

        /* Base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-family-primary);
            font-size: var(--font-size-base);
            color: var(--text-primary);
            background-color: var(--bg-primary);
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            transition: all var(--transition-normal);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-xs);
            padding: var(--space-sm) var(--space-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-family: inherit;
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-normal);
            min-height: 2.5rem;
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: var(--text-inverse);
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-medium);
        }

        .btn-secondary:hover {
            background-color: var(--bg-hover);
            transform: translateY(-1px);
        }

        .btn-success {
            background-color: var(--color-success);
            color: var(--text-inverse);
        }

        .btn-error {
            background-color: var(--color-error);
            color: var(--text-inverse);
        }

        .btn-sm {
            padding: var(--space-xs) var(--space-md);
            font-size: var(--font-size-sm);
            min-height: 2rem;
        }

        /* Form controls */
        .form-control {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-medium);
            border-radius: var(--border-radius-md);
            background-color: var(--bg-input);
            color: var(--text-primary);
            font-family: inherit;
            font-size: var(--font-size-base);
            transition: var(--transition-normal);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-xs);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
        }

        /* Cards */
        .card {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        /* Table */
        .table-container {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: var(--space-md);
            text-align: right;
            border-bottom: 1px solid var(--border-light);
        }

        .table th {
            background-color: var(--bg-tertiary);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .table tbody tr:hover {
            background-color: var(--bg-hover);
        }

        /* Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--border-radius-full);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }

        .badge-primary {
            background-color: var(--color-primary-light);
            color: var(--color-primary-dark);
        }

        .badge-success {
            background-color: #D1FAE5;
            color: var(--color-success);
        }

        .badge-warning {
            background-color: #FEF3C7;
            color: var(--color-warning);
        }

        .badge-info {
            background-color: #DBEAFE;
            color: var(--color-info);
        }

        /* Test specific styles */
        .test-container {
            padding: var(--space-lg);
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: var(--space-2xl);
            padding: var(--space-xl);
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
        }

        .test-header h1 {
            color: var(--color-primary);
            margin-bottom: var(--space-md);
            font-size: var(--font-size-3xl);
        }

        .test-actions {
            display: flex;
            gap: var(--space-md);
            justify-content: center;
            margin-bottom: var(--space-xl);
            flex-wrap: wrap;
        }

        .test-content {
            min-height: 600px;
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--space-lg);
        }

        /* Customer management styles */
        .customers-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
            padding: var(--space-lg);
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            flex-wrap: wrap;
        }

        .search-section {
            flex: 1;
            max-width: 400px;
            min-width: 250px;
        }

        .filter-section {
            display: flex;
            gap: var(--space-md);
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-section .form-control {
            min-width: 150px;
        }

        .customers-list {
            display: grid;
            gap: var(--space-md);
        }

        .customer-card {
            padding: var(--space-lg);
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius-md);
            background-color: var(--bg-card);
            transition: var(--transition-normal);
        }

        .customer-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .customer-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-md);
        }

        .customer-name {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .customer-type {
            margin-bottom: var(--space-sm);
        }

        .customer-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .customer-detail {
            display: flex;
            flex-direction: column;
            gap: var(--space-xs);
        }

        .customer-detail-label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }

        .customer-detail-value {
            color: var(--text-primary);
        }

        .customer-actions {
            display: flex;
            gap: var(--space-sm);
            justify-content: flex-end;
        }

        .empty-state {
            text-align: center;
            padding: var(--space-3xl);
            color: var(--text-muted);
        }

        .empty-state h3 {
            margin-bottom: var(--space-md);
            color: var(--text-secondary);
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: var(--bg-card);
            border-radius: var(--border-radius-lg);
            padding: var(--space-xl);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--border-light);
        }

        .modal-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .modal-footer {
            margin-top: var(--space-lg);
            padding-top: var(--space-md);
            border-top: 1px solid var(--border-light);
            display: flex;
            gap: var(--space-md);
            justify-content: flex-end;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .customers-toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-section {
                max-width: none;
            }

            .filter-section {
                justify-content: center;
            }

            .test-actions {
                flex-direction: column;
                align-items: center;
            }

            .customer-details {
                grid-template-columns: 1fr;
            }

            .customer-actions {
                justify-content: center;
            }
        }

        /* Utility classes */
        .hidden { display: none !important; }
        .text-center { text-align: center; }
        .text-muted { color: var(--text-muted); }
        .mb-0 { margin-bottom: 0; }
        .mt-lg { margin-top: var(--space-lg); }
    </style>
</head>
<body class="theme-light">
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار نظام الماهر للصيانة والخدمات</h1>
            <p>اختبار صفحة إدارة العملاء</p>
        </div>
        
        <div class="test-actions">
            <button class="btn btn-primary" id="load-customers">
                👥 تحميل إدارة العملاء
            </button>
            <button class="btn btn-secondary" id="toggle-theme">
                🌙 تبديل المظهر
            </button>
            <button class="btn btn-secondary" id="add-customer">
                ➕ إضافة عميل
            </button>
            <button class="btn btn-secondary" id="clear-data">
                🗑️ مسح البيانات
            </button>
        </div>
        
        <div class="test-content" id="test-content">
            <div style="text-align: center; padding: var(--space-3xl);">
                <h3>مرحباً بك في نظام الماهر</h3>
                <p>اضغط على "تحميل إدارة العملاء" لبدء الاختبار</p>
            </div>
        </div>
    </div>

    <!-- Customer Modal -->
    <div id="customer-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">إضافة عميل جديد</h3>
            </div>
            <form id="customer-form">
                <div class="form-group">
                    <label class="form-label" for="customer-name">الاسم الكامل *</label>
                    <input type="text" id="customer-name" name="name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customer-type">نوع العميل *</label>
                    <select id="customer-type" name="type" class="form-control" required>
                        <option value="">اختر نوع العميل</option>
                        <option value="individual">فرد</option>
                        <option value="company">شركة</option>
                        <option value="vip">عميل مميز</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customer-phone">رقم الهاتف *</label>
                    <input type="tel" id="customer-phone" name="phone" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customer-email">البريد الإلكتروني</label>
                    <input type="email" id="customer-email" name="email" class="form-control">
                </div>

                <div class="form-group">
                    <label class="form-label" for="customer-city">المدينة</label>
                    <select id="customer-city" name="city" class="form-control">
                        <option value="">اختر المدينة</option>
                        <option value="الرياض">الرياض</option>
                        <option value="جدة">جدة</option>
                        <option value="مكة المكرمة">مكة المكرمة</option>
                        <option value="المدينة المنورة">المدينة المنورة</option>
                        <option value="الدمام">الدمام</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customer-address">العنوان</label>
                    <textarea id="customer-address" name="address" class="form-control" rows="3"></textarea>
                </div>

                <input type="hidden" id="customer-id" name="id">
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancel-btn">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-btn">حفظ</button>
            </div>
        </div>
    </div>

    <script>
        // Simple Customer Management System
        class SimpleCustomerManager {
            constructor() {
                this.customers = [];
                this.filteredCustomers = [];
                this.searchTerm = '';
                this.filterType = 'all';
                this.currentEditId = null;
                this.init();
            }

            init() {
                this.loadCustomers();
                this.setupEventListeners();
                console.log('🚀 Simple Customer Manager initialized');
            }

            // Load customers from localStorage
            loadCustomers() {
                try {
                    const stored = localStorage.getItem('almaher_customers');
                    this.customers = stored ? JSON.parse(stored) : [];
                    this.applyFilters();
                } catch (error) {
                    console.error('Error loading customers:', error);
                    this.customers = [];
                    this.filteredCustomers = [];
                }
            }

            // Save customers to localStorage
            saveCustomers() {
                try {
                    localStorage.setItem('almaher_customers', JSON.stringify(this.customers));
                } catch (error) {
                    console.error('Error saving customers:', error);
                    this.showMessage('خطأ في حفظ البيانات', 'error');
                }
            }

            // Generate unique ID
            generateId() {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            }

            // Apply search and filters
            applyFilters() {
                let filtered = [...this.customers];

                // Apply search
                if (this.searchTerm) {
                    const searchLower = this.searchTerm.toLowerCase();
                    filtered = filtered.filter(customer =>
                        customer.name?.toLowerCase().includes(searchLower) ||
                        customer.phone?.includes(this.searchTerm) ||
                        customer.email?.toLowerCase().includes(searchLower) ||
                        customer.city?.toLowerCase().includes(searchLower)
                    );
                }

                // Apply type filter
                if (this.filterType !== 'all') {
                    filtered = filtered.filter(customer => customer.type === this.filterType);
                }

                this.filteredCustomers = filtered;
            }

            // Setup event listeners
            setupEventListeners() {
                // Load customers button
                document.getElementById('load-customers').addEventListener('click', () => {
                    this.initSampleData();
                    this.renderCustomerManagement();
                });

                // Toggle theme
                document.getElementById('toggle-theme').addEventListener('click', () => {
                    this.toggleTheme();
                });

                // Add customer button
                document.getElementById('add-customer').addEventListener('click', () => {
                    this.openModal();
                });

                // Clear data
                document.getElementById('clear-data').addEventListener('click', () => {
                    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                        localStorage.clear();
                        location.reload();
                    }
                });

                // Modal events
                document.getElementById('cancel-btn').addEventListener('click', () => {
                    this.closeModal();
                });

                document.getElementById('save-btn').addEventListener('click', () => {
                    this.saveCustomer();
                });

                // Close modal when clicking outside
                document.getElementById('customer-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'customer-modal') {
                        this.closeModal();
                    }
                });
            }

            // Initialize sample data
            initSampleData() {
                if (this.customers.length === 0) {
                    const sampleCustomers = [
                        {
                            id: this.generateId(),
                            name: 'أحمد محمد العلي',
                            type: 'individual',
                            phone: '0501234567',
                            email: '<EMAIL>',
                            city: 'الرياض',
                            address: 'شارع الملك فهد، حي الملز',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: this.generateId(),
                            name: 'فاطمة سعد الغامدي',
                            type: 'individual',
                            phone: '0509876543',
                            email: '<EMAIL>',
                            city: 'جدة',
                            address: 'طريق الملك عبدالعزيز، حي الروضة',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: this.generateId(),
                            name: 'شركة التقنية المتقدمة',
                            type: 'company',
                            phone: '0114567890',
                            email: '<EMAIL>',
                            city: 'الرياض',
                            address: 'برج الفيصلية، الدور 15',
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: this.generateId(),
                            name: 'خالد عبدالله النجار',
                            type: 'vip',
                            phone: '0505555555',
                            email: '<EMAIL>',
                            city: 'مكة المكرمة',
                            address: 'شارع إبراهيم الخليل، العزيزية',
                            createdAt: new Date().toISOString()
                        }
                    ];

                    this.customers = sampleCustomers;
                    this.saveCustomers();
                    this.applyFilters();
                    this.showMessage('تم تحميل البيانات التجريبية بنجاح', 'success');
                }
            }

            // Toggle theme
            toggleTheme() {
                const body = document.body;
                if (body.classList.contains('theme-light')) {
                    body.classList.remove('theme-light');
                    body.classList.add('theme-dark');
                    this.showMessage('تم التبديل إلى المظهر الداكن', 'success');
                } else {
                    body.classList.remove('theme-dark');
                    body.classList.add('theme-light');
                    this.showMessage('تم التبديل إلى المظهر الفاتح', 'success');
                }
            }

            // Show message
            showMessage(message, type = 'info') {
                // Simple alert for now
                alert(message);
            }

            // Render customer management interface
            renderCustomerManagement() {
                const content = document.getElementById('test-content');
                content.innerHTML = `
                    <div class="customers-toolbar">
                        <div class="search-section">
                            <input type="text"
                                   id="customer-search"
                                   class="form-control"
                                   placeholder="البحث في العملاء..."
                                   value="${this.searchTerm}">
                        </div>
                        <div class="filter-section">
                            <select id="customer-filter" class="form-control">
                                <option value="all">جميع العملاء</option>
                                <option value="individual">أفراد</option>
                                <option value="company">شركات</option>
                                <option value="vip">عملاء مميزين</option>
                            </select>
                            <button class="btn btn-primary" id="add-customer-btn">
                                ➕ إضافة عميل
                            </button>
                        </div>
                    </div>

                    <div id="customers-list">
                        ${this.renderCustomersList()}
                    </div>
                `;

                this.setupCustomerManagementEvents();
            }

            // Setup customer management events
            setupCustomerManagementEvents() {
                // Search functionality
                const searchInput = document.getElementById('customer-search');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.searchTerm = e.target.value;
                        this.applyFilters();
                        this.updateCustomersList();
                    });
                }

                // Filter functionality
                const filterSelect = document.getElementById('customer-filter');
                if (filterSelect) {
                    filterSelect.value = this.filterType;
                    filterSelect.addEventListener('change', (e) => {
                        this.filterType = e.target.value;
                        this.applyFilters();
                        this.updateCustomersList();
                    });
                }

                // Add customer button
                const addBtn = document.getElementById('add-customer-btn');
                if (addBtn) {
                    addBtn.addEventListener('click', () => {
                        this.openModal();
                    });
                }

                // Customer action buttons
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('edit-customer-btn')) {
                        const customerId = e.target.getAttribute('data-id');
                        this.editCustomer(customerId);
                    } else if (e.target.classList.contains('delete-customer-btn')) {
                        const customerId = e.target.getAttribute('data-id');
                        this.deleteCustomer(customerId);
                    } else if (e.target.classList.contains('view-customer-btn')) {
                        const customerId = e.target.getAttribute('data-id');
                        this.viewCustomer(customerId);
                    }
                });
            }

            // Render customers list
            renderCustomersList() {
                if (this.filteredCustomers.length === 0) {
                    return `
                        <div class="empty-state">
                            <h3>لا توجد عملاء</h3>
                            <p>ابدأ بإضافة عميل جديد</p>
                            <button class="btn btn-primary" onclick="customerManager.openModal()">
                                إضافة عميل جديد
                            </button>
                        </div>
                    `;
                }

                return `
                    <div class="customers-list">
                        ${this.filteredCustomers.map(customer => this.renderCustomerCard(customer)).join('')}
                    </div>
                `;
            }

            // Render single customer card
            renderCustomerCard(customer) {
                const typeLabels = {
                    individual: 'فرد',
                    company: 'شركة',
                    vip: 'مميز'
                };

                const typeColors = {
                    individual: 'info',
                    company: 'success',
                    vip: 'warning'
                };

                return `
                    <div class="customer-card">
                        <div class="customer-header">
                            <div>
                                <div class="customer-name">${customer.name || 'غير محدد'}</div>
                                <div class="customer-type">
                                    <span class="badge badge-${typeColors[customer.type] || 'info'}">
                                        ${typeLabels[customer.type] || 'غير محدد'}
                                    </span>
                                </div>
                            </div>
                            <div class="customer-actions">
                                <button class="btn btn-sm btn-secondary view-customer-btn"
                                        data-id="${customer.id}"
                                        title="عرض التفاصيل">
                                    👁️
                                </button>
                                <button class="btn btn-sm btn-primary edit-customer-btn"
                                        data-id="${customer.id}"
                                        title="تعديل">
                                    ✏️
                                </button>
                                <button class="btn btn-sm btn-error delete-customer-btn"
                                        data-id="${customer.id}"
                                        title="حذف">
                                    🗑️
                                </button>
                            </div>
                        </div>

                        <div class="customer-details">
                            <div class="customer-detail">
                                <div class="customer-detail-label">الهاتف</div>
                                <div class="customer-detail-value">
                                    <a href="tel:${customer.phone}">${customer.phone || 'غير محدد'}</a>
                                </div>
                            </div>

                            <div class="customer-detail">
                                <div class="customer-detail-label">البريد الإلكتروني</div>
                                <div class="customer-detail-value">
                                    ${customer.email ? `<a href="mailto:${customer.email}">${customer.email}</a>` : 'غير محدد'}
                                </div>
                            </div>

                            <div class="customer-detail">
                                <div class="customer-detail-label">المدينة</div>
                                <div class="customer-detail-value">${customer.city || 'غير محدد'}</div>
                            </div>

                            <div class="customer-detail">
                                <div class="customer-detail-label">العنوان</div>
                                <div class="customer-detail-value">${customer.address || 'غير محدد'}</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Update customers list
            updateCustomersList() {
                const listContainer = document.getElementById('customers-list');
                if (listContainer) {
                    listContainer.innerHTML = this.renderCustomersList();
                }
            }

            // Open modal for add/edit
            openModal(customer = null) {
                const modal = document.getElementById('customer-modal');
                const modalTitle = document.getElementById('modal-title');
                const form = document.getElementById('customer-form');

                // Reset form
                form.reset();
                this.currentEditId = null;

                if (customer) {
                    // Edit mode
                    modalTitle.textContent = 'تعديل بيانات العميل';
                    this.populateForm(customer);
                    this.currentEditId = customer.id;
                } else {
                    // Add mode
                    modalTitle.textContent = 'إضافة عميل جديد';
                }

                modal.classList.remove('hidden');

                // Focus on first input
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 100);
                }
            }

            // Close modal
            closeModal() {
                const modal = document.getElementById('customer-modal');
                modal.classList.add('hidden');
                this.currentEditId = null;
            }

            // Populate form with customer data
            populateForm(customer) {
                const form = document.getElementById('customer-form');
                Object.keys(customer).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.value = customer[key] || '';
                    }
                });
            }

            // Save customer (add or update)
            saveCustomer() {
                const form = document.getElementById('customer-form');
                const formData = new FormData(form);
                const customerData = {};

                // Convert FormData to object
                for (let [key, value] of formData.entries()) {
                    customerData[key] = value.trim();
                }

                // Validation
                if (!this.validateCustomerData(customerData)) {
                    return;
                }

                if (this.currentEditId) {
                    // Update existing customer
                    const index = this.customers.findIndex(c => c.id === this.currentEditId);
                    if (index !== -1) {
                        this.customers[index] = {
                            ...this.customers[index],
                            ...customerData,
                            updatedAt: new Date().toISOString()
                        };
                        this.showMessage('تم تحديث بيانات العميل بنجاح', 'success');
                    }
                } else {
                    // Add new customer
                    const newCustomer = {
                        id: this.generateId(),
                        ...customerData,
                        createdAt: new Date().toISOString()
                    };
                    this.customers.push(newCustomer);
                    this.showMessage('تم إضافة العميل بنجاح', 'success');
                }

                this.saveCustomers();
                this.applyFilters();
                this.updateCustomersList();
                this.closeModal();
            }

            // Validate customer data
            validateCustomerData(data) {
                const errors = [];

                // Required fields
                if (!data.name) errors.push('الاسم مطلوب');
                if (!data.type) errors.push('نوع العميل مطلوب');
                if (!data.phone) errors.push('رقم الهاتف مطلوب');

                // Phone validation
                if (data.phone && !/^05\d{8}$/.test(data.phone)) {
                    errors.push('رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام');
                }

                // Email validation
                if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                    errors.push('البريد الإلكتروني غير صحيح');
                }

                if (errors.length > 0) {
                    this.showMessage(errors.join('\n'), 'error');
                    return false;
                }

                return true;
            }

            // Edit customer
            editCustomer(customerId) {
                const customer = this.customers.find(c => c.id === customerId);
                if (customer) {
                    this.openModal(customer);
                } else {
                    this.showMessage('العميل غير موجود', 'error');
                }
            }

            // Delete customer
            deleteCustomer(customerId) {
                const customer = this.customers.find(c => c.id === customerId);
                if (!customer) {
                    this.showMessage('العميل غير موجود', 'error');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
                    this.customers = this.customers.filter(c => c.id !== customerId);
                    this.saveCustomers();
                    this.applyFilters();
                    this.updateCustomersList();
                    this.showMessage('تم حذف العميل بنجاح', 'success');
                }
            }

            // View customer details
            viewCustomer(customerId) {
                const customer = this.customers.find(c => c.id === customerId);
                if (!customer) {
                    this.showMessage('العميل غير موجود', 'error');
                    return;
                }

                const typeLabels = {
                    individual: 'فرد',
                    company: 'شركة',
                    vip: 'مميز'
                };

                const details = `
تفاصيل العميل: ${customer.name}

الاسم: ${customer.name || 'غير محدد'}
النوع: ${typeLabels[customer.type] || 'غير محدد'}
الهاتف: ${customer.phone || 'غير محدد'}
البريد الإلكتروني: ${customer.email || 'غير محدد'}
المدينة: ${customer.city || 'غير محدد'}
العنوان: ${customer.address || 'غير محدد'}
تاريخ الإضافة: ${customer.createdAt ? new Date(customer.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
                `;

                alert(details);
            }

            // Export customers to CSV
            exportCustomers() {
                try {
                    const csvData = this.generateCSV();
                    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');

                    if (link.download !== undefined) {
                        const url = URL.createObjectURL(blob);
                        link.setAttribute('href', url);
                        link.setAttribute('download', `customers_${new Date().toISOString().split('T')[0]}.csv`);
                        link.style.visibility = 'hidden';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        this.showMessage('تم تصدير قائمة العملاء بنجاح', 'success');
                    }
                } catch (error) {
                    console.error('Failed to export customers:', error);
                    this.showMessage('خطأ في تصدير قائمة العملاء', 'error');
                }
            }

            // Generate CSV data
            generateCSV() {
                const headers = ['الاسم', 'النوع', 'الهاتف', 'البريد الإلكتروني', 'المدينة', 'العنوان', 'تاريخ الإضافة'];
                const csvRows = [headers.join(',')];

                const typeLabels = {
                    individual: 'فرد',
                    company: 'شركة',
                    vip: 'مميز'
                };

                this.filteredCustomers.forEach(customer => {
                    const row = [
                        this.escapeCsvValue(customer.name || ''),
                        this.escapeCsvValue(typeLabels[customer.type] || ''),
                        this.escapeCsvValue(customer.phone || ''),
                        this.escapeCsvValue(customer.email || ''),
                        this.escapeCsvValue(customer.city || ''),
                        this.escapeCsvValue(customer.address || ''),
                        this.escapeCsvValue(customer.createdAt ? new Date(customer.createdAt).toLocaleDateString('ar-SA') : '')
                    ];
                    csvRows.push(row.join(','));
                });

                return '\uFEFF' + csvRows.join('\n'); // Add BOM for Arabic support
            }

            // Escape CSV value
            escapeCsvValue(value) {
                if (value === null || value === undefined) return '';
                const stringValue = String(value);
                if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
                    return `"${stringValue.replace(/"/g, '""')}"`;
                }
                return stringValue;
            }
        }

        // Initialize the customer manager
        const customerManager = new SimpleCustomerManager();

        // Make it globally available for testing
        window.customerManager = customerManager;
    </script>
</body>
</html>
