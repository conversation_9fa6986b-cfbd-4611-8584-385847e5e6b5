<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف النظام - الماهر للصيانة والخدمات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            color: #007bff;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .back-link {
            display: inline-block;
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .back-link:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <a href="index.html" class="back-link">← العودة للنظام الرئيسي</a>
        
        <h1>🧪 اختبار وظائف نظام الماهر للصيانة والخدمات</h1>
        <p>هذه الصفحة لاختبار جميع الوظائف المضافة حديثاً</p>

        <!-- Theme Testing -->
        <div class="test-section">
            <h2 class="test-title">🎨 اختبار الثيمات (7 ثيمات)</h2>
            <button class="test-button" onclick="testTheme('light')">فاتح</button>
            <button class="test-button" onclick="testTheme('dark')">داكن</button>
            <button class="test-button" onclick="testTheme('blue')">أزرق</button>
            <button class="test-button" onclick="testTheme('green')">أخضر</button>
            <button class="test-button" onclick="testTheme('purple')">بنفسجي</button>
            <button class="test-button" onclick="testTheme('orange')">برتقالي</button>
            <button class="test-button" onclick="testTheme('teal')">تركوازي</button>
            <div id="theme-result" class="test-result"></div>
        </div>

        <!-- Chart Testing -->
        <div class="test-section">
            <h2 class="test-title">📊 اختبار الرسوم البيانية</h2>
            <button class="test-button" onclick="testCharts()">اختبار Chart.js</button>
            <button class="test-button" onclick="createTestChart()">إنشاء رسم بياني تجريبي</button>
            <div id="chart-result" class="test-result"></div>
            <canvas id="test-chart" width="400" height="200" style="margin-top: 10px; display: none;"></canvas>
        </div>

        <!-- Export Testing -->
        <div class="test-section">
            <h2 class="test-title">📤 اختبار التصدير</h2>
            <button class="test-button" onclick="testExportLibraries()">اختبار المكتبات</button>
            <button class="test-button" onclick="testExportExcel()">تصدير Excel</button>
            <button class="test-button" onclick="testExportPDF()">تصدير PDF</button>
            <button class="test-button" onclick="testExportCSV()">تصدير CSV</button>
            <div id="export-result" class="test-result"></div>
        </div>

        <!-- Print Testing -->
        <div class="test-section">
            <h2 class="test-title">🖨️ اختبار الطباعة</h2>
            <button class="test-button" onclick="testPrintManager()">اختبار مدير الطباعة</button>
            <button class="test-button" onclick="testPrintInvoice()">طباعة فاتورة تجريبية</button>
            <div id="print-result" class="test-result"></div>
        </div>

        <!-- Notification Testing -->
        <div class="test-section">
            <h2 class="test-title">🔔 اختبار الإشعارات</h2>
            <button class="test-button" onclick="testNotificationManager()">اختبار مدير الإشعارات</button>
            <button class="test-button" onclick="addTestNotification()">إضافة إشعار تجريبي</button>
            <button class="test-button" onclick="testBrowserNotification()">إشعار المتصفح</button>
            <div id="notification-result" class="test-result"></div>
        </div>

        <!-- Overall System Test -->
        <div class="test-section">
            <h2 class="test-title">🔧 اختبار شامل للنظام</h2>
            <button class="test-button" onclick="runFullSystemTest()">تشغيل اختبار شامل</button>
            <div id="system-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // Test Theme Functionality
        function testTheme(theme) {
            const result = document.getElementById('theme-result');
            try {
                if (window.themeSelector) {
                    window.themeSelector.setTheme(theme);
                    result.className = 'test-result success';
                    result.textContent = `✅ تم تطبيق الثيم "${theme}" بنجاح`;
                } else {
                    result.className = 'test-result error';
                    result.textContent = '❌ مدير الثيمات غير متوفر';
                }
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في تطبيق الثيم: ${error.message}`;
            }
        }

        // Test Chart Functionality
        function testCharts() {
            const result = document.getElementById('chart-result');
            if (window.Chart) {
                result.className = 'test-result success';
                result.textContent = '✅ مكتبة Chart.js محملة بنجاح';
            } else {
                result.className = 'test-result error';
                result.textContent = '❌ مكتبة Chart.js غير محملة';
            }
        }

        function createTestChart() {
            const canvas = document.getElementById('test-chart');
            const result = document.getElementById('chart-result');
            
            if (!window.Chart) {
                result.className = 'test-result error';
                result.textContent = '❌ مكتبة Chart.js غير متوفرة';
                return;
            }

            try {
                canvas.style.display = 'block';
                new Chart(canvas, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل'],
                        datasets: [{
                            label: 'مبيعات تجريبية',
                            data: [12, 19, 3, 5],
                            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                rtl: true
                            }
                        }
                    }
                });
                result.className = 'test-result success';
                result.textContent = '✅ تم إنشاء الرسم البياني التجريبي بنجاح';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في إنشاء الرسم البياني: ${error.message}`;
            }
        }

        // Test Export Functionality
        function testExportLibraries() {
            const result = document.getElementById('export-result');
            const libraries = {
                'XLSX (Excel)': !!window.XLSX,
                'jsPDF (PDF)': !!window.jsPDF,
                'Export Manager': !!window.exportManager
            };

            let message = 'حالة مكتبات التصدير:\n';
            let allLoaded = true;

            for (const [lib, loaded] of Object.entries(libraries)) {
                message += `${loaded ? '✅' : '❌'} ${lib}\n`;
                if (!loaded) allLoaded = false;
            }

            result.className = allLoaded ? 'test-result success' : 'test-result warning';
            result.innerHTML = message.replace(/\n/g, '<br>');
        }

        function testExportExcel() {
            const result = document.getElementById('export-result');
            if (!window.exportManager) {
                result.className = 'test-result error';
                result.textContent = '❌ مدير التصدير غير متوفر';
                return;
            }

            try {
                const testData = [
                    { 'الاسم': 'أحمد محمد', 'المبلغ': '1500 ر.س', 'التاريخ': '2024-01-15' },
                    { 'الاسم': 'فاطمة علي', 'المبلغ': '2300 ر.س', 'التاريخ': '2024-01-16' }
                ];
                window.exportManager.exportToExcel(testData, 'اختبار-تصدير');
                result.className = 'test-result success';
                result.textContent = '✅ تم تصدير ملف Excel تجريبي';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في تصدير Excel: ${error.message}`;
            }
        }

        function testExportPDF() {
            const result = document.getElementById('export-result');
            if (!window.exportManager) {
                result.className = 'test-result error';
                result.textContent = '❌ مدير التصدير غير متوفر';
                return;
            }

            try {
                const testData = [
                    { 'البيان': 'إجمالي المبيعات', 'القيمة': '15,000 ر.س' },
                    { 'البيان': 'عدد الفواتير', 'القيمة': '25' }
                ];
                window.exportManager.exportToPDF(testData, 'اختبار-تقرير', 'تقرير تجريبي');
                result.className = 'test-result success';
                result.textContent = '✅ تم تصدير ملف PDF تجريبي';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في تصدير PDF: ${error.message}`;
            }
        }

        function testExportCSV() {
            const result = document.getElementById('export-result');
            if (!window.exportManager) {
                result.className = 'test-result error';
                result.textContent = '❌ مدير التصدير غير متوفر';
                return;
            }

            try {
                const testData = [
                    { 'العميل': 'شركة التقنية', 'الخدمة': 'صيانة مكيفات', 'المبلغ': '800' },
                    { 'العميل': 'أحمد محمد', 'الخدمة': 'أعمال كهربائية', 'المبلغ': '450' }
                ];
                window.exportManager.exportToCSV(testData, 'اختبار-بيانات');
                result.className = 'test-result success';
                result.textContent = '✅ تم تصدير ملف CSV تجريبي';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في تصدير CSV: ${error.message}`;
            }
        }

        // Test Print Functionality
        function testPrintManager() {
            const result = document.getElementById('print-result');
            if (window.printManager) {
                result.className = 'test-result success';
                result.textContent = '✅ مدير الطباعة متوفر ويعمل';
            } else {
                result.className = 'test-result error';
                result.textContent = '❌ مدير الطباعة غير متوفر';
            }
        }

        function testPrintInvoice() {
            const result = document.getElementById('print-result');
            if (!window.printManager) {
                result.className = 'test-result error';
                result.textContent = '❌ مدير الطباعة غير متوفر';
                return;
            }

            try {
                const testInvoice = {
                    number: 'INV-TEST-001',
                    date: new Date().toLocaleDateString('ar-SA'),
                    customerName: 'عميل تجريبي',
                    customerPhone: '0501234567',
                    items: [
                        { name: 'صيانة مكيف', quantity: 1, price: 200, total: 200 },
                        { name: 'قطع غيار', quantity: 2, price: 50, total: 100 }
                    ],
                    subtotal: 300,
                    tax: 45,
                    total: 345
                };
                window.printManager.printInvoice(testInvoice);
                result.className = 'test-result success';
                result.textContent = '✅ تم فتح نافذة طباعة الفاتورة التجريبية';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في طباعة الفاتورة: ${error.message}`;
            }
        }

        // Test Notification Functionality
        function testNotificationManager() {
            const result = document.getElementById('notification-result');
            if (window.notificationManager) {
                result.className = 'test-result success';
                result.textContent = '✅ مدير الإشعارات متوفر ويعمل';
            } else {
                result.className = 'test-result error';
                result.textContent = '❌ مدير الإشعارات غير متوفر';
            }
        }

        function addTestNotification() {
            const result = document.getElementById('notification-result');
            if (!window.notificationManager) {
                result.className = 'test-result error';
                result.textContent = '❌ مدير الإشعارات غير متوفر';
                return;
            }

            try {
                window.notificationManager.addNotification({
                    title: 'إشعار تجريبي',
                    message: 'هذا إشعار تجريبي لاختبار النظام',
                    type: 'info',
                    persistent: false
                });
                result.className = 'test-result success';
                result.textContent = '✅ تم إضافة إشعار تجريبي بنجاح';
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ خطأ في إضافة الإشعار: ${error.message}`;
            }
        }

        function testBrowserNotification() {
            const result = document.getElementById('notification-result');
            
            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification('اختبار إشعار المتصفح', {
                        body: 'هذا إشعار تجريبي من نظام الماهر',
                        icon: '/favicon.ico'
                    });
                    result.className = 'test-result success';
                    result.textContent = '✅ تم إرسال إشعار المتصفح';
                } else if (Notification.permission !== 'denied') {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            testBrowserNotification();
                        }
                    });
                    result.className = 'test-result warning';
                    result.textContent = '⚠️ يرجى السماح بالإشعارات';
                } else {
                    result.className = 'test-result error';
                    result.textContent = '❌ إشعارات المتصفح مرفوضة';
                }
            } else {
                result.className = 'test-result error';
                result.textContent = '❌ المتصفح لا يدعم الإشعارات';
            }
        }

        // Full System Test
        function runFullSystemTest() {
            const result = document.getElementById('system-result');
            let report = 'تقرير الاختبار الشامل:\n\n';
            let passedTests = 0;
            let totalTests = 0;

            // Test all components
            const tests = [
                { name: 'Theme Selector', check: () => !!window.themeSelector },
                { name: 'Chart Manager', check: () => !!window.chartManager && !!window.Chart },
                { name: 'Export Manager', check: () => !!window.exportManager && !!window.XLSX && !!window.jsPDF },
                { name: 'Print Manager', check: () => !!window.printManager },
                { name: 'Notification Manager', check: () => !!window.notificationManager },
                { name: 'UI Manager', check: () => !!window.uiManager },
                { name: 'Data Manager', check: () => !!window.dataManager },
                { name: 'Router', check: () => !!window.router }
            ];

            tests.forEach(test => {
                totalTests++;
                const passed = test.check();
                if (passed) passedTests++;
                report += `${passed ? '✅' : '❌'} ${test.name}\n`;
            });

            report += `\nالنتيجة: ${passedTests}/${totalTests} اختبار نجح`;
            
            const percentage = (passedTests / totalTests) * 100;
            if (percentage === 100) {
                result.className = 'test-result success';
            } else if (percentage >= 70) {
                result.className = 'test-result warning';
            } else {
                result.className = 'test-result error';
            }

            result.innerHTML = report.replace(/\n/g, '<br>');
        }

        // Auto-run system test on page load
        window.addEventListener('load', () => {
            setTimeout(runFullSystemTest, 1000);
        });
    </script>
</body>
</html>
