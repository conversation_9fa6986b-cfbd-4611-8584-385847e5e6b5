/**
 * Print Manager
 * إدارة طباعة الفواتير والتقارير
 */
class PrintManager {
    constructor() {
        this.printStyles = '';
        this.companyInfo = {
            name: 'الماهر للصيانة والخدمات',
            nameEn: 'Almaher Maintenance & Services',
            address: 'فرع الشرائع، 24263، حي الخضراء، مكة المكرمة',
            phone: '0509888210',
            email: '<EMAIL>',
            website: 'almaher-est.com',
            taxId: '311055560500003'
        };
    }

    /**
     * Initialize print manager
     */
    init() {
        console.log('🖨️ Initializing Print Manager...');
        this.loadPrintStyles();
        console.log('✅ Print Manager initialized');
    }

    /**
     * Load print styles
     */
    loadPrintStyles() {
        this.printStyles = `
            <style>
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }
                    
                    body {
                        font-family: 'Cairo', Arial, sans-serif;
                        font-size: 12px;
                        line-height: 1.4;
                        color: #000;
                        direction: rtl;
                        text-align: right;
                        margin: 0;
                        padding: 20px;
                    }
                    
                    .print-container {
                        max-width: 210mm;
                        margin: 0 auto;
                        background: white;
                    }
                    
                    .company-header {
                        text-align: center;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 15px;
                        margin-bottom: 20px;
                    }
                    
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 5px;
                    }
                    
                    .company-name-en {
                        font-size: 16px;
                        color: #666;
                        margin-bottom: 10px;
                    }
                    
                    .company-details {
                        font-size: 11px;
                        color: #666;
                        line-height: 1.3;
                    }
                    
                    .invoice-header {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 20px;
                        border: 1px solid #ddd;
                        padding: 15px;
                        background: #f8f9fa;
                    }
                    
                    .invoice-title {
                        font-size: 20px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    
                    .invoice-details {
                        font-size: 12px;
                    }
                    
                    .customer-info {
                        border: 1px solid #ddd;
                        padding: 15px;
                        margin-bottom: 20px;
                        background: #f8f9fa;
                    }
                    
                    .customer-title {
                        font-weight: bold;
                        margin-bottom: 10px;
                        color: #007bff;
                    }
                    
                    .items-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    
                    .items-table th,
                    .items-table td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: right;
                    }
                    
                    .items-table th {
                        background: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    
                    .items-table tr:nth-child(even) {
                        background: #f8f9fa;
                    }
                    
                    .totals-section {
                        margin-top: 20px;
                        border: 1px solid #ddd;
                        padding: 15px;
                        background: #f8f9fa;
                    }
                    
                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                    }
                    
                    .total-row.final {
                        font-weight: bold;
                        font-size: 16px;
                        color: #007bff;
                        border-top: 2px solid #007bff;
                        padding-top: 10px;
                        margin-top: 10px;
                    }
                    
                    .notes-section {
                        margin-top: 20px;
                        border: 1px solid #ddd;
                        padding: 15px;
                    }
                    
                    .footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 10px;
                        color: #666;
                        border-top: 1px solid #ddd;
                        padding-top: 15px;
                    }
                    
                    .no-print {
                        display: none !important;
                    }
                }
            </style>
        `;
    }

    /**
     * Print invoice
     */
    printInvoice(invoice) {
        const printContent = this.generateInvoiceHTML(invoice);
        this.openPrintWindow(printContent, `فاتورة-${invoice.number}`);
    }

    /**
     * Generate invoice HTML
     */
    generateInvoiceHTML(invoice) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة رقم ${invoice.number}</title>
                ${this.printStyles}
            </head>
            <body>
                <div class="print-container">
                    <!-- Company Header -->
                    <div class="company-header">
                        <div class="company-name">${this.companyInfo.name}</div>
                        <div class="company-name-en">${this.companyInfo.nameEn}</div>
                        <div class="company-details">
                            <div>${this.companyInfo.address}</div>
                            <div>هاتف: ${this.companyInfo.phone} | البريد: ${this.companyInfo.email}</div>
                            <div>الموقع: ${this.companyInfo.website} | الرقم الضريبي: ${this.companyInfo.taxId}</div>
                        </div>
                    </div>

                    <!-- Invoice Header -->
                    <div class="invoice-header">
                        <div>
                            <div class="invoice-title">فاتورة ضريبية</div>
                            <div class="invoice-details">
                                <div><strong>رقم الفاتورة:</strong> ${invoice.number}</div>
                                <div><strong>التاريخ:</strong> ${invoice.date}</div>
                                <div><strong>تاريخ الاستحقاق:</strong> ${invoice.dueDate || 'فوري'}</div>
                            </div>
                        </div>
                        <div>
                            <div><strong>حالة الفاتورة:</strong> ${this.getStatusText(invoice.status)}</div>
                            <div><strong>طريقة الدفع:</strong> ${invoice.paymentMethod || 'نقداً'}</div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div class="customer-info">
                        <div class="customer-title">بيانات العميل</div>
                        <div><strong>الاسم:</strong> ${invoice.customerName}</div>
                        <div><strong>الهاتف:</strong> ${invoice.customerPhone || ''}</div>
                        <div><strong>العنوان:</strong> ${invoice.customerAddress || ''}</div>
                        ${invoice.customerTaxId ? `<div><strong>الرقم الضريبي:</strong> ${invoice.customerTaxId}</div>` : ''}
                    </div>

                    <!-- Items Table -->
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>الخدمة/المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الخصم</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price.toFixed(2)} ر.س</td>
                                    <td>${item.discount || 0}%</td>
                                    <td>${item.total.toFixed(2)} ر.س</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <!-- Totals -->
                    <div class="totals-section">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span>${invoice.subtotal.toFixed(2)} ر.س</span>
                        </div>
                        ${invoice.discount ? `
                            <div class="total-row">
                                <span>الخصم:</span>
                                <span>${invoice.discount.toFixed(2)} ر.س</span>
                            </div>
                        ` : ''}
                        ${invoice.tax ? `
                            <div class="total-row">
                                <span>ضريبة القيمة المضافة (${invoice.taxRate || 15}%):</span>
                                <span>${invoice.tax.toFixed(2)} ر.س</span>
                            </div>
                        ` : ''}
                        <div class="total-row final">
                            <span>المجموع الإجمالي:</span>
                            <span>${invoice.total.toFixed(2)} ر.س</span>
                        </div>
                    </div>

                    <!-- Notes -->
                    ${invoice.notes ? `
                        <div class="notes-section">
                            <strong>ملاحظات:</strong><br>
                            ${invoice.notes}
                        </div>
                    ` : ''}

                    <!-- Footer -->
                    <div class="footer">
                        <div>شكراً لتعاملكم معنا</div>
                        <div>تم إنشاء هذه الفاتورة بواسطة نظام الماهر للصيانة والخدمات</div>
                        <div>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Print quotation
     */
    printQuotation(quotation) {
        const printContent = this.generateQuotationHTML(quotation);
        this.openPrintWindow(printContent, `عرض-سعر-${quotation.number}`);
    }

    /**
     * Generate quotation HTML
     */
    generateQuotationHTML(quotation) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>عرض سعر رقم ${quotation.number}</title>
                ${this.printStyles}
            </head>
            <body>
                <div class="print-container">
                    <!-- Company Header -->
                    <div class="company-header">
                        <div class="company-name">${this.companyInfo.name}</div>
                        <div class="company-name-en">${this.companyInfo.nameEn}</div>
                        <div class="company-details">
                            <div>${this.companyInfo.address}</div>
                            <div>هاتف: ${this.companyInfo.phone} | البريد: ${this.companyInfo.email}</div>
                            <div>الموقع: ${this.companyInfo.website}</div>
                        </div>
                    </div>

                    <!-- Quotation Header -->
                    <div class="invoice-header">
                        <div>
                            <div class="invoice-title">عرض سعر</div>
                            <div class="invoice-details">
                                <div><strong>رقم العرض:</strong> ${quotation.number}</div>
                                <div><strong>التاريخ:</strong> ${quotation.date}</div>
                                <div><strong>صالح حتى:</strong> ${quotation.validUntil}</div>
                            </div>
                        </div>
                        <div>
                            <div><strong>حالة العرض:</strong> ${this.getStatusText(quotation.status)}</div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div class="customer-info">
                        <div class="customer-title">بيانات العميل</div>
                        <div><strong>الاسم:</strong> ${quotation.customerName}</div>
                        <div><strong>الهاتف:</strong> ${quotation.customerPhone || ''}</div>
                        <div><strong>العنوان:</strong> ${quotation.customerAddress || ''}</div>
                    </div>

                    <!-- Items Table -->
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>الخدمة/المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${quotation.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.price.toFixed(2)} ر.س</td>
                                    <td>${item.total.toFixed(2)} ر.س</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <!-- Totals -->
                    <div class="totals-section">
                        <div class="total-row final">
                            <span>المجموع الإجمالي:</span>
                            <span>${quotation.total.toFixed(2)} ر.س</span>
                        </div>
                    </div>

                    <!-- Notes -->
                    ${quotation.notes ? `
                        <div class="notes-section">
                            <strong>ملاحظات:</strong><br>
                            ${quotation.notes}
                        </div>
                    ` : ''}

                    <!-- Footer -->
                    <div class="footer">
                        <div>نتطلع لخدمتكم</div>
                        <div>هذا العرض صالح حتى التاريخ المحدد أعلاه</div>
                        <div>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Print report
     */
    printReport(reportData, title = 'تقرير') {
        const printContent = this.generateReportHTML(reportData, title);
        this.openPrintWindow(printContent, title);
    }

    /**
     * Generate report HTML
     */
    generateReportHTML(reportData, title) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                ${this.printStyles}
            </head>
            <body>
                <div class="print-container">
                    <!-- Company Header -->
                    <div class="company-header">
                        <div class="company-name">${this.companyInfo.name}</div>
                        <div class="company-details">
                            <div>${this.companyInfo.address}</div>
                            <div>هاتف: ${this.companyInfo.phone}</div>
                        </div>
                    </div>

                    <!-- Report Header -->
                    <div class="invoice-header">
                        <div>
                            <div class="invoice-title">${title}</div>
                            <div class="invoice-details">
                                <div><strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Report Content -->
                    <div class="report-content">
                        ${this.formatReportData(reportData)}
                    </div>

                    <!-- Footer -->
                    <div class="footer">
                        <div>تم إنشاء هذا التقرير بواسطة نظام الماهر للصيانة والخدمات</div>
                        <div>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Format report data
     */
    formatReportData(data) {
        if (Array.isArray(data)) {
            return `
                <table class="items-table">
                    <thead>
                        <tr>
                            ${Object.keys(data[0] || {}).map(key => `<th>${key}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                ${Object.values(row).map(value => `<td>${value}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }
        
        return `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    }

    /**
     * Open print window
     */
    openPrintWindow(content, title = 'طباعة') {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(content);
        printWindow.document.close();
        
        printWindow.onload = () => {
            printWindow.focus();
            printWindow.print();
        };
    }

    /**
     * Get status text in Arabic
     */
    getStatusText(status) {
        const statusMap = {
            'pending': 'معلقة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية',
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'accepted': 'مقبولة',
            'rejected': 'مرفوضة',
            'expired': 'منتهية الصلاحية'
        };
        
        return statusMap[status] || status;
    }

    /**
     * Print current page
     */
    printCurrentPage() {
        window.print();
    }
}

// Export for use in other modules
window.PrintManager = PrintManager;
