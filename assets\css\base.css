/* ===== BASE STYLES & RESET ===== */

/* Modern CSS Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  direction: rtl;
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-colors);
}

a:hover, a:focus {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== FORM ELEMENTS ===== */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input, textarea, select {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-input);
  color: var(--text-primary);
  transition: var(--transition-colors);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

input::placeholder, textarea::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

/* ===== BUTTON STYLES ===== */
button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
  font-size: inherit;
  transition: var(--transition-all);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* ===== LISTS ===== */
ul, ol {
  padding-right: var(--space-lg);
}

li {
  margin-bottom: var(--space-xs);
}

/* Remove default list styling for navigation */
.nav-list, .breadcrumb-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* ===== TABLES ===== */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-lg);
}

th, td {
  padding: var(--space-sm) var(--space-md);
  text-align: right;
  border-bottom: 1px solid var(--border-light);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
}

tr:hover {
  background-color: var(--bg-hover);
}

/* ===== IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* ===== SCROLLBARS ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--border-medium) var(--bg-tertiary);
}

/* ===== SELECTION ===== */
::selection {
  background-color: var(--color-primary-light);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--color-primary-light);
  color: var(--text-primary);
}

/* ===== FOCUS STYLES ===== */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  max-width: 300px;
}

.company-logo h1 {
  font-size: var(--font-size-2xl);
  color: var(--color-primary);
  margin-bottom: var(--space-lg);
  font-weight: var(--font-weight-bold);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: var(--space-lg) auto;
}

.loading-screen p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* Hide loading screen when app is ready */
body:not([data-loading="true"]) .loading-screen {
  opacity: 0;
  pointer-events: none;
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Text alignment utilities */
.text-right { text-align: right; }
.text-left { text-align: left; }
.text-center { text-align: center; }

/* Font weight utilities */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Color utilities */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

/* Background utilities */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-card { background-color: var(--bg-card); }

/* Spacing utilities */
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

/* Flexbox utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-1 { gap: var(--space-xs); }
.gap-2 { gap: var(--space-sm); }
.gap-4 { gap: var(--space-md); }
.gap-6 { gap: var(--space-lg); }

/* Border utilities */
.border { border: 1px solid var(--border-light); }
.border-t { border-top: 1px solid var(--border-light); }
.border-r { border-right: 1px solid var(--border-light); }
.border-b { border-bottom: 1px solid var(--border-light); }
.border-l { border-left: 1px solid var(--border-light); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Neumorphic utilities */
.neumorphic { box-shadow: var(--shadow-neumorphic-outset); }
.neumorphic-inset { box-shadow: var(--shadow-neumorphic-inset); }
.neumorphic-pressed { box-shadow: var(--shadow-neumorphic-pressed); }

/* Animation utilities */
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }
.animate-bounce { animation: bounce 1s infinite; }

/* Transition utilities */
.transition { transition: var(--transition-normal); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }
