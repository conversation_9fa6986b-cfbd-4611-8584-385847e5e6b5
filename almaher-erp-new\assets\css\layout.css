/**
 * Layout Styles - نظام الماهر للصيانة والخدمات
 * أنماط التخطيط والهيكل العام
 */

/* ========== شاشة التحميل ========== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  color: var(--white);
}

.company-logo h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-2);
  color: var(--white);
}

.company-logo p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  color: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
  margin-bottom: var(--spacing-6);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.9);
}

/* ========== التطبيق الرئيسي ========== */
.app {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  height: 100vh;
  overflow: hidden;
}

/* ========== الرأس ========== */
.header {
  grid-area: header;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-6);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.menu-toggle:hover {
  background-color: var(--bg-secondary);
}

.company-info {
  flex: 1;
  margin-right: var(--spacing-4);
}

.company-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: 0;
}

.company-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

/* ========== الشريط الجانبي ========== */
.sidebar {
  grid-area: sidebar;
  background: var(--bg-card);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: var(--neumorphic-shadow-inset-light), var(--neumorphic-shadow-inset-dark);
  transition: transform var(--transition-normal);
}

.sidebar-nav {
  padding: var(--spacing-4);
}

/* ========== المحتوى الرئيسي ========== */
.main-content {
  grid-area: main;
  background: var(--bg-secondary);
  overflow-y: auto;
  overflow-x: hidden;
}

.page-container {
  padding: var(--spacing-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

/* ========== التنقل ========== */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--spacing-2);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  background: var(--bg-card);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
}

.nav-link:hover {
  color: var(--primary-color);
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.nav-link.active {
  color: var(--primary-color);
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--neumorphic-shadow-pressed);
}

.nav-icon {
  font-size: var(--font-size-lg);
  margin-left: var(--spacing-3);
  width: 24px;
  text-align: center;
}

.nav-text {
  flex: 1;
  font-weight: var(--font-weight-medium);
}

.nav-arrow {
  font-size: var(--font-size-sm);
  transition: transform var(--transition-fast);
}

.nav-item.has-children .nav-arrow {
  transform: rotate(0deg);
}

.nav-item.has-children.expanded .nav-arrow {
  transform: rotate(-90deg);
}

.nav-submenu {
  list-style: none;
  padding: 0;
  margin: var(--spacing-2) 0 0 var(--spacing-6);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.nav-item.expanded .nav-submenu {
  max-height: 500px;
}

.nav-submenu .nav-link {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

/* ========== منتقي الثيم ========== */
.theme-selector {
  position: relative;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
  background-color: var(--bg-secondary);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-2);
  min-width: 150px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.theme-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.theme-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.theme-option:hover {
  background-color: var(--bg-secondary);
}

.theme-preview {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  margin-left: var(--spacing-2);
  border: 2px solid var(--border-color);
}

.theme-preview.light {
  background: linear-gradient(45deg, #ffffff, #f5f5f5);
}

.theme-preview.dark {
  background: linear-gradient(45deg, #212121, #424242);
}

.theme-preview.blue {
  background: linear-gradient(45deg, #2196F3, #1976D2);
}

.theme-preview.green {
  background: linear-gradient(45deg, #4CAF50, #388E3C);
}

/* ========== الإشعارات ========== */
.notifications {
  position: relative;
}

.notification-toggle {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  position: relative;
}

.notification-toggle:hover {
  background-color: var(--bg-secondary);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--error-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
  transform: translate(50%, -50%);
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.notification-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
}

.clear-all {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: var(--spacing-1);
}

.notification-list {
  padding: var(--spacing-2);
}

.no-notifications {
  text-align: center;
  color: var(--text-tertiary);
  padding: var(--spacing-8);
}

/* ========== الاستجابة ========== */
@media (max-width: 768px) {
  .app {
    grid-template-areas: 
      "header"
      "main";
    grid-template-columns: 1fr;
    grid-template-rows: var(--header-height) 1fr;
  }
  
  .menu-toggle {
    display: block;
  }
  
  .sidebar {
    position: fixed;
    top: var(--header-height);
    right: 0;
    height: calc(100vh - var(--header-height));
    width: var(--sidebar-width);
    z-index: var(--z-modal);
    transform: translateX(100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .company-info {
    text-align: center;
  }
  
  .company-name {
    font-size: var(--font-size-lg);
  }
  
  .company-subtitle {
    display: none;
  }
  
  .page-container {
    padding: var(--spacing-4);
  }
}
