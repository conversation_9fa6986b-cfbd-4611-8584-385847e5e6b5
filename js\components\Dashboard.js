/**
 * Dashboard Component - Main Dashboard View
 * Displays key metrics, recent activities, and quick actions
 */

class Dashboard {
    constructor(params = {}) {
        this.params = params;
        this.container = null;
        this.data = {
            metrics: {},
            recentActivities: [],
            quickActions: []
        };
    }

    /**
     * Render the dashboard
     */
    async render() {
        this.container = document.createElement('div');
        this.container.className = 'dashboard-container';
        
        // Load dashboard data
        await this.loadData();
        
        this.container.innerHTML = `
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1 class="dashboard-title">مرحباً بك في نظام الماهر للصيانة والخدمات</h1>
                    <p class="dashboard-subtitle">نظرة عامة على أداء عملك اليوم</p>
                </div>
                <div class="dashboard-actions">
                    <button class="btn btn-primary" id="new-invoice-btn">
                        <span>📄</span>
                        فاتورة جديدة
                    </button>
                    <button class="btn btn-secondary" id="new-work-order-btn">
                        <span>🔧</span>
                        أمر عمل جديد
                    </button>
                </div>
            </div>

            <div class="dashboard-content">
                <!-- Metrics Cards -->
                <div class="metrics-grid">
                    ${this.renderMetricsCards()}
                </div>

                <!-- Main Content Grid -->
                <div class="dashboard-grid">
                    <!-- Recent Activities -->
                    <div class="dashboard-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">النشاطات الأخيرة</h3>
                                <button class="btn btn-sm btn-secondary" id="view-all-activities">
                                    عرض الكل
                                </button>
                            </div>
                            <div class="card-body">
                                ${this.renderRecentActivities()}
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="dashboard-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">إحصائيات سريعة</h3>
                            </div>
                            <div class="card-body">
                                ${this.renderQuickStats()}
                            </div>
                        </div>
                    </div>

                    <!-- Pending Tasks -->
                    <div class="dashboard-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">المهام المعلقة</h3>
                            </div>
                            <div class="card-body">
                                ${this.renderPendingTasks()}
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">إجراءات سريعة</h3>
                            </div>
                            <div class="card-body">
                                ${this.renderQuickActions()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Set up event listeners
        this.setupEventListeners();

        return this.container;
    }

    /**
     * Load dashboard data
     */
    async loadData() {
        try {
            // Get data from DataManager
            const dataManager = window.dataManager;
            
            // Load metrics data
            const customers = await dataManager.query('customers');
            const products = await dataManager.query('products');
            const invoices = await dataManager.query('invoices');
            const workOrders = await dataManager.query('workOrders');

            this.data.metrics = {
                totalCustomers: customers.length,
                totalProducts: products.length,
                totalInvoices: invoices.length,
                pendingWorkOrders: workOrders.filter(wo => wo.status === 'pending').length,
                monthlyRevenue: this.calculateMonthlyRevenue(invoices),
                completedWorkOrders: workOrders.filter(wo => wo.status === 'completed').length
            };

            // Load recent activities (mock data for now)
            this.data.recentActivities = [
                {
                    id: 1,
                    type: 'invoice',
                    title: 'فاتورة جديدة #INV-001',
                    description: 'تم إنشاء فاتورة للعميل أحمد محمد',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    icon: '📄'
                },
                {
                    id: 2,
                    type: 'work-order',
                    title: 'أمر عمل مكتمل #WO-005',
                    description: 'تم إكمال صيانة جهاز التكييف',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    icon: '✅'
                },
                {
                    id: 3,
                    type: 'customer',
                    title: 'عميل جديد',
                    description: 'تم إضافة العميل سارة أحمد',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
                    icon: '👤'
                }
            ];

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            window.uiManager?.showToast('خطأ في تحميل بيانات لوحة التحكم', 'error');
        }
    }

    /**
     * Calculate monthly revenue
     */
    calculateMonthlyRevenue(invoices) {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        return invoices
            .filter(invoice => {
                const invoiceDate = new Date(invoice.createdAt);
                return invoiceDate.getMonth() === currentMonth && 
                       invoiceDate.getFullYear() === currentYear;
            })
            .reduce((total, invoice) => total + (invoice.total || 0), 0);
    }

    /**
     * Render metrics cards
     */
    renderMetricsCards() {
        const metrics = [
            {
                title: 'إجمالي العملاء',
                value: this.data.metrics.totalCustomers || 0,
                icon: '👥',
                color: 'primary',
                change: '+5%'
            },
            {
                title: 'الإيرادات الشهرية',
                value: `${this.data.metrics.monthlyRevenue || 0} ر.س`,
                icon: '💰',
                color: 'success',
                change: '+12%'
            },
            {
                title: 'أوامر العمل المعلقة',
                value: this.data.metrics.pendingWorkOrders || 0,
                icon: '⏳',
                color: 'warning',
                change: '-3%'
            },
            {
                title: 'أوامر العمل المكتملة',
                value: this.data.metrics.completedWorkOrders || 0,
                icon: '✅',
                color: 'info',
                change: '+8%'
            }
        ];

        return metrics.map(metric => `
            <div class="metric-card card neumorphic">
                <div class="metric-content">
                    <div class="metric-icon metric-icon-${metric.color}">
                        ${metric.icon}
                    </div>
                    <div class="metric-details">
                        <h3 class="metric-value">${metric.value}</h3>
                        <p class="metric-title">${metric.title}</p>
                        <span class="metric-change metric-change-positive">
                            ${metric.change}
                        </span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Render recent activities
     */
    renderRecentActivities() {
        if (this.data.recentActivities.length === 0) {
            return '<p class="text-muted text-center">لا توجد نشاطات حديثة</p>';
        }

        return `
            <div class="activities-list">
                ${this.data.recentActivities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">${activity.icon}</div>
                        <div class="activity-content">
                            <h4 class="activity-title">${activity.title}</h4>
                            <p class="activity-description">${activity.description}</p>
                            <span class="activity-time">${this.formatTimeAgo(activity.timestamp)}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Render quick stats
     */
    renderQuickStats() {
        return `
            <div class="quick-stats">
                <div class="stat-item">
                    <span class="stat-label">إجمالي المنتجات</span>
                    <span class="stat-value">${this.data.metrics.totalProducts || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">إجمالي الفواتير</span>
                    <span class="stat-value">${this.data.metrics.totalInvoices || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">متوسط قيمة الفاتورة</span>
                    <span class="stat-value">${this.calculateAverageInvoice()} ر.س</span>
                </div>
            </div>
        `;
    }

    /**
     * Render pending tasks
     */
    renderPendingTasks() {
        const tasks = [
            { title: 'مراجعة فاتورة #INV-003', priority: 'high' },
            { title: 'متابعة أمر العمل #WO-007', priority: 'medium' },
            { title: 'تحديث بيانات العميل', priority: 'low' }
        ];

        return `
            <div class="tasks-list">
                ${tasks.map(task => `
                    <div class="task-item">
                        <div class="task-priority task-priority-${task.priority}"></div>
                        <span class="task-title">${task.title}</span>
                        <button class="btn btn-sm btn-secondary task-action">
                            إنجاز
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Render quick actions
     */
    renderQuickActions() {
        const actions = [
            { title: 'إضافة عميل جديد', icon: '👤', action: 'add-customer' },
            { title: 'إضافة منتج جديد', icon: '📦', action: 'add-product' },
            { title: 'عرض التقارير', icon: '📊', action: 'view-reports' },
            { title: 'إعدادات النظام', icon: '⚙️', action: 'system-settings' }
        ];

        return `
            <div class="quick-actions-grid">
                ${actions.map(action => `
                    <button class="quick-action-btn neumorphic" data-action="${action.action}">
                        <span class="quick-action-icon">${action.icon}</span>
                        <span class="quick-action-title">${action.title}</span>
                    </button>
                `).join('')}
            </div>
        `;
    }

    /**
     * Calculate average invoice value
     */
    calculateAverageInvoice() {
        const invoices = this.data.metrics.totalInvoices || 0;
        const revenue = this.data.metrics.monthlyRevenue || 0;
        return invoices > 0 ? Math.round(revenue / invoices) : 0;
    }

    /**
     * Format time ago
     */
    formatTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 60) {
            return `منذ ${minutes} دقيقة`;
        } else if (hours < 24) {
            return `منذ ${hours} ساعة`;
        } else {
            return `منذ ${days} يوم`;
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // New invoice button
        const newInvoiceBtn = this.container.querySelector('#new-invoice-btn');
        if (newInvoiceBtn) {
            newInvoiceBtn.addEventListener('click', () => {
                window.router?.navigate('/invoices');
            });
        }

        // New work order button
        const newWorkOrderBtn = this.container.querySelector('#new-work-order-btn');
        if (newWorkOrderBtn) {
            newWorkOrderBtn.addEventListener('click', () => {
                window.router?.navigate('/work-orders');
            });
        }

        // View all activities button
        const viewAllActivitiesBtn = this.container.querySelector('#view-all-activities');
        if (viewAllActivitiesBtn) {
            viewAllActivitiesBtn.addEventListener('click', () => {
                window.router?.navigate('/reports/activity-log');
            });
        }

        // Quick action buttons
        const quickActionBtns = this.container.querySelectorAll('.quick-action-btn');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.getAttribute('data-action');
                this.handleQuickAction(action);
            });
        });

        // Task action buttons
        const taskActionBtns = this.container.querySelectorAll('.task-action');
        taskActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.textContent = 'تم ✓';
                e.target.disabled = true;
                e.target.classList.add('btn-success');
            });
        });
    }

    /**
     * Handle quick actions
     */
    handleQuickAction(action) {
        const routes = {
            'add-customer': '/customers',
            'add-product': '/products',
            'view-reports': '/reports',
            'system-settings': '/settings'
        };

        const route = routes[action];
        if (route) {
            window.router?.navigate(route);
        } else {
            window.uiManager?.showToast('هذه الميزة قيد التطوير', 'info');
        }
    }

    /**
     * Initialize component
     */
    async init() {
        // Any initialization logic
        console.log('Dashboard component initialized');
    }

    /**
     * Cleanup component
     */
    destroy() {
        if (this.container) {
            this.container.remove();
        }
    }
}

// Export the Dashboard component
export default Dashboard;
