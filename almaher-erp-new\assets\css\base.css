/**
 * Base Styles - نظام الماهر للصيانة والخدمات
 * الأنماط الأساسية والعامة
 */

/* ========== إعادة تعيين CSS ========== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* ========== HTML و Body ========== */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ========== العناوين ========== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-2xl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-base);
}

h6 {
  font-size: var(--font-size-sm);
}

/* ========== النصوص ========== */
p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ========== القوائم ========== */
ul, ol {
  margin-bottom: var(--spacing-4);
  padding-right: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
}

/* ========== الصور ========== */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* ========== الجداول ========== */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
}

th, td {
  padding: var(--spacing-3);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: var(--font-weight-semibold);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ========== النماذج ========== */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input, textarea, select {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

input:disabled, textarea:disabled, select:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* ========== الأزرار ========== */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-md);
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
}

button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

button:disabled {
  background-color: var(--gray-400);
  color: var(--gray-600);
  cursor: not-allowed;
  transform: none;
}

/* ========== أنواع الأزرار ========== */
.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--white);
}

.btn-success:hover {
  background-color: var(--success-dark);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--white);
}

.btn-warning:hover {
  background-color: var(--warning-dark);
}

.btn-error {
  background-color: var(--error-color);
  color: var(--white);
}

.btn-error:hover {
  background-color: var(--error-dark);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

/* ========== أحجام الأزرار ========== */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

/* ========== الأدوات المساعدة ========== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* ========== التمرير المخصص ========== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* ========== التحديد ========== */
::selection {
  background-color: var(--primary-color);
  color: var(--white);
}

::-moz-selection {
  background-color: var(--primary-color);
  color: var(--white);
}

/* ========== التركيز ========== */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ========== الطباعة ========== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .no-print {
    display: none !important;
  }
}
