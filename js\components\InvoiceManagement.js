/**
 * Invoice Management Component
 * إدارة الفواتير
 */
class InvoiceManagement {
    constructor() {
        this.container = null;
        this.invoices = [];
        this.filteredInvoices = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        this.selectedStatus = 'all';
    }

    /**
     * Render the component
     */
    render(container) {
        this.container = container;
        this.loadInvoices();
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * Load invoices data
     */
    loadInvoices() {
        // Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
        this.invoices = [
            {
                id: 'INV-001',
                customerName: 'أحمد محمد',
                customerPhone: '0501234567',
                date: '2025-01-15',
                dueDate: '2025-02-15',
                total: 450.00,
                paid: 450.00,
                status: 'paid',
                items: [
                    { name: 'صيانة مكيف سبليت', quantity: 2, price: 150, total: 300 },
                    { name: 'فلتر مكيف', quantity: 6, price: 25, total: 150 }
                ]
            },
            {
                id: 'INV-002',
                customerName: 'فاطمة أحمد',
                customerPhone: '0507654321',
                date: '2025-01-14',
                dueDate: '2025-02-14',
                total: 280.00,
                paid: 100.00,
                status: 'partial',
                items: [
                    { name: 'تنظيف مكيف شباك', quantity: 2, price: 80, total: 160 },
                    { name: 'ريموت كنترول', quantity: 2, price: 45, total: 90 },
                    { name: 'فلتر مكيف', quantity: 1, price: 25, total: 25 }
                ]
            },
            {
                id: 'INV-003',
                customerName: 'محمد علي',
                customerPhone: '0551234567',
                date: '2025-01-13',
                dueDate: '2025-02-13',
                total: 200.00,
                paid: 0.00,
                status: 'pending',
                items: [
                    { name: 'تركيب مكيف جديد', quantity: 1, price: 200, total: 200 }
                ]
            },
            {
                id: 'INV-004',
                customerName: 'سارة خالد',
                customerPhone: '0561234567',
                date: '2025-01-12',
                dueDate: '2025-01-12',
                total: 175.00,
                paid: 0.00,
                status: 'overdue',
                items: [
                    { name: 'صيانة مكيف سبليت', quantity: 1, price: 150, total: 150 },
                    { name: 'فلتر مكيف', quantity: 1, price: 25, total: 25 }
                ]
            },
            {
                id: 'INV-005',
                customerName: 'عبدالله سعد',
                customerPhone: '0571234567',
                date: '2025-01-11',
                dueDate: '2025-02-11',
                total: 320.00,
                paid: 320.00,
                status: 'paid',
                items: [
                    { name: 'تنظيف مكيف شباك', quantity: 4, price: 80, total: 320 }
                ]
            }
        ];
        
        this.filteredInvoices = [...this.invoices];
    }

    /**
     * Render content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="page-content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">إدارة الفواتير</h1>
                        <p class="page-subtitle">إدارة فواتير المبيعات والخدمات</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-invoice-btn">
                            <span class="btn-icon">➕</span>
                            فاتورة جديدة
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    ${this.renderStatsCards()}
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="search-box">
                        <input type="text" id="invoice-search" placeholder="البحث في الفواتير..." value="${this.searchTerm}">
                        <span class="search-icon">🔍</span>
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab ${this.selectedStatus === 'all' ? 'active' : ''}" data-status="all">
                            الكل (${this.invoices.length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'paid' ? 'active' : ''}" data-status="paid">
                            مدفوعة (${this.invoices.filter(i => i.status === 'paid').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'pending' ? 'active' : ''}" data-status="pending">
                            معلقة (${this.invoices.filter(i => i.status === 'pending').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'partial' ? 'active' : ''}" data-status="partial">
                            مدفوعة جزئياً (${this.invoices.filter(i => i.status === 'partial').length})
                        </button>
                        <button class="filter-tab ${this.selectedStatus === 'overdue' ? 'active' : ''}" data-status="overdue">
                            متأخرة (${this.invoices.filter(i => i.status === 'overdue').length})
                        </button>
                    </div>
                </div>

                <!-- Invoices Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.renderInvoiceRows()}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                ${this.renderPagination()}
            </div>
        `;
    }

    /**
     * Render stats cards
     */
    renderStatsCards() {
        const totalInvoices = this.invoices.length;
        const totalAmount = this.invoices.reduce((sum, inv) => sum + inv.total, 0);
        const paidAmount = this.invoices.reduce((sum, inv) => sum + inv.paid, 0);
        const pendingAmount = totalAmount - paidAmount;
        const overdueInvoices = this.invoices.filter(i => i.status === 'overdue').length;

        return `
            <div class="stat-card">
                <div class="stat-icon">📄</div>
                <div class="stat-content">
                    <div class="stat-value">${totalInvoices}</div>
                    <div class="stat-label">إجمالي الفواتير</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-value">${totalAmount.toFixed(2)} ر.س</div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <div class="stat-value">${paidAmount.toFixed(2)} ر.س</div>
                    <div class="stat-label">المبلغ المحصل</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <div class="stat-value">${pendingAmount.toFixed(2)} ر.س</div>
                    <div class="stat-label">المبلغ المعلق</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚠️</div>
                <div class="stat-content">
                    <div class="stat-value">${overdueInvoices}</div>
                    <div class="stat-label">فواتير متأخرة</div>
                </div>
            </div>
        `;
    }

    /**
     * Render invoice rows
     */
    renderInvoiceRows() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedInvoices = this.filteredInvoices.slice(startIndex, endIndex);

        if (paginatedInvoices.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="empty-state">
                        <div class="empty-icon">📄</div>
                        <h3>لا توجد فواتير</h3>
                        <p>لم يتم العثور على فواتير تطابق البحث</p>
                    </td>
                </tr>
            `;
        }

        return paginatedInvoices.map(invoice => {
            const remaining = invoice.total - invoice.paid;
            const statusText = {
                'paid': 'مدفوعة',
                'pending': 'معلقة',
                'partial': 'مدفوعة جزئياً',
                'overdue': 'متأخرة'
            };

            return `
                <tr data-invoice-id="${invoice.id}">
                    <td>
                        <div class="invoice-id">
                            <strong>${invoice.id}</strong>
                        </div>
                    </td>
                    <td>
                        <div class="customer-info">
                            <div class="customer-name">${invoice.customerName}</div>
                            <div class="customer-phone">${invoice.customerPhone}</div>
                        </div>
                    </td>
                    <td>${this.formatDate(invoice.date)}</td>
                    <td>${this.formatDate(invoice.dueDate)}</td>
                    <td class="amount">${invoice.total.toFixed(2)} ر.س</td>
                    <td class="amount paid">${invoice.paid.toFixed(2)} ر.س</td>
                    <td class="amount ${remaining > 0 ? 'pending' : 'paid'}">${remaining.toFixed(2)} ر.س</td>
                    <td>
                        <span class="status-badge ${invoice.status}">${statusText[invoice.status]}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" data-action="view" data-id="${invoice.id}" title="عرض">
                                👁️
                            </button>
                            <button class="action-btn edit-btn" data-action="edit" data-id="${invoice.id}" title="تعديل">
                                ✏️
                            </button>
                            <button class="action-btn print-btn" data-action="print" data-id="${invoice.id}" title="طباعة">
                                🖨️
                            </button>
                            ${invoice.status !== 'paid' ? `
                                <button class="action-btn payment-btn" data-action="payment" data-id="${invoice.id}" title="دفع">
                                    💳
                                </button>
                            ` : ''}
                            <button class="action-btn delete-btn" data-action="delete" data-id="${invoice.id}" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const totalPages = Math.ceil(this.filteredInvoices.length / this.itemsPerPage);
        
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="pagination-btn active">${i}</button>`;
            } else {
                paginationHTML += `<button class="pagination-btn" data-page="${i}">${i}</button>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="pagination-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }
        
        paginationHTML += '</div>';
        return paginationHTML;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Search functionality
        const searchInput = this.container.querySelector('#invoice-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.filterInvoices();
            });
        }

        // Status filters
        const filterTabs = this.container.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.selectedStatus = e.target.dataset.status;
                this.filterInvoices();
            });
        });

        // Invoice actions
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            const invoiceId = e.target.dataset.id;

            switch (action) {
                case 'view':
                    this.viewInvoice(invoiceId);
                    break;
                case 'edit':
                    this.editInvoice(invoiceId);
                    break;
                case 'print':
                    this.printInvoice(invoiceId);
                    break;
                case 'payment':
                    this.recordPayment(invoiceId);
                    break;
                case 'delete':
                    this.deleteInvoice(invoiceId);
                    break;
            }
        });

        // Add invoice button
        const addBtn = this.container.querySelector('#add-invoice-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addInvoice());
        }

        // Pagination
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('pagination-btn') && e.target.dataset.page) {
                this.currentPage = parseInt(e.target.dataset.page);
                this.renderContent();
                this.attachEventListeners();
            }
        });
    }

    /**
     * Filter invoices
     */
    filterInvoices() {
        this.filteredInvoices = this.invoices.filter(invoice => {
            const matchesSearch = invoice.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                invoice.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                invoice.customerPhone.includes(this.searchTerm);
            const matchesStatus = this.selectedStatus === 'all' || invoice.status === this.selectedStatus;
            
            return matchesSearch && matchesStatus;
        });

        this.currentPage = 1;
        this.renderContent();
        this.attachEventListeners();
    }

    /**
     * View invoice details
     */
    viewInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const itemsHTML = invoice.items.map(item => `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.price.toFixed(2)} ر.س</td>
                <td>${item.total.toFixed(2)} ر.س</td>
            </tr>
        `).join('');

        window.uiManager?.showModal({
            title: `تفاصيل الفاتورة ${invoice.id}`,
            content: `
                <div class="invoice-details">
                    <div class="invoice-header">
                        <div class="customer-section">
                            <h4>بيانات العميل</h4>
                            <p><strong>الاسم:</strong> ${invoice.customerName}</p>
                            <p><strong>الهاتف:</strong> ${invoice.customerPhone}</p>
                        </div>
                        <div class="invoice-info">
                            <h4>بيانات الفاتورة</h4>
                            <p><strong>التاريخ:</strong> ${this.formatDate(invoice.date)}</p>
                            <p><strong>تاريخ الاستحقاق:</strong> ${this.formatDate(invoice.dueDate)}</p>
                            <p><strong>الحالة:</strong> <span class="status-badge ${invoice.status}">${this.getStatusText(invoice.status)}</span></p>
                        </div>
                    </div>
                    
                    <div class="invoice-items">
                        <h4>تفاصيل الأصناف</h4>
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itemsHTML}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="invoice-totals">
                        <div class="total-row">
                            <span>الإجمالي:</span>
                            <span>${invoice.total.toFixed(2)} ر.س</span>
                        </div>
                        <div class="total-row">
                            <span>المدفوع:</span>
                            <span>${invoice.paid.toFixed(2)} ر.س</span>
                        </div>
                        <div class="total-row final">
                            <span>المتبقي:</span>
                            <span>${(invoice.total - invoice.paid).toFixed(2)} ر.س</span>
                        </div>
                    </div>
                </div>
            `,
            size: 'large',
            actions: [
                {
                    text: 'إغلاق',
                    class: 'btn-secondary',
                    action: 'close'
                },
                {
                    text: 'طباعة',
                    class: 'btn-primary',
                    action: 'print',
                    handler: () => this.printInvoice(invoiceId)
                }
            ]
        });
    }

    /**
     * Add new invoice
     */
    addInvoice() {
        window.uiManager?.showToast('سيتم إضافة صفحة إنشاء الفواتير قريباً', 'info');
    }

    /**
     * Edit invoice
     */
    editInvoice(invoiceId) {
        window.uiManager?.showToast('سيتم إضافة صفحة تعديل الفواتير قريباً', 'info');
    }

    /**
     * Print invoice
     */
    printInvoice(invoiceId) {
        window.uiManager?.showToast('سيتم إضافة وظيفة الطباعة قريباً', 'info');
    }

    /**
     * Record payment
     */
    recordPayment(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const remaining = invoice.total - invoice.paid;

        window.uiManager?.showModal({
            title: 'تسجيل دفعة',
            content: `
                <div class="payment-form">
                    <div class="invoice-summary">
                        <h4>ملخص الفاتورة ${invoice.id}</h4>
                        <p><strong>العميل:</strong> ${invoice.customerName}</p>
                        <p><strong>إجمالي الفاتورة:</strong> ${invoice.total.toFixed(2)} ر.س</p>
                        <p><strong>المدفوع سابقاً:</strong> ${invoice.paid.toFixed(2)} ر.س</p>
                        <p><strong>المتبقي:</strong> ${remaining.toFixed(2)} ر.س</p>
                    </div>
                    
                    <form class="payment-details">
                        <div class="form-group">
                            <label for="payment-amount">مبلغ الدفعة (ر.س) *</label>
                            <input type="number" id="payment-amount" name="amount" max="${remaining}" step="0.01" value="${remaining}" required>
                        </div>
                        <div class="form-group">
                            <label for="payment-method">طريقة الدفع *</label>
                            <select id="payment-method" name="method" required>
                                <option value="cash">نقداً</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="payment-date">تاريخ الدفع *</label>
                            <input type="date" id="payment-date" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>
                        <div class="form-group">
                            <label for="payment-notes">ملاحظات</label>
                            <textarea id="payment-notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
            `,
            size: 'medium',
            actions: [
                {
                    text: 'إلغاء',
                    class: 'btn-secondary',
                    action: 'cancel'
                },
                {
                    text: 'تسجيل الدفعة',
                    class: 'btn-primary',
                    action: 'save',
                    handler: () => this.savePayment(invoiceId)
                }
            ]
        });
    }

    /**
     * Save payment
     */
    savePayment(invoiceId) {
        const form = document.querySelector('.payment-details');
        const formData = new FormData(form);
        const amount = parseFloat(formData.get('amount'));
        
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        invoice.paid += amount;
        
        // Update status based on payment
        if (invoice.paid >= invoice.total) {
            invoice.status = 'paid';
        } else if (invoice.paid > 0) {
            invoice.status = 'partial';
        }

        this.filterInvoices();
        window.uiManager?.showToast('تم تسجيل الدفعة بنجاح', 'success');
    }

    /**
     * Delete invoice
     */
    deleteInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        window.uiManager?.showConfirmation({
            title: 'حذف الفاتورة',
            message: `هل أنت متأكد من حذف الفاتورة "${invoice.id}"؟`,
            confirmText: 'حذف',
            confirmClass: 'btn-danger',
            onConfirm: () => {
                this.invoices = this.invoices.filter(i => i.id !== invoiceId);
                this.filterInvoices();
                window.uiManager?.showToast('تم حذف الفاتورة بنجاح', 'success');
            }
        });
    }

    /**
     * Format date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    /**
     * Get status text
     */
    getStatusText(status) {
        const statusText = {
            'paid': 'مدفوعة',
            'pending': 'معلقة',
            'partial': 'مدفوعة جزئياً',
            'overdue': 'متأخرة'
        };
        return statusText[status] || status;
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.InvoiceManagement = InvoiceManagement;
console.log('📄 InvoiceManagement class loaded and made globally available');

// Export for use in other modules
export default InvoiceManagement;
