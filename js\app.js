/**
 * Main Application Entry Point
 * Initializes all core systems and manages application lifecycle
 */

import { EventBus } from './core/EventBus.js';
import { DataManager } from './core/DataManager.js';
import { Router } from './core/Router.js';
import { ThemeManager } from './core/ThemeManager.js';
import { UIManager } from './core/UIManager.js';

class AlmaherERP {
    constructor() {
        this.eventBus = new EventBus();
        this.dataManager = null;
        this.router = null;
        this.themeManager = null;
        this.uiManager = null;
        this.initialized = false;
        this.version = '1.0.0';
        
        // Company information
        this.companyInfo = {
            name: 'الماهر للصيانة والخدمات',
            nameEn: 'Almaher Maintenance & Services',
            address: 'فرع الشرائع، 24263، حي الخضراء، مكة المكرمة',
            website: 'almaher-est.com',
            phone: '0509888210',
            taxId: '311055560500003',
            logo: 'assets/images/logo.png'
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing Almaher ERP System...');
            
            // Show loading screen
            this.showLoadingScreen();
            
            // Initialize core systems in order
            await this.initializeDataManager();
            await this.initializeThemeManager();
            await this.initializeUIManager();
            await this.initializeRouter();
            
            // Set up global event listeners
            this.setupGlobalEventListeners();
            
            // Set up error handling
            this.setupErrorHandling();
            
            // Initialize sample data if needed
            await this.initializeSampleData();
            
            // Mark as initialized
            this.initialized = true;
            
            // Hide loading screen and show app
            this.hideLoadingScreen();
            
            console.log('✅ Almaher ERP System initialized successfully');
            
            // Emit initialization complete event
            this.eventBus.emit('app:initialized', {
                version: this.version,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('❌ Failed to initialize Almaher ERP System:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialize Data Manager
     */
    async initializeDataManager() {
        console.log('📊 Initializing Data Manager...');
        
        this.dataManager = new DataManager();
        
        // Set up data event listeners
        this.dataManager.on('data:error', (data) => {
            console.error('Data operation error:', data);
            this.uiManager?.showToast('حدث خطأ في العملية', 'error');
        });
        
        this.dataManager.on('data:created', (data) => {
            console.log('Data created:', data);
        });
        
        this.dataManager.on('data:updated', (data) => {
            console.log('Data updated:', data);
        });
        
        this.dataManager.on('data:deleted', (data) => {
            console.log('Data deleted:', data);
        });
        
        // Make data manager globally available
        window.dataManager = this.dataManager;
    }

    /**
     * Initialize Theme Manager
     */
    async initializeThemeManager() {
        console.log('🎨 Initializing Theme Manager...');
        
        this.themeManager = new ThemeManager();
        
        // Set up theme event listeners
        this.themeManager.on('theme:changed', (data) => {
            console.log('Theme changed:', data);
            this.eventBus.emit('app:theme-changed', data);
        });
        
        // Make theme manager globally available
        window.themeManager = this.themeManager;
    }

    /**
     * Initialize UI Manager
     */
    async initializeUIManager() {
        console.log('🖼️ Initializing UI Manager...');
        
        this.uiManager = new UIManager();
        
        // Set up UI event listeners
        this.uiManager.on('ui:modal-opened', (data) => {
            console.log('Modal opened:', data);
        });
        
        this.uiManager.on('ui:modal-closed', (data) => {
            console.log('Modal closed:', data);
        });
        
        // Make UI manager globally available
        window.uiManager = this.uiManager;
    }

    /**
     * Initialize Router
     */
    async initializeRouter() {
        console.log('🧭 Initializing Router...');
        
        this.router = new Router();
        
        // Set up router event listeners
        this.router.on('route:change:start', (data) => {
            console.log('Route change started:', data);
            this.uiManager?.showPageLoader();
        });
        
        this.router.on('route:change:complete', (data) => {
            console.log('Route change completed:', data);
            this.uiManager?.hidePageLoader();
            this.updateActiveNavigation(data.path);
        });
        
        this.router.on('route:error', (data) => {
            console.error('Route error:', data);
            this.uiManager?.hidePageLoader();
            this.uiManager?.showToast('خطأ في تحميل الصفحة', 'error');
        });
        
        // Set up navigation menu
        this.setupNavigationMenu();

        // Set up sidebar scroll indicators
        this.setupSidebarScrollIndicators();

        // Make router globally available
        window.router = this.router;
    }

    /**
     * Set up navigation menu
     */
    setupNavigationMenu() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;
        
        const sidebarContent = sidebar.querySelector('.sidebar-content');
        if (!sidebarContent) return;
        
        const navigationMenu = this.router.getNavigationMenu();
        const menuHTML = this.generateMenuHTML(navigationMenu.main);
        
        sidebarContent.innerHTML = menuHTML;
        
        // Set up menu interactions
        this.setupMenuInteractions();
    }

    /**
     * Generate HTML for navigation menu
     */
    generateMenuHTML(menuItems) {
        const generateItem = (item) => {
            const hasChildren = item.children && item.children.length > 0;
            const isActive = this.router.getCurrentRoute()?.path === item.path;
            
            let html = `
                <li class="nav-item ${hasChildren ? 'has-children' : ''} ${isActive ? 'active' : ''}">
                    <a href="${item.path || '#'}" class="nav-link" data-path="${item.path || ''}" data-id="${item.id}">
                        <span class="nav-icon">${item.icon}</span>
                        <span class="nav-text">${item.title}</span>
                        ${hasChildren ? '<span class="nav-arrow">◀</span>' : ''}
                    </a>
            `;
            
            if (hasChildren) {
                html += '<ul class="nav-submenu">';
                item.children.forEach(child => {
                    html += generateItem(child);
                });
                html += '</ul>';
            }
            
            html += '</li>';
            return html;
        };
        
        let menuHTML = '<ul class="nav-menu">';
        menuItems.forEach(item => {
            menuHTML += generateItem(item);
        });
        menuHTML += '</ul>';
        
        return menuHTML;
    }

    /**
     * Set up menu interactions
     */
    setupMenuInteractions() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;
        
        // Handle menu item clicks
        sidebar.addEventListener('click', (e) => {
            const navLink = e.target.closest('.nav-link');
            if (!navLink) return;
            
            e.preventDefault();
            
            const path = navLink.getAttribute('data-path');
            const navItem = navLink.closest('.nav-item');
            
            // Handle submenu toggle
            if (navItem.classList.contains('has-children')) {
                navItem.classList.toggle('expanded');
                return;
            }
            
            // Navigate to page
            if (path) {
                this.router.navigate(path);
                
                // Close sidebar on mobile
                if (window.innerWidth <= 768) {
                    this.uiManager?.closeSidebar();
                }
            }
        });
        
        // Set up menu toggle button
        const menuToggle = document.getElementById('menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', () => {
                this.uiManager?.toggleSidebar();
            });
        }
    }

    /**
     * Update active navigation item
     */
    updateActiveNavigation(currentPath) {
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            const navItem = link.closest('.nav-item');
            const linkPath = link.getAttribute('data-path');

            if (linkPath === currentPath) {
                navItem.classList.add('active');

                // Expand parent menus
                let parent = navItem.closest('.nav-submenu');
                while (parent) {
                    const parentItem = parent.closest('.nav-item');
                    if (parentItem) {
                        parentItem.classList.add('expanded');
                    }
                    parent = parentItem?.closest('.nav-submenu');
                }

                // Scroll active item into view
                navItem.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'nearest'
                });
            } else {
                navItem.classList.remove('active');
            }
        });
    }

    /**
     * Set up sidebar scroll indicators
     */
    setupSidebarScrollIndicators() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        const updateScrollIndicators = () => {
            const { scrollTop, scrollHeight, clientHeight } = sidebar;
            const isAtTop = scrollTop <= 5;
            const isAtBottom = scrollTop >= scrollHeight - clientHeight - 5;

            sidebar.classList.toggle('at-top', isAtTop);
            sidebar.classList.toggle('at-bottom', isAtBottom);
        };

        // Initial check
        updateScrollIndicators();

        // Listen for scroll events
        sidebar.addEventListener('scroll', updateScrollIndicators);

        // Listen for resize events
        window.addEventListener('resize', updateScrollIndicators);
    }

    /**
     * Set up global event listeners
     */
    setupGlobalEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
        
        // Handle online/offline status
        window.addEventListener('online', () => {
            this.uiManager?.showToast('تم استعادة الاتصال بالإنترنت', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.uiManager?.showToast('انقطع الاتصال بالإنترنت - العمل في وضع عدم الاتصال', 'warning');
        });
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // Handle before unload
        window.addEventListener('beforeunload', (e) => {
            // Check for unsaved changes
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            }
        });
    }

    /**
     * Set up error handling
     */
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.uiManager?.showToast('حدث خطأ غير متوقع', 'error');
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.uiManager?.showToast('حدث خطأ في العملية', 'error');
        });
    }

    /**
     * Initialize sample data if needed
     */
    async initializeSampleData() {
        try {
            // Check if we have any existing data
            const customers = await this.dataManager.query('customers');
            
            if (customers.length === 0) {
                console.log('📝 Initializing sample data...');

                // Create sample customers
                const sampleCustomers = [
                    {
                        name: 'أحمد محمد العلي',
                        type: 'individual',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        city: 'الرياض',
                        district: 'الملز',
                        address: 'شارع الملك فهد، حي الملز',
                        status: 'active',
                        creditLimit: 5000,
                        notes: 'عميل مميز، يفضل التواصل عبر الواتساب'
                    },
                    {
                        name: 'فاطمة سعد الغامدي',
                        type: 'individual',
                        phone: '0509876543',
                        phone2: '0112345678',
                        email: '<EMAIL>',
                        city: 'جدة',
                        district: 'الروضة',
                        address: 'طريق الملك عبدالعزيز، حي الروضة',
                        status: 'active',
                        creditLimit: 3000
                    },
                    {
                        name: 'شركة التقنية المتقدمة',
                        type: 'company',
                        company: 'شركة التقنية المتقدمة للحلول الرقمية',
                        taxId: '300123456700003',
                        phone: '0114567890',
                        email: '<EMAIL>',
                        website: 'https://advanced-tech.com',
                        city: 'الرياض',
                        district: 'العليا',
                        address: 'برج الفيصلية، الدور 15',
                        status: 'active',
                        creditLimit: 50000,
                        notes: 'شركة كبيرة، تحتاج فواتير ضريبية'
                    },
                    {
                        name: 'خالد عبدالله النجار',
                        type: 'vip',
                        phone: '0505555555',
                        email: '<EMAIL>',
                        city: 'مكة المكرمة',
                        district: 'العزيزية',
                        address: 'شارع إبراهيم الخليل، العزيزية',
                        status: 'active',
                        creditLimit: 10000,
                        notes: 'عميل VIP، أولوية في الخدمة'
                    },
                    {
                        name: 'مؤسسة البناء الحديث',
                        type: 'company',
                        company: 'مؤسسة البناء الحديث للمقاولات',
                        taxId: '300987654300003',
                        phone: '0126789012',
                        email: '<EMAIL>',
                        city: 'الدمام',
                        district: 'الفيصلية',
                        address: 'شارع الأمير محمد بن فهد',
                        status: 'active',
                        creditLimit: 100000
                    }
                ];

                for (const customer of sampleCustomers) {
                    await this.dataManager.create('customers', customer);
                }

                // Create sample product
                await this.dataManager.create('products', {
                    name: 'خدمة صيانة',
                    sku: 'SRV001',
                    price: 100,
                    type: 'service',
                    category: 'maintenance'
                });

                console.log('✅ Sample data initialized');
            }
        } catch (error) {
            console.warn('Failed to initialize sample data:', error);
        }
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        // Close sidebar on mobile when resizing to desktop
        if (window.innerWidth > 768) {
            this.uiManager?.openSidebar();
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.uiManager?.openSearch();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            this.uiManager?.closeTopModal();
        }
        
        // Ctrl/Cmd + S for save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.eventBus.emit('app:save-requested');
        }
    }

    /**
     * Check for unsaved changes
     */
    hasUnsavedChanges() {
        // This would be implemented based on form states
        return false;
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
        
        document.body.setAttribute('data-loading', 'true');
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen && app) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                app.style.display = 'block';
                
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    document.body.removeAttribute('data-loading');
                }, 300);
            }, 500); // Minimum loading time for better UX
        }
    }

    /**
     * Handle initialization error
     */
    handleInitializationError(error) {
        console.error('Initialization error:', error);
        
        // Show error message
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="loading-content">
                    <div class="company-logo">
                        <h1>الماهر للصيانة والخدمات</h1>
                    </div>
                    <div class="error-message">
                        <h2>خطأ في تحميل النظام</h2>
                        <p>عذراً، حدث خطأ أثناء تحميل النظام. يرجى إعادة تحميل الصفحة.</p>
                        <button onclick="location.reload()" class="btn btn-primary">إعادة تحميل</button>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Get company information
     */
    getCompanyInfo() {
        return this.companyInfo;
    }

    /**
     * Get application version
     */
    getVersion() {
        return this.version;
    }

    /**
     * Check if application is initialized
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * Event subscription methods
     */
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }

    off(event, callback) {
        return this.eventBus.off(event, callback);
    }

    emit(event, data) {
        return this.eventBus.emit(event, data);
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AlmaherERP();
});

// Export for module usage
export default AlmaherERP;
