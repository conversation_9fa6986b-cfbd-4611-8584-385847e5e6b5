/**
 * Contact Management Component
 * إدارة جهات الاتصال العامة
 */
export default class ContactManagement {
    constructor() {
        console.log('📞 ContactManagement constructor called');
        this.container = null;
        this.contacts = [];
        this.filteredContacts = [];
        this.currentFilter = 'all';
        this.currentSort = 'name';
        this.searchTerm = '';
        
        // أنواع جهات الاتصال
        this.contactTypes = [
            'شركة',
            'مؤسسة',
            'فرد',
            'مكتب',
            'وكالة',
            'مقاول',
            'استشاري',
            'أخرى'
        ];
        
        // فئات جهات الاتصال
        this.categories = [
            'عميل محتمل',
            'شريك تجاري',
            'مورد خدمات',
            'جهة حكومية',
            'مؤسسة مالية',
            'شركة تأمين',
            'وسائل إعلام',
            'أخرى'
        ];
        
        // حالات جهات الاتصال
        this.statuses = {
            'active': { name: 'نشط', color: '#28a745', icon: '✅' },
            'inactive': { name: 'غير نشط', color: '#6c757d', icon: '⏸️' },
            'potential': { name: 'محتمل', color: '#ffc107', icon: '🎯' }
        };
        
        this.loadContacts();
    }

    /**
     * Load contacts from storage
     */
    loadContacts() {
        const saved = localStorage.getItem('almaher_contacts');
        if (saved) {
            this.contacts = JSON.parse(saved);
        } else {
            this.createSampleData();
        }
        this.filteredContacts = [...this.contacts];
    }

    /**
     * Save contacts to storage
     */
    saveContacts() {
        localStorage.setItem('almaher_contacts', JSON.stringify(this.contacts));
    }

    /**
     * Create sample contacts
     */
    createSampleData() {
        const sampleContacts = [
            {
                id: 'CON-001',
                name: 'شركة الرياض للتطوير العقاري',
                contactPerson: 'سعد بن عبدالله الراشد',
                position: 'مدير المشاريع',
                phone: '0112345678',
                mobile: '0501234567',
                email: '<EMAIL>',
                website: 'www.riyadh-dev.com',
                address: 'طريق الملك فهد، الرياض',
                contactType: 'شركة',
                category: 'عميل محتمل',
                status: 'potential',
                notes: 'شركة تطوير عقاري كبيرة، مهتمة بخدمات الصيانة للمشاريع الجديدة',
                createdDate: '2024-01-01',
                lastContactDate: '2024-01-10'
            },
            {
                id: 'CON-002',
                name: 'مكتب الهندسة المتقدمة',
                contactPerson: 'م. فاطمة أحمد الزهراني',
                position: 'مهندسة معمارية',
                phone: '0126789012',
                mobile: '0559876543',
                email: '<EMAIL>',
                website: 'www.advanced-eng.com',
                address: 'حي الحمراء، جدة',
                contactType: 'مكتب',
                category: 'شريك تجاري',
                status: 'active',
                notes: 'مكتب هندسي متخصص في التصميم والإشراف',
                createdDate: '2024-01-05',
                lastContactDate: '2024-01-12'
            },
            {
                id: 'CON-003',
                name: 'شركة التأمين الوطنية',
                contactPerson: 'خالد محمد العتيبي',
                position: 'مدير المطالبات',
                phone: '0138901234',
                mobile: '0551234567',
                email: '<EMAIL>',
                website: 'www.national-insurance.com',
                address: 'شارع الأمير سلطان، الدمام',
                contactType: 'شركة',
                category: 'شركة تأمين',
                status: 'active',
                notes: 'شركة تأمين معتمدة للمطالبات',
                createdDate: '2023-12-15',
                lastContactDate: '2024-01-08'
            },
            {
                id: 'CON-004',
                name: 'بلدية الرياض',
                contactPerson: 'أحمد سالم القحطاني',
                position: 'مدير الصيانة',
                phone: '0114567890',
                mobile: '0507654321',
                email: '<EMAIL>',
                website: 'www.riyadh.gov.sa',
                address: 'مركز الملك عبدالعزيز التاريخي، الرياض',
                contactType: 'مؤسسة',
                category: 'جهة حكومية',
                status: 'active',
                notes: 'جهة حكومية للمشاريع العامة',
                createdDate: '2023-11-20',
                lastContactDate: '2024-01-05'
            }
        ];
        
        this.contacts = sampleContacts;
        this.saveContacts();
    }

    /**
     * Render the contact management interface
     */
    render(container) {
        console.log('📞 ContactManagement render called', container);
        this.container = container;
        this.renderContent();
        this.attachEventListeners();
        this.applyFilters();
        console.log('✅ ContactManagement render completed');
    }

    /**
     * Render the main content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="contact-management">
                <!-- Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <span class="page-icon">📞</span>
                            إدارة جهات الاتصال العامة
                        </h1>
                        <p class="page-description">إدارة وتتبع جميع جهات الاتصال والشركاء</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-contact-btn">
                            <span class="btn-icon">➕</span>
                            جهة اتصال جديدة
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card active">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-active">0</div>
                            <div class="stat-label">جهات نشطة</div>
                        </div>
                    </div>
                    <div class="stat-card potential">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-potential">0</div>
                            <div class="stat-label">عملاء محتملين</div>
                        </div>
                    </div>
                    <div class="stat-card companies">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-companies">0</div>
                            <div class="stat-label">شركات</div>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label">إجمالي جهات الاتصال</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث في جهات الاتصال..." class="search-input">
                            <span class="search-icon">🔍</span>
                        </div>
                        
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="status-filter" class="filter-select">
                                <option value="all">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="potential">محتمل</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>النوع:</label>
                            <select id="type-filter" class="filter-select">
                                <option value="all">جميع الأنواع</option>
                                ${this.contactTypes.map(type => 
                                    `<option value="${type}">${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>الفئة:</label>
                            <select id="category-filter" class="filter-select">
                                <option value="all">جميع الفئات</option>
                                ${this.categories.map(category => 
                                    `<option value="${category}">${category}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>الترتيب:</label>
                            <select id="sort-select" class="filter-select">
                                <option value="name">حسب الاسم</option>
                                <option value="lastContact">آخر اتصال</option>
                                <option value="created">تاريخ الإضافة</option>
                                <option value="category">حسب الفئة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Contacts List -->
                <div class="contacts-section">
                    <div class="section-header">
                        <h2>قائمة جهات الاتصال</h2>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="cards">
                                <span>📋</span> بطاقات
                            </button>
                            <button class="view-btn" data-view="table">
                                <span>📊</span> جدول
                            </button>
                        </div>
                    </div>
                    
                    <div id="contacts-container" class="contacts-container">
                        <!-- Contacts will be rendered here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update statistics
     */
    updateStatistics() {
        const stats = {
            active: this.contacts.filter(c => c.status === 'active').length,
            potential: this.contacts.filter(c => c.status === 'potential').length,
            companies: this.contacts.filter(c => c.contactType === 'شركة').length,
            total: this.contacts.length
        };

        document.getElementById('stat-active').textContent = stats.active;
        document.getElementById('stat-potential').textContent = stats.potential;
        document.getElementById('stat-companies').textContent = stats.companies;
        document.getElementById('stat-total').textContent = stats.total;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Add new contact button
        const addBtn = this.container.querySelector('#add-contact-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddContactModal());
        }

        // Search input
        const searchInput = this.container.querySelector('#search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.applyFilters();
            });
        }

        // Filter selects
        const statusFilter = this.container.querySelector('#status-filter');
        const typeFilter = this.container.querySelector('#type-filter');
        const categoryFilter = this.container.querySelector('#category-filter');
        const sortSelect = this.container.querySelector('#sort-select');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.typeFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.categoryFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.applyFilters();
            });
        }

        // View toggle buttons
        const viewBtns = this.container.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentView = btn.dataset.view;
                this.renderContacts();
            });
        });
    }

    /**
     * Apply filters and search
     */
    applyFilters() {
        let filtered = [...this.contacts];

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(c => c.status === this.currentFilter);
        }

        // Apply type filter
        if (this.typeFilter && this.typeFilter !== 'all') {
            filtered = filtered.filter(c => c.contactType === this.typeFilter);
        }

        // Apply category filter
        if (this.categoryFilter && this.categoryFilter !== 'all') {
            filtered = filtered.filter(c => c.category === this.categoryFilter);
        }

        // Apply search
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(c => 
                c.name.toLowerCase().includes(term) ||
                c.contactPerson.toLowerCase().includes(term) ||
                c.phone.includes(term) ||
                c.email.toLowerCase().includes(term) ||
                c.category.toLowerCase().includes(term)
            );
        }

        // Apply sorting
        this.sortContacts(filtered);

        this.filteredContacts = filtered;
        this.renderContacts();
        this.updateStatistics();
    }

    /**
     * Sort contacts
     */
    sortContacts(contacts) {
        switch (this.currentSort) {
            case 'name':
                contacts.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
                break;
            case 'lastContact':
                contacts.sort((a, b) => new Date(b.lastContactDate) - new Date(a.lastContactDate));
                break;
            case 'created':
                contacts.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));
                break;
            case 'category':
                contacts.sort((a, b) => a.category.localeCompare(b.category, 'ar'));
                break;
        }
    }

    /**
     * Render contacts list
     */
    renderContacts() {
        const container = this.container.querySelector('#contacts-container');
        if (!container) return;

        if (this.filteredContacts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📞</div>
                    <h3>لا توجد جهات اتصال</h3>
                    <p>لم يتم العثور على جهات اتصال تطابق المعايير المحددة</p>
                    <button class="btn btn-primary" onclick="document.getElementById('add-contact-btn').click()">
                        إضافة جهة اتصال جديدة
                    </button>
                </div>
            `;
            return;
        }

        const currentView = this.container.querySelector('.view-btn.active')?.dataset.view || 'cards';
        
        if (currentView === 'cards') {
            this.renderCardsView(container);
        } else {
            this.renderTableView(container);
        }
    }

    /**
     * Render cards view
     */
    renderCardsView(container) {
        container.innerHTML = `
            <div class="contacts-grid">
                ${this.filteredContacts.map(contact => this.renderContactCard(contact)).join('')}
            </div>
        `;
    }

    /**
     * Render single contact card
     */
    renderContactCard(contact) {
        const status = this.statuses[contact.status];
        
        return `
            <div class="contact-card" data-id="${contact.id}">
                <div class="card-header">
                    <div class="card-id">${contact.id}</div>
                    <div class="card-badges">
                        <span class="status-badge ${contact.status}" title="${status.name}">
                            ${status.icon} ${status.name}
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <h3 class="contact-name">${contact.name}</h3>
                    <div class="contact-type">${contact.contactType} - ${contact.category}</div>
                    <div class="contact-person">👤 ${contact.contactPerson}</div>
                    <div class="contact-position">${contact.position}</div>
                    
                    <div class="card-details">
                        <div class="detail-item">
                            <span class="detail-icon">📞</span>
                            <span class="detail-text">${contact.phone}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📱</span>
                            <span class="detail-text">${contact.mobile}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📧</span>
                            <span class="detail-text">${contact.email}</span>
                        </div>
                        ${contact.website ? `
                        <div class="detail-item">
                            <span class="detail-icon">🌐</span>
                            <span class="detail-text">${contact.website}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="contactManagement.viewContact('${contact.id}')">
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="contactManagement.editContact('${contact.id}')">
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="contactManagement.contactPerson('${contact.id}')">
                        اتصال
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Render table view
     */
    renderTableView(container) {
        container.innerHTML = `
            <div class="contacts-table-container">
                <table class="contacts-table">
                    <thead>
                        <tr>
                            <th>رقم جهة الاتصال</th>
                            <th>الاسم</th>
                            <th>جهة الاتصال</th>
                            <th>النوع</th>
                            <th>الفئة</th>
                            <th>الحالة</th>
                            <th>آخر اتصال</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.filteredContacts.map(contact => this.renderContactRow(contact)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Render single contact table row
     */
    renderContactRow(contact) {
        const status = this.statuses[contact.status];
        
        return `
            <tr class="contact-row" data-id="${contact.id}">
                <td class="contact-id">${contact.id}</td>
                <td class="contact-info">
                    <div class="contact-name">${contact.name}</div>
                    <div class="contact-phone">${contact.phone}</div>
                </td>
                <td class="contact-person">
                    <div class="person-name">${contact.contactPerson}</div>
                    <div class="person-position">${contact.position}</div>
                </td>
                <td class="contact-type">${contact.contactType}</td>
                <td class="category">${contact.category}</td>
                <td class="status">
                    <span class="status-badge ${contact.status}">
                        ${status.icon} ${status.name}
                    </span>
                </td>
                <td class="last-contact">${this.formatDate(contact.lastContactDate)}</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="contactManagement.viewContact('${contact.id}')" title="عرض">
                            👁️
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="contactManagement.editContact('${contact.id}')" title="تعديل">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="contactManagement.contactPerson('${contact.id}')" title="اتصال">
                            📞
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Show add contact modal
     */
    showAddContactModal() {
        window.uiManager?.showToast('سيتم إضافة نموذج إنشاء جهة اتصال جديدة قريباً', 'info');
    }

    /**
     * View contact details
     */
    viewContact(contactId) {
        window.uiManager?.showToast(`عرض تفاصيل جهة الاتصال: ${contactId}`, 'info');
    }

    /**
     * Edit contact
     */
    editContact(contactId) {
        window.uiManager?.showToast(`تعديل جهة الاتصال: ${contactId}`, 'info');
    }

    /**
     * Contact person
     */
    contactPerson(contactId) {
        const contact = this.contacts.find(c => c.id === contactId);
        if (contact) {
            // Update last contact date
            contact.lastContactDate = new Date().toISOString().split('T')[0];
            this.saveContacts();
            this.applyFilters();
            window.uiManager?.showToast(`تم تسجيل اتصال مع: ${contact.contactPerson}`, 'success');
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.ContactManagement = ContactManagement;
console.log('📞 ContactManagement class loaded and made globally available');
