/**
 * Supplier Management Component
 * إدارة الموردين
 */
export default class SupplierManagement {
    constructor() {
        console.log('🏢 SupplierManagement constructor called');
        this.container = null;
        this.suppliers = [];
        this.filteredSuppliers = [];
        this.currentFilter = 'all';
        this.currentSort = 'name';
        this.searchTerm = '';
        
        // أنواع الموردين
        this.supplierTypes = [
            'مورد مواد',
            'مورد خدمات',
            'مورد معدات',
            'مورد قطع غيار',
            'مقاول',
            'مورد عام'
        ];
        
        // حالات الموردين
        this.statuses = {
            'active': { name: 'نشط', color: '#28a745', icon: '✅' },
            'inactive': { name: 'غير نشط', color: '#6c757d', icon: '⏸️' },
            'blocked': { name: 'محظور', color: '#dc3545', icon: '🚫' }
        };
        
        this.loadSuppliers();
    }

    /**
     * Load suppliers from storage
     */
    loadSuppliers() {
        const saved = localStorage.getItem('almaher_suppliers');
        if (saved) {
            this.suppliers = JSON.parse(saved);
        } else {
            this.createSampleData();
        }
        this.filteredSuppliers = [...this.suppliers];
    }

    /**
     * Save suppliers to storage
     */
    saveSuppliers() {
        localStorage.setItem('almaher_suppliers', JSON.stringify(this.suppliers));
    }

    /**
     * Create sample suppliers
     */
    createSampleData() {
        const sampleSuppliers = [
            {
                id: 'SUP-001',
                name: 'شركة المواد الكهربائية المتقدمة',
                contactPerson: 'أحمد محمد السالم',
                phone: '0112345678',
                email: '<EMAIL>',
                address: 'طريق الملك فهد، الرياض',
                supplierType: 'مورد مواد',
                status: 'active',
                taxNumber: '300123456789003',
                paymentTerms: '30 يوم',
                creditLimit: 50000,
                currentBalance: 15000,
                notes: 'مورد موثوق للمواد الكهربائية',
                createdDate: '2024-01-01',
                lastOrderDate: '2024-01-10'
            },
            {
                id: 'SUP-002',
                name: 'مؤسسة قطع غيار المكيفات',
                contactPerson: 'فاطمة عبدالله',
                phone: '0509876543',
                email: '<EMAIL>',
                address: 'حي الصناعية، جدة',
                supplierType: 'مورد قطع غيار',
                status: 'active',
                taxNumber: '300987654321003',
                paymentTerms: '15 يوم',
                creditLimit: 30000,
                currentBalance: 8500,
                notes: 'متخصص في قطع غيار المكيفات',
                createdDate: '2024-01-05',
                lastOrderDate: '2024-01-12'
            },
            {
                id: 'SUP-003',
                name: 'شركة الخدمات التقنية',
                contactPerson: 'محمد علي الغامدي',
                phone: '0551234567',
                email: '<EMAIL>',
                address: 'شارع التحلية، الخبر',
                supplierType: 'مورد خدمات',
                status: 'inactive',
                taxNumber: '300456789123003',
                paymentTerms: '45 يوم',
                creditLimit: 20000,
                currentBalance: 0,
                notes: 'خدمات تقنية متنوعة',
                createdDate: '2023-12-15',
                lastOrderDate: '2023-12-28'
            }
        ];
        
        this.suppliers = sampleSuppliers;
        this.saveSuppliers();
    }

    /**
     * Render the supplier management interface
     */
    render(container) {
        console.log('🏢 SupplierManagement render called', container);
        this.container = container;
        this.renderContent();
        this.attachEventListeners();
        this.applyFilters();
        console.log('✅ SupplierManagement render completed');
    }

    /**
     * Render the main content
     */
    renderContent() {
        this.container.innerHTML = `
            <div class="supplier-management">
                <!-- Header -->
                <div class="page-header">
                    <div class="page-title">
                        <h1>
                            <span class="page-icon">🏢</span>
                            إدارة الموردين
                        </h1>
                        <p class="page-description">إدارة وتتبع جميع الموردين والمقاولين</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="add-supplier-btn">
                            <span class="btn-icon">➕</span>
                            مورد جديد
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card active">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-active">0</div>
                            <div class="stat-label">موردين نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card inactive">
                        <div class="stat-icon">⏸️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-inactive">0</div>
                            <div class="stat-label">غير نشطين</div>
                        </div>
                    </div>
                    <div class="stat-card balance">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-balance">0</div>
                            <div class="stat-label">إجمالي الأرصدة</div>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label">إجمالي الموردين</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث في الموردين..." class="search-input">
                            <span class="search-icon">🔍</span>
                        </div>
                        
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="status-filter" class="filter-select">
                                <option value="all">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="blocked">محظور</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>نوع المورد:</label>
                            <select id="type-filter" class="filter-select">
                                <option value="all">جميع الأنواع</option>
                                ${this.supplierTypes.map(type => 
                                    `<option value="${type}">${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>الترتيب:</label>
                            <select id="sort-select" class="filter-select">
                                <option value="name">حسب الاسم</option>
                                <option value="balance">حسب الرصيد</option>
                                <option value="lastOrder">آخر طلب</option>
                                <option value="created">تاريخ الإضافة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Suppliers List -->
                <div class="suppliers-section">
                    <div class="section-header">
                        <h2>قائمة الموردين</h2>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="cards">
                                <span>📋</span> بطاقات
                            </button>
                            <button class="view-btn" data-view="table">
                                <span>📊</span> جدول
                            </button>
                        </div>
                    </div>
                    
                    <div id="suppliers-container" class="suppliers-container">
                        <!-- Suppliers will be rendered here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update statistics
     */
    updateStatistics() {
        const stats = {
            active: this.suppliers.filter(s => s.status === 'active').length,
            inactive: this.suppliers.filter(s => s.status === 'inactive').length,
            balance: this.suppliers.reduce((sum, s) => sum + (s.currentBalance || 0), 0),
            total: this.suppliers.length
        };

        document.getElementById('stat-active').textContent = stats.active;
        document.getElementById('stat-inactive').textContent = stats.inactive;
        document.getElementById('stat-balance').textContent = `${stats.balance.toLocaleString()} ر.س`;
        document.getElementById('stat-total').textContent = stats.total;
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Add new supplier button
        const addBtn = this.container.querySelector('#add-supplier-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddSupplierModal());
        }

        // Search input
        const searchInput = this.container.querySelector('#search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.applyFilters();
            });
        }

        // Filter selects
        const statusFilter = this.container.querySelector('#status-filter');
        const typeFilter = this.container.querySelector('#type-filter');
        const sortSelect = this.container.querySelector('#sort-select');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.typeFilter = e.target.value;
                this.applyFilters();
            });
        }

        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.applyFilters();
            });
        }

        // View toggle buttons
        const viewBtns = this.container.querySelectorAll('.view-btn');
        viewBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentView = btn.dataset.view;
                this.renderSuppliers();
            });
        });
    }

    /**
     * Apply filters and search
     */
    applyFilters() {
        let filtered = [...this.suppliers];

        // Apply status filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(s => s.status === this.currentFilter);
        }

        // Apply type filter
        if (this.typeFilter && this.typeFilter !== 'all') {
            filtered = filtered.filter(s => s.supplierType === this.typeFilter);
        }

        // Apply search
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(s => 
                s.name.toLowerCase().includes(term) ||
                s.contactPerson.toLowerCase().includes(term) ||
                s.phone.includes(term) ||
                s.email.toLowerCase().includes(term)
            );
        }

        // Apply sorting
        this.sortSuppliers(filtered);

        this.filteredSuppliers = filtered;
        this.renderSuppliers();
        this.updateStatistics();
    }

    /**
     * Sort suppliers
     */
    sortSuppliers(suppliers) {
        switch (this.currentSort) {
            case 'name':
                suppliers.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
                break;
            case 'balance':
                suppliers.sort((a, b) => (b.currentBalance || 0) - (a.currentBalance || 0));
                break;
            case 'lastOrder':
                suppliers.sort((a, b) => new Date(b.lastOrderDate) - new Date(a.lastOrderDate));
                break;
            case 'created':
                suppliers.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));
                break;
        }
    }

    /**
     * Render suppliers list
     */
    renderSuppliers() {
        const container = this.container.querySelector('#suppliers-container');
        if (!container) return;

        if (this.filteredSuppliers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🏢</div>
                    <h3>لا يوجد موردين</h3>
                    <p>لم يتم العثور على موردين تطابق المعايير المحددة</p>
                    <button class="btn btn-primary" onclick="document.getElementById('add-supplier-btn').click()">
                        إضافة مورد جديد
                    </button>
                </div>
            `;
            return;
        }

        const currentView = this.container.querySelector('.view-btn.active')?.dataset.view || 'cards';
        
        if (currentView === 'cards') {
            this.renderCardsView(container);
        } else {
            this.renderTableView(container);
        }
    }

    /**
     * Render cards view
     */
    renderCardsView(container) {
        container.innerHTML = `
            <div class="suppliers-grid">
                ${this.filteredSuppliers.map(supplier => this.renderSupplierCard(supplier)).join('')}
            </div>
        `;
    }

    /**
     * Render single supplier card
     */
    renderSupplierCard(supplier) {
        const status = this.statuses[supplier.status];
        
        return `
            <div class="supplier-card" data-id="${supplier.id}">
                <div class="card-header">
                    <div class="card-id">${supplier.id}</div>
                    <div class="card-badges">
                        <span class="status-badge ${supplier.status}" title="${status.name}">
                            ${status.icon} ${status.name}
                        </span>
                    </div>
                </div>
                
                <div class="card-content">
                    <h3 class="supplier-name">${supplier.name}</h3>
                    <div class="supplier-type">${supplier.supplierType}</div>
                    <div class="contact-person">👤 ${supplier.contactPerson}</div>
                    
                    <div class="card-details">
                        <div class="detail-item">
                            <span class="detail-icon">📞</span>
                            <span class="detail-text">${supplier.phone}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📧</span>
                            <span class="detail-text">${supplier.email}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">💰</span>
                            <span class="detail-text">الرصيد: ${supplier.currentBalance.toLocaleString()} ر.س</span>
                        </div>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="supplierManagement.viewSupplier('${supplier.id}')">
                        عرض
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="supplierManagement.editSupplier('${supplier.id}')">
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="supplierManagement.toggleStatus('${supplier.id}')">
                        ${supplier.status === 'active' ? 'إيقاف' : 'تفعيل'}
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Render table view
     */
    renderTableView(container) {
        container.innerHTML = `
            <div class="suppliers-table-container">
                <table class="suppliers-table">
                    <thead>
                        <tr>
                            <th>رقم المورد</th>
                            <th>اسم المورد</th>
                            <th>جهة الاتصال</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>الرصيد الحالي</th>
                            <th>آخر طلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.filteredSuppliers.map(supplier => this.renderSupplierRow(supplier)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Render single supplier table row
     */
    renderSupplierRow(supplier) {
        const status = this.statuses[supplier.status];
        
        return `
            <tr class="supplier-row" data-id="${supplier.id}">
                <td class="supplier-id">${supplier.id}</td>
                <td class="supplier-info">
                    <div class="supplier-name">${supplier.name}</div>
                    <div class="supplier-phone">${supplier.phone}</div>
                </td>
                <td class="contact-person">${supplier.contactPerson}</td>
                <td class="supplier-type">${supplier.supplierType}</td>
                <td class="status">
                    <span class="status-badge ${supplier.status}">
                        ${status.icon} ${status.name}
                    </span>
                </td>
                <td class="balance">${supplier.currentBalance.toLocaleString()} ر.س</td>
                <td class="last-order">${this.formatDate(supplier.lastOrderDate)}</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="supplierManagement.viewSupplier('${supplier.id}')" title="عرض">
                            👁️
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="supplierManagement.editSupplier('${supplier.id}')" title="تعديل">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="supplierManagement.toggleStatus('${supplier.id}')" title="تغيير الحالة">
                            🔄
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Show add supplier modal
     */
    showAddSupplierModal() {
        window.uiManager?.showToast('سيتم إضافة نموذج إنشاء مورد جديد قريباً', 'info');
    }

    /**
     * View supplier details
     */
    viewSupplier(supplierId) {
        window.uiManager?.showToast(`عرض تفاصيل المورد: ${supplierId}`, 'info');
    }

    /**
     * Edit supplier
     */
    editSupplier(supplierId) {
        window.uiManager?.showToast(`تعديل المورد: ${supplierId}`, 'info');
    }

    /**
     * Toggle supplier status
     */
    toggleStatus(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (supplier) {
            supplier.status = supplier.status === 'active' ? 'inactive' : 'active';
            this.saveSuppliers();
            this.applyFilters();
            window.uiManager?.showToast(`تم تغيير حالة المورد إلى: ${this.statuses[supplier.status].name}`, 'success');
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Cleanup
     */
    destroy() {
        // Cleanup event listeners if needed
    }
}

// Make available globally for router
window.SupplierManagement = SupplierManagement;
console.log('🏢 SupplierManagement class loaded and made globally available');
