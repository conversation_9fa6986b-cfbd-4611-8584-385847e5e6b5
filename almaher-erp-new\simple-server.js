// خادم HTTP بسيط لنظام الماهر للصيانة والخدمات
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;

const server = http.createServer((req, res) => {
    console.log(`طلب: ${req.method} ${req.url}`);
    
    let filePath = req.url === '/' ? '/index.html' : req.url;
    filePath = path.join(__dirname, filePath);
    
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html; charset=utf-8',
        '.js': 'application/javascript; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml'
    };
    
    const contentType = mimeTypes[ext] || 'text/plain';
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            console.log(`خطأ في قراءة الملف: ${filePath}`);
            // إذا لم يوجد الملف، أرسل index.html (للـ SPA routing)
            if (err.code === 'ENOENT' && ext !== '.html') {
                fs.readFile(path.join(__dirname, 'index.html'), (err2, data2) => {
                    if (err2) {
                        res.writeHead(404, { 'Content-Type': 'text/plain; charset=utf-8' });
                        res.end('الملف غير موجود');
                    } else {
                        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                        res.end(data2);
                    }
                });
            } else {
                res.writeHead(404, { 'Content-Type': 'text/plain; charset=utf-8' });
                res.end('الملف غير موجود');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        }
    });
});

server.listen(PORT, () => {
    console.log(`🚀 نظام الماهر للصيانة والخدمات يعمل على:`);
    console.log(`   http://localhost:${PORT}`);
    console.log(`📁 مجلد الملفات: ${__dirname}`);
    console.log(`⏰ الوقت: ${new Date().toLocaleString('ar-SA')}`);
    console.log(`✅ الخادم جاهز للاستخدام!`);
});

server.on('error', (err) => {
    console.error(`❌ خطأ في الخادم: ${err.message}`);
    if (err.code === 'EADDRINUSE') {
        console.log(`المنفذ ${PORT} مستخدم، جرب منفذ آخر`);
    }
});
