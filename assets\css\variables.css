/* ===== CSS VARIABLES FOR THEME SYSTEM ===== */

:root {
  /* ===== LIGHT THEME COLORS ===== */
  --color-primary: #2196F3;
  --color-primary-dark: #1976D2;
  --color-primary-light: #BBDEFB;
  --color-secondary: #FF9800;
  --color-secondary-dark: #F57C00;
  --color-secondary-light: #FFE0B2;
  
  /* Background Colors */
  --bg-primary: #F5F7FA;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #FAFBFC;
  --bg-card: #FFFFFF;
  --bg-input: #FFFFFF;
  --bg-hover: #F8F9FA;
  --bg-active: #E3F2FD;
  
  /* Text Colors */
  --text-primary: #2C3E50;
  --text-secondary: #5A6C7D;
  --text-tertiary: #8B9DC3;
  --text-muted: #A0AEC0;
  --text-inverse: #FFFFFF;
  
  /* Border Colors */
  --border-light: #E2E8F0;
  --border-medium: #CBD5E0;
  --border-dark: #A0AEC0;
  --border-focus: var(--color-primary);
  
  /* Status Colors */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-info: #3B82F6;
  --color-info-light: #DBEAFE;
  
  /* ===== NEUMORPHIC SHADOWS ===== */
  /* Light Theme Shadows */
  --shadow-neumorphic-inset: inset 2px 2px 5px #D1D9E6, inset -2px -2px 5px #FFFFFF;
  --shadow-neumorphic-outset: 2px 2px 5px #D1D9E6, -2px -2px 5px #FFFFFF;
  --shadow-neumorphic-pressed: inset 1px 1px 3px #D1D9E6, inset -1px -1px 3px #FFFFFF;
  --shadow-neumorphic-hover: 3px 3px 8px #D1D9E6, -3px -3px 8px #FFFFFF;
  --shadow-neumorphic-card: 4px 4px 10px #D1D9E6, -4px -4px 10px #FFFFFF;
  --shadow-neumorphic-modal: 6px 6px 15px #D1D9E6, -6px -6px 15px #FFFFFF;
  
  /* Standard Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* ===== SPACING SYSTEM ===== */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* ===== TYPOGRAPHY ===== */
  --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'Courier New', Courier, monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* ===== LAYOUT DIMENSIONS ===== */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;
  
  /* ===== TRANSITIONS ===== */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* ===== Z-INDEX LAYERS ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== DARK THEME OVERRIDES ===== */
[data-theme="dark"], .theme-dark {
  /* Background Colors */
  --bg-primary: #1A202C;
  --bg-secondary: #2D3748;
  --bg-tertiary: #4A5568;
  --bg-card: #2D3748;
  --bg-input: #4A5568;
  --bg-hover: #4A5568;
  --bg-active: #2B6CB0;
  
  /* Text Colors */
  --text-primary: #F7FAFC;
  --text-secondary: #E2E8F0;
  --text-tertiary: #CBD5E0;
  --text-muted: #A0AEC0;
  --text-inverse: #1A202C;
  
  /* Border Colors */
  --border-light: #4A5568;
  --border-medium: #718096;
  --border-dark: #A0AEC0;
  
  /* Neumorphic Shadows for Dark Theme */
  --shadow-neumorphic-inset: inset 2px 2px 5px #0F1419, inset -2px -2px 5px #252F3F;
  --shadow-neumorphic-outset: 2px 2px 5px #0F1419, -2px -2px 5px #252F3F;
  --shadow-neumorphic-pressed: inset 1px 1px 3px #0F1419, inset -1px -1px 3px #252F3F;
  --shadow-neumorphic-hover: 3px 3px 8px #0F1419, -3px -3px 8px #252F3F;
  --shadow-neumorphic-card: 4px 4px 10px #0F1419, -4px -4px 10px #252F3F;
  --shadow-neumorphic-modal: 6px 6px 15px #0F1419, -6px -6px 15px #252F3F;
  
  /* Status Colors - Adjusted for dark theme */
  --color-success-light: #065F46;
  --color-warning-light: #92400E;
  --color-error-light: #991B1B;
  --color-info-light: #1E40AF;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-30px); }
  70% { transform: translateY(-15px); }
  90% { transform: translateY(-4px); }
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.transition-all {
  transition: all var(--transition-normal);
}

.transition-colors {
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-transform {
  transition: transform var(--transition-normal);
}
