/* ===== THEME SELECTOR STYLES ===== */

.theme-selector {
  position: relative;
  display: inline-block;
}

.theme-selector-btn {
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: var(--border-radius-full);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all);
  box-shadow: var(--shadow-neumorphic-outset);
  position: relative;
}

.theme-selector-btn:hover {
  box-shadow: var(--shadow-neumorphic-hover);
  transform: translateY(-1px);
}

.theme-selector-btn:active {
  box-shadow: var(--shadow-neumorphic-pressed);
  transform: translateY(0);
}

.theme-icon {
  font-size: var(--font-size-base);
}

.theme-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  box-shadow: var(--shadow-neumorphic-modal);
  padding: var(--space-sm);
  min-width: 200px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-normal);
  direction: rtl;
  text-align: right;
}

.theme-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-sm) var(--space-md);
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-colors);
  margin-bottom: var(--space-xs);
}

.theme-option:last-child {
  margin-bottom: 0;
}

.theme-option:hover {
  background: var(--bg-hover);
}

.theme-option.active {
  background: var(--bg-active);
  color: var(--color-primary);
}

.theme-preview {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.theme-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
}

.theme-preview::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
}

/* Light Theme Preview */
.theme-preview.light {
  background: #F5F7FA;
}

.theme-preview.light::before {
  background: #2196F3;
}

.theme-preview.light::after {
  background: #FFFFFF;
}

/* Dark Theme Preview */
.theme-preview.dark {
  background: #1A202C;
}

.theme-preview.dark::before {
  background: #2196F3;
}

.theme-preview.dark::after {
  background: #2D3748;
}

/* Blue Theme Preview */
.theme-preview.blue {
  background: #EFF6FF;
}

.theme-preview.blue::before {
  background: #1E40AF;
}

.theme-preview.blue::after {
  background: #DBEAFE;
}

/* Green Theme Preview */
.theme-preview.green {
  background: #ECFDF5;
}

.theme-preview.green::before {
  background: #059669;
}

.theme-preview.green::after {
  background: #D1FAE5;
}

/* Purple Theme Preview */
.theme-preview.purple {
  background: #FAF5FF;
}

.theme-preview.purple::before {
  background: #7C3AED;
}

.theme-preview.purple::after {
  background: #EDE9FE;
}

/* Orange Theme Preview */
.theme-preview.orange {
  background: #FFF7ED;
}

.theme-preview.orange::before {
  background: #EA580C;
}

.theme-preview.orange::after {
  background: #FED7AA;
}

/* Teal Theme Preview */
.theme-preview.teal {
  background: #F0FDFA;
}

.theme-preview.teal::before {
  background: #0D9488;
}

.theme-preview.teal::after {
  background: #CCFBF1;
}

.theme-option span {
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .theme-dropdown {
    right: -50px;
    min-width: 180px;
  }
  
  .theme-option {
    padding: var(--space-md);
  }
  
  .theme-preview {
    width: 18px;
    height: 18px;
  }
  
  .theme-option span {
    font-size: 13px;
  }
}

/* Animation for theme changes */
.theme-transitioning * {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease,
              box-shadow 0.3s ease !important;
}

/* Close dropdown when clicking outside */
.theme-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Theme selector active state */
.theme-selector.active .theme-selector-btn {
  background: var(--bg-active);
  color: var(--color-primary);
  box-shadow: var(--shadow-neumorphic-pressed);
}

/* Smooth theme transition */
:root {
  transition: all 0.3s ease;
}

/* Theme change notification */
.theme-change-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: var(--shadow-neumorphic-modal);
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
  text-align: center;
  min-width: 200px;
}

.theme-change-notification.show {
  opacity: 1;
  visibility: visible;
}

.theme-change-notification .theme-icon-large {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
}

.theme-change-notification h3 {
  margin: 0 0 var(--space-xs) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.theme-change-notification p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}
