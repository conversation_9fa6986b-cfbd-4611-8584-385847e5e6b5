/**
 * Router - Single Page Application Routing System
 * Handles navigation, route matching, and page loading
 */

import { EventBus } from './EventBus.js';

class Router {
    constructor() {
        this.eventBus = new EventBus();
        this.routes = new Map();
        this.currentRoute = null;
        this.basePath = '';
        this.defaultRoute = '/';
        this.notFoundRoute = '/404';
        this.beforeRouteChange = null;
        this.afterRouteChange = null;
        
        // Navigation menu structure
        this.navigationMenu = {
            main: [
                {
                    id: 'home',
                    title: 'الرئيسية',
                    icon: '🏠',
                    path: '/',
                    component: 'Dashboard'
                },
                {
                    id: 'contacts',
                    title: 'جهات الاتصال',
                    icon: '👥',
                    children: [
                        {
                            id: 'customers',
                            title: 'العملاء',
                            icon: '👤',
                            path: '/customers',
                            component: 'CustomerManagement'
                        },
                        {
                            id: 'suppliers',
                            title: 'الموردين',
                            icon: '🏢',
                            path: '/suppliers',
                            component: 'SupplierManagement'
                        },
                        {
                            id: 'contacts-general',
                            title: 'جهات الاتصال العامة',
                            icon: '📞',
                            path: '/contacts',
                            component: 'ContactManagement'
                        }
                    ]
                },
                {
                    id: 'sales',
                    title: 'المبيعات',
                    icon: '💰',
                    children: [
                        {
                            id: 'invoices',
                            title: 'الفواتير',
                            icon: '📄',
                            path: '/invoices',
                            component: 'InvoiceManagement'
                        },
                        {
                            id: 'credit-notes',
                            title: 'إشعارات دائنة (فواتير مرتجعة)',
                            icon: '📋',
                            path: '/credit-notes',
                            component: 'CreditNoteManagement'
                        },
                        {
                            id: 'quotations',
                            title: 'عروض الأسعار',
                            icon: '📊',
                            path: '/quotations',
                            component: 'QuotationManagement'
                        },
                        {
                            id: 'purchase-requests',
                            title: 'طلبات الشراء',
                            icon: '🛒',
                            path: '/purchase-requests',
                            component: 'PurchaseRequestManagement'
                        },
                        {
                            id: 'sales-payments',
                            title: 'المدفوعات',
                            icon: '💳',
                            path: '/sales-payments',
                            component: 'PaymentManagement'
                        }
                    ]
                },
                {
                    id: 'products-services',
                    title: 'المنتجات والخدمات',
                    icon: '📦',
                    path: '/products',
                    component: 'ProductManagement'
                },
                {
                    id: 'purchases',
                    title: 'المشتريات',
                    icon: '🛍️',
                    children: [
                        {
                            id: 'purchase-invoices',
                            title: 'فواتير الشراء',
                            icon: '📑',
                            path: '/purchase-invoices',
                            component: 'PurchaseInvoiceManagement'
                        },
                        {
                            id: 'purchase-returns',
                            title: 'مرتجعات الشراء',
                            icon: '↩️',
                            path: '/purchase-returns',
                            component: 'PurchaseReturnManagement'
                        },
                        {
                            id: 'purchase-orders',
                            title: 'أوامر الشراء',
                            icon: '📋',
                            path: '/purchase-orders',
                            component: 'PurchaseOrderManagement'
                        },
                        {
                            id: 'purchase-payments',
                            title: 'مدفوعات الشراء',
                            icon: '💸',
                            path: '/purchase-payments',
                            component: 'PurchasePaymentManagement'
                        }
                    ]
                },
                {
                    id: 'inventory',
                    title: 'المخزون',
                    icon: '📋',
                    children: [
                        {
                            id: 'product-list',
                            title: 'قائمة المنتجات/الخدمات',
                            icon: '📝',
                            path: '/inventory/products',
                            component: 'InventoryProductList'
                        },
                        {
                            id: 'opening-stock',
                            title: 'الرصيد الافتتاحي',
                            icon: '📊',
                            path: '/inventory/opening-stock',
                            component: 'OpeningStock'
                        },
                        {
                            id: 'stock-transactions',
                            title: 'حركات المخزون (إصدار/إضافة)',
                            icon: '🔄',
                            path: '/inventory/transactions',
                            component: 'StockTransactions'
                        },
                        {
                            id: 'stock-transfers',
                            title: 'تحويلات المخزون',
                            icon: '🚚',
                            path: '/inventory/transfers',
                            component: 'StockTransfers'
                        },
                        {
                            id: 'stocktaking',
                            title: 'جرد المخزون',
                            icon: '📋',
                            path: '/inventory/stocktaking',
                            component: 'Stocktaking'
                        },
                        {
                            id: 'price-lists',
                            title: 'قوائم الأسعار',
                            icon: '💰',
                            path: '/inventory/price-lists',
                            component: 'PriceLists'
                        }
                    ]
                },
                {
                    id: 'finance',
                    title: 'المالية',
                    icon: '🏦',
                    children: [
                        {
                            id: 'bank-accounts',
                            title: 'الحسابات البنكية وصناديق النقد',
                            icon: '🏧',
                            path: '/finance/accounts',
                            component: 'BankAccountManagement'
                        },
                        {
                            id: 'expenses',
                            title: 'المصروفات',
                            icon: '💸',
                            path: '/finance/expenses',
                            component: 'ExpenseManagement'
                        },
                        {
                            id: 'fixed-assets',
                            title: 'الأصول الثابتة',
                            icon: '🏢',
                            path: '/finance/fixed-assets',
                            component: 'FixedAssetManagement'
                        },
                        {
                            id: 'cash-receipts',
                            title: 'سندات قبض نقدي',
                            icon: '🧾',
                            path: '/finance/cash-receipts',
                            component: 'CashReceiptManagement'
                        },
                        {
                            id: 'journal-entries',
                            title: 'دفتر اليومية العام (قيود اليومية)',
                            icon: '📚',
                            path: '/finance/journal-entries',
                            component: 'JournalEntryManagement'
                        },
                        {
                            id: 'chart-of-accounts',
                            title: 'دليل الحسابات',
                            icon: '📊',
                            path: '/finance/chart-of-accounts',
                            component: 'ChartOfAccounts'
                        }
                    ]
                },
                {
                    id: 'work-orders',
                    title: 'أوامر العمل',
                    icon: '🔧',
                    path: '/work-orders',
                    component: 'WorkOrderManagement'
                },
                {
                    id: 'reports',
                    title: 'التقارير',
                    icon: '📈',
                    children: [
                        {
                            id: 'sales-reports',
                            title: 'تقارير المبيعات',
                            icon: '📊',
                            path: '/reports/sales',
                            component: 'SalesReports'
                        },
                        {
                            id: 'purchase-reports',
                            title: 'تقارير المشتريات',
                            icon: '📋',
                            path: '/reports/purchases',
                            component: 'PurchaseReports'
                        },
                        {
                            id: 'inventory-reports',
                            title: 'تقارير المخزون',
                            icon: '📦',
                            path: '/reports/inventory',
                            component: 'InventoryReports'
                        },
                        {
                            id: 'financial-reports',
                            title: 'التقارير المالية',
                            icon: '💰',
                            path: '/reports/financial',
                            component: 'FinancialReports'
                        },
                        {
                            id: 'tax-reports',
                            title: 'التقارير الضريبية',
                            icon: '📄',
                            path: '/reports/tax',
                            component: 'TaxReports'
                        },
                        {
                            id: 'activity-log',
                            title: 'سجل النشاطات',
                            icon: '📝',
                            path: '/reports/activity-log',
                            component: 'ActivityLog'
                        }
                    ]
                },
                {
                    id: 'settings',
                    title: 'الإعدادات',
                    icon: '⚙️',
                    children: [
                        {
                            id: 'account-settings',
                            title: 'إعدادات الحساب',
                            icon: '👤',
                            path: '/settings/account',
                            component: 'AccountSettings'
                        },
                        {
                            id: 'invoice-settings',
                            title: 'إعدادات الفواتير',
                            icon: '📄',
                            path: '/settings/invoices',
                            component: 'InvoiceSettings'
                        },
                        {
                            id: 'tax-settings',
                            title: 'إعدادات الضرائب',
                            icon: '💰',
                            path: '/settings/tax',
                            component: 'TaxSettings'
                        },
                        {
                            id: 'user-management',
                            title: 'إدارة المستخدمين',
                            icon: '👥',
                            path: '/settings/users',
                            component: 'UserManagement'
                        },
                        {
                            id: 'branches',
                            title: 'الفروع',
                            icon: '🏢',
                            path: '/settings/branches',
                            component: 'BranchManagement'
                        },
                        {
                            id: 'print-templates',
                            title: 'قوالب الطباعة',
                            icon: '🖨️',
                            path: '/settings/print-templates',
                            component: 'PrintTemplates'
                        },
                        {
                            id: 'integrations',
                            title: 'التكاملات وربط API',
                            icon: '🔗',
                            path: '/settings/integrations',
                            component: 'IntegrationSettings'
                        },
                        {
                            id: 'backup-restore',
                            title: 'النسخ الاحتياطي والاستعادة',
                            icon: '💾',
                            path: '/settings/backup',
                            component: 'BackupRestore'
                        },
                        {
                            id: 'customization',
                            title: 'التخصيص',
                            icon: '🎨',
                            path: '/settings/customization',
                            component: 'CustomizationSettings'
                        }
                    ]
                }
            ]
        };
        
        this.init();
    }

    /**
     * Initialize router
     */
    init() {
        // Set up route definitions from navigation menu
        this.setupRoutesFromMenu();
        
        // Listen for browser navigation events
        window.addEventListener('popstate', (event) => {
            this.handlePopState(event);
        });
        
        // Handle initial route
        this.handleInitialRoute();
    }

    /**
     * Set up routes from navigation menu structure
     */
    setupRoutesFromMenu() {
        const addRoutes = (items) => {
            items.forEach(item => {
                if (item.path && item.component) {
                    this.addRoute(item.path, {
                        component: item.component,
                        title: item.title,
                        icon: item.icon,
                        id: item.id
                    });
                }
                
                if (item.children) {
                    addRoutes(item.children);
                }
            });
        };
        
        addRoutes(this.navigationMenu.main);
        
        // Add 404 route
        this.addRoute('/404', {
            component: 'NotFound',
            title: 'الصفحة غير موجودة',
            icon: '❌'
        });
    }

    /**
     * Add a route
     */
    addRoute(path, config) {
        this.routes.set(path, {
            path,
            ...config,
            params: this.extractParams(path)
        });
    }

    /**
     * Extract parameters from route path
     */
    extractParams(path) {
        const params = [];
        const segments = path.split('/');
        
        segments.forEach((segment, index) => {
            if (segment.startsWith(':')) {
                params.push({
                    name: segment.slice(1),
                    index
                });
            }
        });
        
        return params;
    }

    /**
     * Navigate to a route
     */
    navigate(path, options = {}) {
        const { replace = false, state = null } = options;
        
        // Normalize path
        const normalizedPath = this.normalizePath(path);
        
        // Check if route exists
        const route = this.matchRoute(normalizedPath);
        if (!route) {
            console.warn(`Router: Route not found: ${normalizedPath}`);
            this.navigate(this.notFoundRoute, { replace: true });
            return;
        }
        
        // Call before route change hook
        if (this.beforeRouteChange) {
            const shouldContinue = this.beforeRouteChange(route, this.currentRoute);
            if (shouldContinue === false) {
                return;
            }
        }
        
        // Update browser history
        if (replace) {
            window.history.replaceState(state, '', normalizedPath);
        } else {
            window.history.pushState(state, '', normalizedPath);
        }
        
        // Load the route
        this.loadRoute(normalizedPath, route);
    }

    /**
     * Match a path to a route
     */
    matchRoute(path) {
        // Try exact match first
        if (this.routes.has(path)) {
            return this.routes.get(path);
        }
        
        // Try parameter matching
        for (const [routePath, route] of this.routes) {
            if (this.matchPathWithParams(path, routePath)) {
                return {
                    ...route,
                    params: this.extractRouteParams(path, routePath)
                };
            }
        }
        
        return null;
    }

    /**
     * Match path with parameters
     */
    matchPathWithParams(path, routePath) {
        const pathSegments = path.split('/');
        const routeSegments = routePath.split('/');
        
        if (pathSegments.length !== routeSegments.length) {
            return false;
        }
        
        return routeSegments.every((segment, index) => {
            return segment.startsWith(':') || segment === pathSegments[index];
        });
    }

    /**
     * Extract route parameters from path
     */
    extractRouteParams(path, routePath) {
        const pathSegments = path.split('/');
        const routeSegments = routePath.split('/');
        const params = {};
        
        routeSegments.forEach((segment, index) => {
            if (segment.startsWith(':')) {
                const paramName = segment.slice(1);
                params[paramName] = pathSegments[index];
            }
        });
        
        return params;
    }

    /**
     * Load a route
     */
    async loadRoute(path, route) {
        try {
            // Emit route change start event
            this.eventBus.emit('route:change:start', { 
                path, 
                route, 
                previousRoute: this.currentRoute 
            });
            
            // Update current route
            const previousRoute = this.currentRoute;
            this.currentRoute = { ...route, path };
            
            // Update page title
            document.title = `${route.title} - الماهر للصيانة والخدمات`;
            
            // Update breadcrumb
            this.updateBreadcrumb(route);
            
            // Load component
            await this.loadComponent(route.component, route.params || {});
            
            // Call after route change hook
            if (this.afterRouteChange) {
                this.afterRouteChange(this.currentRoute, previousRoute);
            }
            
            // Emit route change complete event
            this.eventBus.emit('route:change:complete', { 
                path, 
                route: this.currentRoute, 
                previousRoute 
            });
            
        } catch (error) {
            console.error('Router: Failed to load route:', error);
            this.eventBus.emit('route:error', { path, route, error });
            
            // Navigate to error page
            if (path !== this.notFoundRoute) {
                this.navigate(this.notFoundRoute, { replace: true });
            }
        }
    }

    /**
     * Load component for route
     */
    async loadComponent(componentName, params = {}) {
        try {
            // Get content container
            const contentContainer = document.getElementById('page-content');
            if (!contentContainer) {
                throw new Error('Page content container not found');
            }

            // Clear existing content
            contentContainer.innerHTML = '';

            // List of available components
            const availableComponents = [
                'Dashboard',
                'CustomerManagement',
                'ProductManagement',
                'InvoiceManagement',
                'QuotationManagement',
                'PurchaseRequestManagement',
                'PaymentManagement',
                'ReportManagement',
                'SettingsManagement',
                'NotFound'
            ];

            // Check if component is available
            if (availableComponents.includes(componentName)) {
                try {
                    // Try to load the component
                    let Component;

                    console.log(`🔍 Router: Looking for component ${componentName}`);
                    console.log(`🔍 Router: window.${componentName} exists:`, !!window[componentName]);

                    if (window[componentName]) {
                        // Component is already loaded globally
                        console.log(`✅ Router: Found ${componentName} in global scope`);
                        Component = window[componentName];
                    } else {
                        // Try dynamic import
                        console.log(`📦 Router: Importing ${componentName} module`);
                        const module = await import(`../components/${componentName}.js`);
                        Component = module.default || module[componentName] || window[componentName];
                        console.log(`📦 Router: Import result:`, !!Component);
                    }

                    if (!Component) {
                        throw new Error(`Component ${componentName} not found`);
                    }

                    // Create component instance
                    const componentInstance = new Component(params);

                    // Store work order management globally for easy access
                    if (componentName === 'WorkOrderManagement') {
                        window.workOrderManagement = componentInstance;
                    }

                    // Render component
                    if (typeof componentInstance.render === 'function') {
                        await componentInstance.render(contentContainer);
                    } else if (typeof componentInstance.mount === 'function') {
                        await componentInstance.mount(contentContainer);
                    }

                    return;
                } catch (componentError) {
                    console.warn(`Failed to load component ${componentName}:`, componentError);
                    // Fall through to placeholder
                }
            }

            // For other components, try dynamic import
            try {
                const module = await import(`../components/${componentName}.js`);
                const Component = module.default || module[componentName];

                if (!Component) {
                    throw new Error(`Component ${componentName} not found`);
                }

                // Create component instance
                const componentInstance = new Component(params);

                // Render component
                if (typeof componentInstance.render === 'function') {
                    const content = await componentInstance.render();
                    if (content) {
                        contentContainer.appendChild(content);
                    }
                } else if (typeof componentInstance.mount === 'function') {
                    await componentInstance.mount(contentContainer);
                }

            } catch (importError) {
                // If component doesn't exist, show placeholder
                contentContainer.innerHTML = `
                    <div class="page-content">
                        <div class="page-header">
                            <h1 class="page-title">${componentName}</h1>
                            <p class="page-description">هذه الصفحة قيد التطوير</p>
                        </div>
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🚧</div>
                            <h2>قيد التطوير</h2>
                            <p>هذه الصفحة ستكون متاحة قريباً</p>
                        </div>
                    </div>
                `;
            }

        } catch (error) {
            console.error(`Router: Failed to load component ${componentName}:`, error);

            // Show error message
            const contentContainer = document.getElementById('page-content');
            if (contentContainer) {
                contentContainer.innerHTML = `
                    <div class="error-container">
                        <h2>خطأ في تحميل الصفحة</h2>
                        <p>عذراً، حدث خطأ أثناء تحميل هذه الصفحة.</p>
                        <button onclick="location.reload()" class="btn btn-primary">إعادة تحميل</button>
                    </div>
                `;
            }
        }
    }

    /**
     * Update breadcrumb navigation
     */
    updateBreadcrumb(route) {
        const breadcrumbContainer = document.querySelector('.breadcrumb-list');
        if (!breadcrumbContainer) return;
        
        // Find route in menu structure to build breadcrumb
        const breadcrumb = this.buildBreadcrumb(route.path);
        
        // Clear existing breadcrumb
        breadcrumbContainer.innerHTML = '';
        
        // Add breadcrumb items
        breadcrumb.forEach((item, index) => {
            const li = document.createElement('li');
            
            if (index === breadcrumb.length - 1) {
                // Current page - no link
                li.textContent = item.title;
                li.classList.add('current');
            } else {
                // Parent pages - with links
                const a = document.createElement('a');
                a.href = item.path;
                a.textContent = item.title;
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.navigate(item.path);
                });
                li.appendChild(a);
            }
            
            breadcrumbContainer.appendChild(li);
        });
    }

    /**
     * Build breadcrumb from route path
     */
    buildBreadcrumb(path) {
        const breadcrumb = [{ title: 'الرئيسية', path: '/' }];
        
        // Find the route in menu structure
        const findInMenu = (items, currentPath = []) => {
            for (const item of items) {
                const newPath = [...currentPath, item];
                
                if (item.path === path) {
                    return newPath;
                }
                
                if (item.children) {
                    const found = findInMenu(item.children, newPath);
                    if (found) return found;
                }
            }
            return null;
        };
        
        const menuPath = findInMenu(this.navigationMenu.main);
        if (menuPath) {
            // Add parent items to breadcrumb
            menuPath.forEach(item => {
                if (item.path && item.path !== '/') {
                    breadcrumb.push({
                        title: item.title,
                        path: item.path
                    });
                }
            });
        }
        
        return breadcrumb;
    }

    /**
     * Handle browser back/forward navigation
     */
    handlePopState(event) {
        const path = window.location.pathname;
        const route = this.matchRoute(path);
        
        if (route) {
            this.loadRoute(path, route);
        } else {
            this.navigate(this.notFoundRoute, { replace: true });
        }
    }

    /**
     * Handle initial route on page load
     */
    handleInitialRoute() {
        const path = window.location.pathname;

        // If path is empty or root, navigate to dashboard
        if (!path || path === '/' || path === '/index.html') {
            this.navigate('/', { replace: true });
        } else {
            this.navigate(path, { replace: true });
        }
    }

    /**
     * Normalize path
     */
    normalizePath(path) {
        // Remove trailing slash except for root
        if (path !== '/' && path.endsWith('/')) {
            path = path.slice(0, -1);
        }
        
        // Ensure path starts with /
        if (!path.startsWith('/')) {
            path = '/' + path;
        }
        
        return path;
    }

    /**
     * Get navigation menu
     */
    getNavigationMenu() {
        return this.navigationMenu;
    }

    /**
     * Get current route
     */
    getCurrentRoute() {
        return this.currentRoute;
    }

    /**
     * Set route change hooks
     */
    setBeforeRouteChange(callback) {
        this.beforeRouteChange = callback;
    }

    setAfterRouteChange(callback) {
        this.afterRouteChange = callback;
    }

    /**
     * Event subscription methods
     */
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }

    off(event, callback) {
        return this.eventBus.off(event, callback);
    }
}

// Export the Router class
export { Router };
